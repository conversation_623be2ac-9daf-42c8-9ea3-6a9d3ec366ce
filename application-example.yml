# UAMS 应用配置示例文件
# 复制此文件为 application-local.yml 并修改相应配置

# 数据库配置示例
uams:
  datasource:
    type: mysql  # 数据库类型: mysql, oracle, sqlserver
    host: localhost
    port: 3306
    serviceName: uams  # 数据库名/服务名
    username: root
    password: password
  
  # Redis配置
  redis:
    host: localhost
    port: 6379
    password: 
    database: 0
  
  # RabbitMQ配置
  rabbitmq:
    host: localhost
    port: 5672
    username: guest
    password: guest
  
  # 安全策略配置
  security-policy:
    enabled-gateway-https: false

# 服务器配置
server:
  port: 8080
  servlet:
    context-path: /uams

# 日志配置
logging:
  level:
    com.fulongtech.uams: DEBUG
    org.springframework: INFO
  file:
    name: logs/uams.log

# 开发环境配置
spring:
  profiles:
    active: local
