/*
 * Decompiled with CFR 0.152.
 */
package io.loadkit;

import io.loadkit.Filter;
import io.loadkit.Loader;
import io.loadkit.PatternLoader;
import io.loadkit.RegexFilter;
import io.loadkit.StdLoader;

public class RegexLoader
extends PatternLoader
implements Loader {
    public RegexLoader() {
        this(new StdLoader());
    }

    public RegexLoader(ClassLoader classLoader) {
        this(new StdLoader(classLoader));
    }

    public RegexLoader(Loader delegate) {
        super(delegate);
    }

    protected String path(String pattern) {
        return "";
    }

    protected boolean recursively(String pattern) {
        return true;
    }

    protected Filter filter(String pattern) {
        return new RegexFilter(pattern);
    }
}
