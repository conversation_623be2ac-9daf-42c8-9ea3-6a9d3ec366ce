/*
 * Decompiled with CFR 0.152.
 */
package io.loadkit;

import io.loadkit.Filter;
import io.loadkit.Filters;
import io.loadkit.Loader;
import io.loadkit.Res;
import io.loadkit.Resource;
import io.loadkit.ResourceLoader;
import io.loadkit.Uris;
import java.io.File;
import java.io.IOException;
import java.net.JarURLConnection;
import java.net.URL;
import java.nio.charset.Charset;
import java.util.Enumeration;
import java.util.jar.JarEntry;
import java.util.jar.JarFile;

/*
 * This class specifies class file version 49.0 but uses Java 6 signatures.  Assumed Java 6.
 */
public class JarLoader
extends ResourceLoader
implements Loader {
    private final URL context;
    private final JarFile jarFile;

    public JarLoader(File file) throws IOException {
        this(new URL("jar:" + file.toURI().toURL() + "!/"), new JarFile(file));
    }

    public JarLoader(URL jarURL) throws IOException {
        this(jarURL, ((JarURLConnection)jarURL.openConnection()).getJarFile());
    }

    public JarLoader(URL context, JarFile jarFile) {
        if (context == null) {
            throw new IllegalArgumentException("context must not be null");
        }
        if (jarFile == null) {
            throw new IllegalArgumentException("jarFile must not be null");
        }
        this.context = context;
        this.jarFile = jarFile;
    }

    @Override
    public Enumeration<Resource> load(String path, boolean recursively, Filter filter) {
        while (path.startsWith("/")) {
            path = path.substring(1);
        }
        while (path.endsWith("/")) {
            path = path.substring(0, path.length() - 1);
        }
        return new Enumerator(this.context, this.jarFile, path, recursively, filter != null ? filter : Filters.ALWAYS);
    }

    /*
     * This class specifies class file version 49.0 but uses Java 6 signatures.  Assumed Java 6.
     */
    private static class Enumerator
    extends ResourceLoader.ResourceEnumerator
    implements Enumeration<Resource> {
        private final URL context;
        private final String path;
        private final String folder;
        private final boolean recursively;
        private final Filter filter;
        private final Enumeration<JarEntry> entries;

        Enumerator(URL context, JarFile jarFile, String path, boolean recursively, Filter filter) {
            this.context = context;
            this.path = path;
            this.folder = path.endsWith("/") || path.length() == 0 ? path : path + "/";
            this.recursively = recursively;
            this.filter = filter;
            this.entries = jarFile.entries();
        }

        @Override
        public boolean hasMoreElements() {
            if (this.next != null) {
                return true;
            }
            while (this.entries.hasMoreElements()) {
                String name;
                JarEntry jarEntry = this.entries.nextElement();
                if (jarEntry.isDirectory() || !(name = jarEntry.getName()).equals(this.path) && (!this.recursively || !name.startsWith(this.folder)) && (this.recursively || !name.startsWith(this.folder) || name.indexOf(47, this.folder.length()) >= 0)) continue;
                try {
                    URL url = new URL(this.context, Uris.encodePath(name, Charset.defaultCharset()));
                    if (!this.filter.filtrate(name, url)) continue;
                    this.next = new Res(name, url);
                    return true;
                }
                catch (Exception e) {
                    throw new IllegalStateException(e);
                }
            }
            return false;
        }
    }
}
