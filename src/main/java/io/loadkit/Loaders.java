/*
 * Decompiled with CFR 0.152.
 */
package io.loadkit;

import io.loadkit.AntLoader;
import io.loadkit.FileLoader;
import io.loadkit.JarLoader;
import io.loadkit.Loader;
import io.loadkit.PkgLoader;
import io.loadkit.RegexLoader;
import io.loadkit.StdLoader;
import java.io.File;
import java.io.IOException;
import java.net.URL;
import java.util.jar.JarFile;

public abstract class Loaders {
    public static Loader std() {
        return new StdLoader();
    }

    public static Loader std(ClassLoader classLoader) {
        return new StdLoader(classLoader);
    }

    public static Loader pkg() {
        return new PkgLoader();
    }

    public static Loader pkg(ClassLoader classLoader) {
        return new PkgLoader(classLoader);
    }

    public static Loader pkg(Loader delegate) {
        return new PkgLoader(delegate);
    }

    public static Loader ant() {
        return new AntLoader();
    }

    public static Loader ant(ClassLoader classLoader) {
        return new AntLoader(classLoader);
    }

    public static Loader ant(Loader delegate) {
        return new AntLoader(delegate);
    }

    public static Loader regex() {
        return new RegexLoader();
    }

    public static Loader regex(ClassLoader classLoader) {
        return new RegexLoader(classLoader);
    }

    public static Loader regex(Loader delegate) {
        return new RegexLoader(delegate);
    }

    public static Loader file(File root) throws IOException {
        return new FileLoader(root);
    }

    public static Loader file(URL fileURL) {
        return new FileLoader(fileURL);
    }

    public static Loader file(URL context, File root) {
        return new FileLoader(context, root);
    }

    public static Loader jar(File file) throws IOException {
        return new JarLoader(file);
    }

    public static Loader jar(URL jarURL) throws IOException {
        return new JarLoader(jarURL);
    }

    public static Loader jar(URL context, JarFile jarFile) {
        return new JarLoader(context, jarFile);
    }
}
