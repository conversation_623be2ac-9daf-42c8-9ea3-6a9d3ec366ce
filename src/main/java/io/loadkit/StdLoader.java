/*
 * Decompiled with CFR 0.152.
 */
package io.loadkit;

import io.loadkit.FileLoader;
import io.loadkit.Filter;
import io.loadkit.Filters;
import io.loadkit.JarLoader;
import io.loadkit.Loader;
import io.loadkit.Resource;
import io.loadkit.ResourceLoader;
import io.loadkit.Uris;
import java.io.File;
import java.io.IOException;
import java.net.JarURLConnection;
import java.net.URL;
import java.nio.charset.Charset;
import java.util.Collections;
import java.util.Enumeration;
import java.util.LinkedHashSet;
import java.util.jar.JarFile;

/*
 * This class specifies class file version 49.0 but uses Java 6 signatures.  Assumed Java 6.
 */
public class StdLoader
extends ResourceLoader
implements Loader {
    private final ClassLoader classLoader;

    public StdLoader() {
        this(Thread.currentThread().getContextClassLoader() != null ? Thread.currentThread().getContextClassLoader() : ClassLoader.getSystemClassLoader());
    }

    public StdLoader(ClassLoader classLoader) {
        if (classLoader == null) {
            throw new IllegalArgumentException("classLoader must not be null");
        }
        this.classLoader = classLoader;
    }

    @Override
    public Enumeration<Resource> load(String path, boolean recursively, Filter filter) throws IOException {
        while (path.startsWith("/")) {
            path = path.substring(1);
        }
        while (path.endsWith("/")) {
            path = path.substring(0, path.length() - 1);
        }
        return new Enumerator(this.classLoader, path, recursively, filter != null ? filter : Filters.ALWAYS);
    }

    /*
     * This class specifies class file version 49.0 but uses Java 6 signatures.  Assumed Java 6.
     */
    private static class Enumerator
    extends ResourceLoader.ResourceEnumerator
    implements Enumeration<Resource> {
        private final String path;
        private final boolean recursively;
        private final Filter filter;
        private final Enumeration<URL> urls;
        private Enumeration<Resource> resources;

        Enumerator(ClassLoader classLoader, String path, boolean recursively, Filter filter) throws IOException {
            this.path = path;
            this.recursively = recursively;
            this.filter = filter;
            this.urls = this.load(classLoader, path);
            this.resources = Collections.enumeration(Collections.emptySet());
        }

        private Enumeration<URL> load(ClassLoader classLoader, String path) throws IOException {
            if (path.length() > 0) {
                return classLoader.getResources(path);
            }
            LinkedHashSet<URL> set = new LinkedHashSet<URL>();
            set.add(classLoader.getResource(path));
            Enumeration<URL> urls = classLoader.getResources("META-INF/");
            while (urls.hasMoreElements()) {
                String spec;
                int index;
                URL url = urls.nextElement();
                if (!url.getProtocol().equalsIgnoreCase("jar") || (index = (spec = url.toString()).lastIndexOf("!/")) < 0) continue;
                set.add(new URL(url, spec.substring(0, index + "!/".length())));
            }
            return Collections.enumeration(set);
        }

        @Override
        public boolean hasMoreElements() {
            if (this.next != null) {
                return true;
            }
            if (!this.resources.hasMoreElements() && !this.urls.hasMoreElements()) {
                return false;
            }
            if (this.resources.hasMoreElements()) {
                this.next = this.resources.nextElement();
                return true;
            }
            URL url = this.urls.nextElement();
            String protocol = url.getProtocol();
            if ("file".equalsIgnoreCase(protocol)) {
                try {
                    String uri = Uris.decode(url.getPath(), Charset.defaultCharset());
                    String root = uri.substring(0, uri.lastIndexOf(this.path));
                    URL context = new URL(url, "file:" + Uris.encodePath(root, Charset.defaultCharset()));
                    File file = new File(root);
                    this.resources = new FileLoader(context, file).load(this.path, this.recursively, this.filter);
                    return this.hasMoreElements();
                }
                catch (IOException e) {
                    throw new IllegalStateException(e);
                }
            }
            if ("jar".equalsIgnoreCase(protocol)) {
                try {
                    String uri = Uris.decode(url.getPath(), Charset.defaultCharset());
                    String root = uri.substring(0, uri.lastIndexOf(this.path));
                    URL context = new URL(url, "jar:" + Uris.encodePath(root, Charset.defaultCharset()));
                    JarURLConnection jarURLConnection = (JarURLConnection)url.openConnection();
                    JarFile jarFile = jarURLConnection.getJarFile();
                    this.resources = new JarLoader(context, jarFile).load(this.path, this.recursively, this.filter);
                    return this.hasMoreElements();
                }
                catch (IOException e) {
                    throw new IllegalStateException(e);
                }
            }
            return this.hasMoreElements();
        }
    }
}
