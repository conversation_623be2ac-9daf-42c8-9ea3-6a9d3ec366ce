/*
 * Decompiled with CFR 0.152.
 */
package io.loadkit;

import io.loadkit.Filter;
import io.loadkit.Filters;
import io.loadkit.Loader;
import io.loadkit.Resource;
import java.io.IOException;
import java.util.Enumeration;
import java.util.NoSuchElementException;

/*
 * This class specifies class file version 49.0 but uses Java 6 signatures.  Assumed Java 6.
 */
public abstract class ResourceLoader
implements Loader {
    @Override
    public Enumeration<Resource> load(String path) throws IOException {
        return this.load(path, false, Filters.ALWAYS);
    }

    @Override
    public Enumeration<Resource> load(String path, boolean recursively) throws IOException {
        return this.load(path, recursively, Filters.ALWAYS);
    }

    @Override
    public Enumeration<Resource> load(String path, Filter filter) throws IOException {
        return this.load(path, true, filter);
    }

    /*
     * This class specifies class file version 49.0 but uses Java 6 signatures.  Assumed Java 6.
     */
    protected static abstract class ResourceEnumerator
    implements Enumeration<Resource> {
        protected Resource next;

        protected ResourceEnumerator() {
        }

        @Override
        public Resource nextElement() {
            if (this.hasMoreElements()) {
                Resource resource = this.next;
                this.next = null;
                return resource;
            }
            throw new NoSuchElementException();
        }
    }
}
