/*
 * Decompiled with CFR 0.152.
 */
package io.loadkit;

import io.loadkit.Filter;
import io.loadkit.RegexFilter;

public class AntFilter
extends RegexFilter
implements Filter {
    private static final String[] SYMBOLS = new String[]{"\\", "$", "(", ")", "+", ".", "[", "]", "^", "{", "}", "|"};

    public AntFilter(String ant) {
        super(AntFilter.convert(ant));
    }

    private static String convert(String ant) {
        String regex = ant;
        for (String symbol : SYMBOLS) {
            regex = regex.replace(symbol, '\\' + symbol);
        }
        regex = regex.replace("?", ".{1}");
        regex = regex.replace("**/", "(.{0,}?/){0,}?");
        regex = regex.replace("**", ".{0,}?");
        regex = regex.replace("*", "[^/]{0,}?");
        while (regex.startsWith("/")) {
            regex = regex.substring(1);
        }
        while (regex.endsWith("/")) {
            regex = regex.substring(0, regex.length() - 1);
        }
        return regex;
    }
}
