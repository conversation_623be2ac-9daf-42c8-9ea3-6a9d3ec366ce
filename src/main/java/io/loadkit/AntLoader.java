/*
 * Decompiled with CFR 0.152.
 */
package io.loadkit;

import io.loadkit.AntFilter;
import io.loadkit.Filter;
import io.loadkit.Loader;
import io.loadkit.PatternLoader;
import io.loadkit.Resource;
import io.loadkit.StdLoader;
import java.io.IOException;
import java.util.Enumeration;

/*
 * This class specifies class file version 49.0 but uses Java 6 signatures.  Assumed Java 6.
 */
public class AntLoader
extends PatternLoader
implements Loader {
    public AntLoader() {
        this(new StdLoader());
    }

    public AntLoader(ClassLoader classLoader) {
        this(new StdLoader(classLoader));
    }

    public AntLoader(Loader delegate) {
        super(delegate);
    }

    @Override
    public Enumeration<Resource> load(String pattern, boolean recursively, Filter filter) throws IOException {
        if (Math.max(pattern.indexOf(42), pattern.indexOf(63)) < 0) {
            return this.delegate.load(pattern, recursively, filter);
        }
        return super.load(pattern, recursively, filter);
    }

    @Override
    protected String path(String ant) {
        int index = 0x7FFFFFFE;
        if (ant.contains("*") && ant.indexOf(42) < index) {
            index = ant.indexOf(42);
        }
        if (ant.contains("?") && ant.indexOf(63) < index) {
            index = ant.indexOf(63);
        }
        return ant.substring(0, ant.lastIndexOf(47, index) + 1);
    }

    @Override
    protected boolean recursively(String ant) {
        return true;
    }

    @Override
    protected Filter filter(String ant) {
        return new AntFilter(ant);
    }
}
