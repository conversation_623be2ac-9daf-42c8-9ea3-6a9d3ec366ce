/*
 * Decompiled with CFR 0.152.
 */
package io.loadkit;

import io.loadkit.Filter;
import io.loadkit.Res;
import io.loadkit.Resource;
import io.loadkit.ResourceLoader;
import java.io.File;
import java.net.URL;
import java.util.Enumeration;
import java.util.LinkedList;
import java.util.Queue;

/*
 * This class specifies class file version 49.0 but uses Java 6 signatures.  Assumed Java 6.
 */
private static class FileLoader.Enumerator
extends ResourceLoader.ResourceEnumerator
implements Enumeration<Resource> {
    private final URL context;
    private final boolean recursively;
    private final Filter filter;
    private final Queue<File> queue;

    FileLoader.Enumerator(URL context, File root, String path, boolean recursively, Filter filter) {
        this.context = context;
        this.recursively = recursively;
        this.filter = filter;
        this.queue = new LinkedList<File>();
        File file = new File(root, path);
        if (file.isDirectory()) {
            File[] files = file.listFiles();
            for (int i = 0; files != null && i < files.length; ++i) {
                this.queue.offer(files[i]);
            }
        } else {
            this.queue.offer(file);
        }
    }

    @Override
    public boolean hasMoreElements() {
        if (this.next != null) {
            return true;
        }
        while (!this.queue.isEmpty()) {
            File file = this.queue.poll();
            if (!file.exists()) continue;
            if (file.isFile()) {
                try {
                    String name = this.context.toURI().relativize(file.toURI()).toString();
                    URL url = new URL(this.context, name);
                    if (this.filter.filtrate(name, url)) {
                        this.next = new Res(name, url);
                        return true;
                    }
                }
                catch (Exception e) {
                    throw new IllegalStateException(e);
                }
            }
            if (!file.isDirectory() || !this.recursively) continue;
            File[] files = file.listFiles();
            for (int i = 0; files != null && i < files.length; ++i) {
                this.queue.offer(files[i]);
            }
            return this.hasMoreElements();
        }
        return false;
    }
}
