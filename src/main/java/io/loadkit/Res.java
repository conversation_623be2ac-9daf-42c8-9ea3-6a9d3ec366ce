/*
 * Decompiled with CFR 0.152.
 */
package io.loadkit;

import io.loadkit.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;

public class Res
implements Resource {
    private final String name;
    private final URL url;

    public Res(String name, URL url) {
        if (name == null) {
            throw new IllegalArgumentException("name must not be null");
        }
        if (url == null) {
            throw new IllegalArgumentException("url must not be null");
        }
        this.name = name;
        this.url = url;
    }

    public String getName() {
        return this.name;
    }

    public URL getUrl() {
        return this.url;
    }

    public InputStream getInputStream() throws IOException {
        return this.url.openStream();
    }

    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || this.getClass() != o.getClass()) {
            return false;
        }
        Res that = (Res)o;
        return this.url.equals(that.url);
    }

    public int hashCode() {
        return this.url.hashCode();
    }

    public String toString() {
        return this.url.toString();
    }
}
