/*
 * Decompiled with CFR 0.152.
 */
package io.loadkit;

import io.loadkit.Filter;
import java.net.URL;
import java.util.regex.Pattern;

public class RegexFilter
implements Filter {
    private final Pattern pattern;

    public RegexFilter(String regex) {
        this(Pattern.compile(regex));
    }

    public RegexFilter(Pattern pattern) {
        if (pattern == null) {
            throw new IllegalArgumentException("pattern must not be null");
        }
        this.pattern = pattern;
    }

    public boolean filtrate(String name, URL url) {
        return this.pattern.matcher(name).matches();
    }
}
