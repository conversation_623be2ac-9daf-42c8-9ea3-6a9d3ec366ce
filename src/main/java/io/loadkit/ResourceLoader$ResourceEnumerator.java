/*
 * Decompiled with CFR 0.152.
 */
package io.loadkit;

import io.loadkit.Resource;
import java.util.Enumeration;
import java.util.NoSuchElementException;

/*
 * This class specifies class file version 49.0 but uses Java 6 signatures.  Assumed Java 6.
 */
protected static abstract class ResourceLoader.ResourceEnumerator
implements Enumeration<Resource> {
    protected Resource next;

    protected ResourceLoader.ResourceEnumerator() {
    }

    @Override
    public Resource nextElement() {
        if (this.hasMoreElements()) {
            Resource resource = this.next;
            this.next = null;
            return resource;
        }
        throw new NoSuchElementException();
    }
}
