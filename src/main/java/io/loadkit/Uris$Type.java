/*
 * Decompiled with CFR 0.152.
 */
package io.loadkit;

/*
 * This class specifies class file version 49.0 but uses Java 6 signatures.  Assumed Java 6.
 */
public static enum Uris.Type {
    SCHEME{

        public boolean isAllowed(int c) {
            return this.isAlpha(c) || this.isDigit(c) || 43 == c || 45 == c || 46 == c;
        }
    }
    ,
    AUTHORITY{

        public boolean isAllowed(int c) {
            return this.isUnreserved(c) || this.isSubDelimiter(c) || 58 == c || 64 == c;
        }
    }
    ,
    USER_INFO{

        public boolean isAllowed(int c) {
            return this.isUnreserved(c) || this.isSubDelimiter(c) || 58 == c;
        }
    }
    ,
    HOST_IPV4{

        public boolean isAllowed(int c) {
            return this.isUnreserved(c) || this.isSubDelimiter(c);
        }
    }
    ,
    HOST_IPV6{

        public boolean isAllowed(int c) {
            return this.isUnreserved(c) || this.isSubDelimiter(c) || 91 == c || 93 == c || 58 == c;
        }
    }
    ,
    PORT{

        public boolean isAllowed(int c) {
            return this.isDigit(c);
        }
    }
    ,
    PATH{

        public boolean isAllowed(int c) {
            return this.isPchar(c) || 47 == c;
        }
    }
    ,
    PATH_SEGMENT{

        public boolean isAllowed(int c) {
            return this.isPchar(c);
        }
    }
    ,
    QUERY{

        public boolean isAllowed(int c) {
            return this.isPchar(c) || 47 == c || 63 == c;
        }
    }
    ,
    QUERY_PARAM{

        public boolean isAllowed(int c) {
            if (61 == c || 38 == c) {
                return false;
            }
            return this.isPchar(c) || 47 == c || 63 == c;
        }
    }
    ,
    FRAGMENT{

        public boolean isAllowed(int c) {
            return this.isPchar(c) || 47 == c || 63 == c;
        }
    }
    ,
    URI{

        public boolean isAllowed(int c) {
            return this.isUnreserved(c);
        }
    };


    public abstract boolean isAllowed(int var1);

    protected boolean isAlpha(int c) {
        return c >= 97 && c <= 122 || c >= 65 && c <= 90;
    }

    protected boolean isDigit(int c) {
        return c >= 48 && c <= 57;
    }

    protected boolean isGenericDelimiter(int c) {
        return 58 == c || 47 == c || 63 == c || 35 == c || 91 == c || 93 == c || 64 == c;
    }

    protected boolean isSubDelimiter(int c) {
        return 33 == c || 36 == c || 38 == c || 39 == c || 40 == c || 41 == c || 42 == c || 43 == c || 44 == c || 59 == c || 61 == c;
    }

    protected boolean isReserved(int c) {
        return this.isGenericDelimiter(c) || this.isSubDelimiter(c);
    }

    protected boolean isUnreserved(int c) {
        return this.isAlpha(c) || this.isDigit(c) || 45 == c || 46 == c || 95 == c || 126 == c;
    }

    protected boolean isPchar(int c) {
        return this.isUnreserved(c) || this.isSubDelimiter(c) || 58 == c || 64 == c;
    }
}
