/*
 * Decompiled with CFR 0.152.
 */
package io.loadkit;

import io.loadkit.AllFilter;
import io.loadkit.AnyFilter;
import io.loadkit.Filter;
import java.net.URL;
import java.util.Collection;

/*
 * This class specifies class file version 49.0 but uses Java 6 signatures.  Assumed Java 6.
 */
public abstract class Filters {
    public static final Filter ALWAYS = new Filter(){

        public boolean filtrate(String name, URL url) {
            return true;
        }
    };
    public static final Filter NEVER = new Filter(){

        public boolean filtrate(String name, URL url) {
            return false;
        }
    };

    public static Filter all(Filter ... filters) {
        return new AllFilter(filters);
    }

    public static Filter all(Collection<? extends Filter> filters) {
        return new AllFilter(filters);
    }

    public static Filter and(Filter ... filters) {
        return Filters.all(filters);
    }

    public static Filter and(Collection<? extends Filter> filters) {
        return Filters.all(filters);
    }

    public static Filter any(Filter ... filters) {
        return new AnyFilter(filters);
    }

    public static Filter any(Collection<? extends Filter> filters) {
        return new AnyFilter(filters);
    }

    public static Filter or(Filter ... filters) {
        return Filters.any(filters);
    }

    public static Filter or(Collection<? extends Filter> filters) {
        return Filters.any(filters);
    }
}
