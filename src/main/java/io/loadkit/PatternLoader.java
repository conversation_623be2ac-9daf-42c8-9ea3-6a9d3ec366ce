/*
 * Decompiled with CFR 0.152.
 */
package io.loadkit;

import io.loadkit.AllFilter;
import io.loadkit.DelegateLoader;
import io.loadkit.Filter;
import io.loadkit.Loader;
import io.loadkit.Resource;
import java.io.IOException;
import java.util.Enumeration;

/*
 * This class specifies class file version 49.0 but uses Java 6 signatures.  Assumed Java 6.
 */
public abstract class PatternLoader
extends DelegateLoader
implements Loader {
    protected PatternLoader(Loader delegate) {
        super(delegate);
    }

    @Override
    public Enumeration<Resource> load(String pattern, boolean recursively, Filter filter) throws IOException {
        Filter matcher = this.filter(pattern);
        AllFilter allFilter = new AllFilter(new Filter[0]);
        if (matcher != null) {
            allFilter.add(matcher);
        }
        if (filter != null) {
            allFilter.add(filter);
        }
        return this.delegate.load(this.path(pattern), this.recursively(pattern), allFilter);
    }

    protected abstract String path(String var1);

    protected abstract boolean recursively(String var1);

    protected abstract Filter filter(String var1);
}
