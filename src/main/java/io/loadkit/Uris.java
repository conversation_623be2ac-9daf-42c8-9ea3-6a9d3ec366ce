/*
 * Decompiled with CFR 0.152.
 */
package io.loadkit;

import java.io.ByteArrayOutputStream;
import java.io.UnsupportedEncodingException;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.Map;

/*
 * This class specifies class file version 49.0 but uses Java 6 signatures.  Assumed Java 6.
 */
public abstract class Uris {
    public static String encodeScheme(String scheme, String encoding) {
        return Uris.encode(scheme, encoding, Type.SCHEME);
    }

    public static String encodeScheme(String scheme, Charset charset) {
        return Uris.encode(scheme, charset, Type.SCHEME);
    }

    public static String encodeAuthority(String authority, String encoding) {
        return Uris.encode(authority, encoding, Type.AUTHORITY);
    }

    public static String encodeAuthority(String authority, Charset charset) {
        return Uris.encode(authority, charset, Type.AUTHORITY);
    }

    public static String encodeUserInfo(String userInfo, String encoding) {
        return Uris.encode(userInfo, encoding, Type.USER_INFO);
    }

    public static String encodeUserInfo(String userInfo, Charset charset) {
        return Uris.encode(userInfo, charset, Type.USER_INFO);
    }

    public static String encodeHost(String host, String encoding) {
        return Uris.encode(host, encoding, Type.HOST_IPV4);
    }

    public static String encodeHost(String host, Charset charset) {
        return Uris.encode(host, charset, Type.HOST_IPV4);
    }

    public static String encodePort(String port, String encoding) {
        return Uris.encode(port, encoding, Type.PORT);
    }

    public static String encodePort(String port, Charset charset) {
        return Uris.encode(port, charset, Type.PORT);
    }

    public static String encodePath(String path, String encoding) {
        return Uris.encode(path, encoding, Type.PATH);
    }

    public static String encodePath(String path, Charset charset) {
        return Uris.encode(path, charset, Type.PATH);
    }

    public static String encodePathSegment(String segment, String encoding) {
        return Uris.encode(segment, encoding, Type.PATH_SEGMENT);
    }

    public static String encodePathSegment(String segment, Charset charset) {
        return Uris.encode(segment, charset, Type.PATH_SEGMENT);
    }

    public static String encodeQuery(String query, String encoding) {
        return Uris.encode(query, encoding, Type.QUERY);
    }

    public static String encodeQuery(String query, Charset charset) {
        return Uris.encode(query, charset, Type.QUERY);
    }

    public static String encodeQueryParam(String queryParam, String encoding) {
        return Uris.encode(queryParam, encoding, Type.QUERY_PARAM);
    }

    public static String encodeQueryParam(String queryParam, Charset charset) {
        return Uris.encode(queryParam, charset, Type.QUERY_PARAM);
    }

    public static String encodeFragment(String fragment, String encoding) {
        return Uris.encode(fragment, encoding, Type.FRAGMENT);
    }

    public static String encodeFragment(String fragment, Charset charset) {
        return Uris.encode(fragment, charset, Type.FRAGMENT);
    }

    public static String encode(String source, String encoding) {
        return Uris.encode(source, encoding, Type.URI);
    }

    public static String encode(String source, Charset charset) {
        return Uris.encode(source, charset, Type.URI);
    }

    public static Map<String, String> encodeUriVariables(Map<String, ?> uriVariables) {
        LinkedHashMap<String, String> result = new LinkedHashMap<String, String>(uriVariables.size());
        for (Map.Entry<String, ?> entry : uriVariables.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            String stringValue = value != null ? value.toString() : "";
            result.put(key, Uris.encode(stringValue, Charset.forName("UTF-8")));
        }
        return result;
    }

    public static Object[] encodeUriVariables(Object ... uriVariables) {
        ArrayList<String> result = new ArrayList<String>();
        for (Object value : uriVariables) {
            String stringValue = value != null ? value.toString() : "";
            result.add(Uris.encode(stringValue, Charset.forName("UTF-8")));
        }
        return result.toArray();
    }

    private static String encode(String scheme, String encoding, Type type) {
        return Uris.encodeUriComponent(scheme, encoding, type);
    }

    private static String encode(String scheme, Charset charset, Type type) {
        return Uris.encodeUriComponent(scheme, charset, type);
    }

    static String encodeUriComponent(String source, String encoding, Type type) {
        return Uris.encodeUriComponent(source, Charset.forName(encoding), type);
    }

    static String encodeUriComponent(String source, Charset charset, Type type) {
        byte[] bytes;
        if (source == null || source.length() <= 0) {
            return source;
        }
        if (charset == null) {
            throw new IllegalArgumentException("Charset must not be null");
        }
        if (type == null) {
            throw new IllegalArgumentException("Type must not be null");
        }
        try {
            bytes = source.getBytes(charset.name());
        }
        catch (UnsupportedEncodingException e) {
            throw new IllegalStateException(e);
        }
        ByteArrayOutputStream bos = new ByteArrayOutputStream(bytes.length);
        boolean changed = false;
        for (byte b : bytes) {
            if (b < 0) {
                b = (byte)(b + 256);
            }
            if (type.isAllowed(b)) {
                bos.write(b);
                continue;
            }
            bos.write(37);
            char hex1 = Character.toUpperCase(Character.forDigit(b >> 4 & 0xF, 16));
            char hex2 = Character.toUpperCase(Character.forDigit(b & 0xF, 16));
            bos.write(hex1);
            bos.write(hex2);
            changed = true;
        }
        try {
            return changed ? new String(bos.toByteArray(), charset.name()) : source;
        }
        catch (UnsupportedEncodingException e) {
            throw new IllegalStateException(e);
        }
    }

    public static String uriDecode(String source, Charset charset) {
        int length = source.length();
        if (length == 0) {
            return source;
        }
        if (charset == null) {
            throw new IllegalArgumentException("Charset must not be null");
        }
        ByteArrayOutputStream bos = new ByteArrayOutputStream(length);
        boolean changed = false;
        for (int i = 0; i < length; ++i) {
            char ch = source.charAt(i);
            if (ch == '%') {
                if (i + 2 < length) {
                    char hex1 = source.charAt(i + 1);
                    char hex2 = source.charAt(i + 2);
                    int u = Character.digit(hex1, 16);
                    int l = Character.digit(hex2, 16);
                    if (u == -1 || l == -1) {
                        throw new IllegalArgumentException("Invalid encoded sequence \"" + source.substring(i) + "\"");
                    }
                    bos.write((char)((u << 4) + l));
                    i += 2;
                    changed = true;
                    continue;
                }
                throw new IllegalArgumentException("Invalid encoded sequence \"" + source.substring(i) + "\"");
            }
            bos.write(ch);
        }
        try {
            return changed ? new String(bos.toByteArray(), charset.name()) : source;
        }
        catch (UnsupportedEncodingException e) {
            throw new IllegalStateException(e);
        }
    }

    public static String decode(String source, String encoding) {
        return Uris.uriDecode(source, Charset.forName(encoding));
    }

    public static String decode(String source, Charset charset) {
        return Uris.uriDecode(source, charset);
    }

    public static String extractFileExtension(String path) {
        int begin;
        int paramIndex;
        int extIndex;
        int end = path.indexOf(63);
        int fragmentIndex = path.indexOf(35);
        if (fragmentIndex != -1 && (end == -1 || fragmentIndex < end)) {
            end = fragmentIndex;
        }
        if (end == -1) {
            end = path.length();
        }
        if ((extIndex = path.lastIndexOf(46, end = (paramIndex = path.indexOf(59, begin = path.lastIndexOf(47, end) + 1)) != -1 && paramIndex < end ? paramIndex : end)) != -1 && extIndex > begin) {
            return path.substring(extIndex + 1, end);
        }
        return null;
    }

    /*
     * This class specifies class file version 49.0 but uses Java 6 signatures.  Assumed Java 6.
     */
    public static enum Type {
        SCHEME{

            public boolean isAllowed(int c) {
                return this.isAlpha(c) || this.isDigit(c) || 43 == c || 45 == c || 46 == c;
            }
        }
        ,
        AUTHORITY{

            public boolean isAllowed(int c) {
                return this.isUnreserved(c) || this.isSubDelimiter(c) || 58 == c || 64 == c;
            }
        }
        ,
        USER_INFO{

            public boolean isAllowed(int c) {
                return this.isUnreserved(c) || this.isSubDelimiter(c) || 58 == c;
            }
        }
        ,
        HOST_IPV4{

            public boolean isAllowed(int c) {
                return this.isUnreserved(c) || this.isSubDelimiter(c);
            }
        }
        ,
        HOST_IPV6{

            public boolean isAllowed(int c) {
                return this.isUnreserved(c) || this.isSubDelimiter(c) || 91 == c || 93 == c || 58 == c;
            }
        }
        ,
        PORT{

            public boolean isAllowed(int c) {
                return this.isDigit(c);
            }
        }
        ,
        PATH{

            public boolean isAllowed(int c) {
                return this.isPchar(c) || 47 == c;
            }
        }
        ,
        PATH_SEGMENT{

            public boolean isAllowed(int c) {
                return this.isPchar(c);
            }
        }
        ,
        QUERY{

            public boolean isAllowed(int c) {
                return this.isPchar(c) || 47 == c || 63 == c;
            }
        }
        ,
        QUERY_PARAM{

            public boolean isAllowed(int c) {
                if (61 == c || 38 == c) {
                    return false;
                }
                return this.isPchar(c) || 47 == c || 63 == c;
            }
        }
        ,
        FRAGMENT{

            public boolean isAllowed(int c) {
                return this.isPchar(c) || 47 == c || 63 == c;
            }
        }
        ,
        URI{

            public boolean isAllowed(int c) {
                return this.isUnreserved(c);
            }
        };


        public abstract boolean isAllowed(int var1);

        protected boolean isAlpha(int c) {
            return c >= 97 && c <= 122 || c >= 65 && c <= 90;
        }

        protected boolean isDigit(int c) {
            return c >= 48 && c <= 57;
        }

        protected boolean isGenericDelimiter(int c) {
            return 58 == c || 47 == c || 63 == c || 35 == c || 91 == c || 93 == c || 64 == c;
        }

        protected boolean isSubDelimiter(int c) {
            return 33 == c || 36 == c || 38 == c || 39 == c || 40 == c || 41 == c || 42 == c || 43 == c || 44 == c || 59 == c || 61 == c;
        }

        protected boolean isReserved(int c) {
            return this.isGenericDelimiter(c) || this.isSubDelimiter(c);
        }

        protected boolean isUnreserved(int c) {
            return this.isAlpha(c) || this.isDigit(c) || 45 == c || 46 == c || 95 == c || 126 == c;
        }

        protected boolean isPchar(int c) {
            return this.isUnreserved(c) || this.isSubDelimiter(c) || 58 == c || 64 == c;
        }
    }
}
