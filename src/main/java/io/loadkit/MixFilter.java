/*
 * Decompiled with CFR 0.152.
 */
package io.loadkit;

import io.loadkit.Filter;
import java.util.Arrays;
import java.util.Collection;
import java.util.LinkedHashSet;
import java.util.Set;

/*
 * This class specifies class file version 49.0 but uses Java 6 signatures.  Assumed Java 6.
 */
public abstract class MixFilter
implements Filter {
    protected final Set<Filter> filters;

    protected MixFilter(Filter ... filters) {
        this(Arrays.asList(filters));
    }

    protected MixFilter(Collection<? extends Filter> filters) {
        this.filters = filters != null ? new LinkedHashSet<Filter>(filters) : new LinkedHashSet();
    }

    public boolean add(Filter filter) {
        return this.filters.add(filter);
    }

    public boolean remove(Filter filter) {
        return this.filters.remove(filter);
    }

    public abstract MixFilter mix(Filter var1);
}
