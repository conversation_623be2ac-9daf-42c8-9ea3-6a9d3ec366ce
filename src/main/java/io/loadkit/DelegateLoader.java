/*
 * Decompiled with CFR 0.152.
 */
package io.loadkit;

import io.loadkit.Loader;
import io.loadkit.ResourceLoader;

public abstract class DelegateLoader
extends ResourceLoader
implements Loader {
    protected final Loader delegate;

    protected DelegateLoader(Loader delegate) {
        if (delegate == null) {
            throw new IllegalArgumentException("delegate must not be null");
        }
        this.delegate = delegate;
    }
}
