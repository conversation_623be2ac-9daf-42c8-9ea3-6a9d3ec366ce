/*
 * Decompiled with CFR 0.152.
 */
package io.loadkit;

import io.loadkit.DelegateLoader;
import io.loadkit.Filter;
import io.loadkit.Loader;
import io.loadkit.Resource;
import io.loadkit.StdLoader;
import java.io.IOException;
import java.util.Enumeration;

/*
 * This class specifies class file version 49.0 but uses Java 6 signatures.  Assumed Java 6.
 */
public class PkgLoader
extends DelegateLoader
implements Loader {
    public PkgLoader() {
        this(new StdLoader());
    }

    public PkgLoader(ClassLoader classLoader) {
        this(new StdLoader(classLoader));
    }

    public PkgLoader(Loader delegate) {
        super(delegate);
    }

    @Override
    public Enumeration<Resource> load(String pkg, boolean recursively, Filter filter) throws IOException {
        String path = pkg.replace('.', '/');
        return this.delegate.load(path, recursively, filter);
    }
}
