/*
 * Decompiled with CFR 0.152.
 */
package io.loadkit;

import io.loadkit.Filter;
import io.loadkit.Resource;
import java.io.IOException;
import java.util.Enumeration;

/*
 * This class specifies class file version 49.0 but uses Java 6 signatures.  Assumed Java 6.
 */
public interface Loader {
    public Enumeration<Resource> load(String var1) throws IOException;

    public Enumeration<Resource> load(String var1, boolean var2) throws IOException;

    public Enumeration<Resource> load(String var1, Filter var2) throws IOException;

    public Enumeration<Resource> load(String var1, boolean var2, Filter var3) throws IOException;
}
