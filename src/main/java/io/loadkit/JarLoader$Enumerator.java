/*
 * Decompiled with CFR 0.152.
 */
package io.loadkit;

import io.loadkit.Filter;
import io.loadkit.Res;
import io.loadkit.Resource;
import io.loadkit.ResourceLoader;
import io.loadkit.Uris;
import java.net.URL;
import java.nio.charset.Charset;
import java.util.Enumeration;
import java.util.jar.JarEntry;
import java.util.jar.JarFile;

/*
 * This class specifies class file version 49.0 but uses Java 6 signatures.  Assumed Java 6.
 */
private static class JarLoader.Enumerator
extends ResourceLoader.ResourceEnumerator
implements Enumeration<Resource> {
    private final URL context;
    private final String path;
    private final String folder;
    private final boolean recursively;
    private final Filter filter;
    private final Enumeration<JarEntry> entries;

    JarLoader.Enumerator(URL context, JarFile jarFile, String path, boolean recursively, Filter filter) {
        this.context = context;
        this.path = path;
        this.folder = path.endsWith("/") || path.length() == 0 ? path : path + "/";
        this.recursively = recursively;
        this.filter = filter;
        this.entries = jarFile.entries();
    }

    @Override
    public boolean hasMoreElements() {
        if (this.next != null) {
            return true;
        }
        while (this.entries.hasMoreElements()) {
            String name;
            JarEntry jarEntry = this.entries.nextElement();
            if (jarEntry.isDirectory() || !(name = jarEntry.getName()).equals(this.path) && (!this.recursively || !name.startsWith(this.folder)) && (this.recursively || !name.startsWith(this.folder) || name.indexOf(47, this.folder.length()) >= 0)) continue;
            try {
                URL url = new URL(this.context, Uris.encodePath(name, Charset.defaultCharset()));
                if (!this.filter.filtrate(name, url)) continue;
                this.next = new Res(name, url);
                return true;
            }
            catch (Exception e) {
                throw new IllegalStateException(e);
            }
        }
        return false;
    }
}
