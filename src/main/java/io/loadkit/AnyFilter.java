/*
 * Decompiled with CFR 0.152.
 */
package io.loadkit;

import io.loadkit.Filter;
import io.loadkit.MixFilter;
import java.net.URL;
import java.util.Collection;

/*
 * This class specifies class file version 49.0 but uses Java 6 signatures.  Assumed Java 6.
 */
public class AnyFilter
extends MixFilter
implements Filter {
    public AnyFilter(Filter ... filters) {
        super(filters);
    }

    public AnyFilter(Collection<? extends Filter> filters) {
        super(filters);
    }

    @Override
    public boolean filtrate(String name, URL url) {
        Filter[] filters;
        for (Filter filter : filters = this.filters.toArray(new Filter[0])) {
            if (!filter.filtrate(name, url)) continue;
            return true;
        }
        return false;
    }

    @Override
    public AnyFilter mix(Filter filter) {
        this.add(filter);
        return this;
    }
}
