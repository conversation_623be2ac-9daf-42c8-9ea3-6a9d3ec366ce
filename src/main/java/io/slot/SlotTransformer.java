/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.apache.maven.model.Build
 *  org.apache.maven.model.Plugin
 *  org.apache.maven.plugin.AbstractMojo
 *  org.apache.maven.plugin.MojoExecutionException
 *  org.apache.maven.plugin.MojoFailureException
 *  org.apache.maven.plugin.logging.Log
 *  org.apache.maven.plugins.annotations.LifecyclePhase
 *  org.apache.maven.plugins.annotations.Mojo
 *  org.apache.maven.plugins.annotations.Parameter
 *  org.apache.maven.project.MavenProject
 *  org.codehaus.plexus.util.xml.Xpp3Dom
 */
package io.slot;

import io.slot.BootSlotter;
import java.io.File;
import java.io.IOException;
import java.util.Map;
import org.apache.maven.model.Build;
import org.apache.maven.model.Plugin;
import org.apache.maven.plugin.AbstractMojo;
import org.apache.maven.plugin.MojoExecutionException;
import org.apache.maven.plugin.MojoFailureException;
import org.apache.maven.plugin.logging.Log;
import org.apache.maven.plugins.annotations.LifecyclePhase;
import org.apache.maven.plugins.annotations.Mojo;
import org.apache.maven.plugins.annotations.Parameter;
import org.apache.maven.project.MavenProject;
import org.codehaus.plexus.util.xml.Xpp3Dom;

@Mojo(name="transform", defaultPhase=LifecyclePhase.PACKAGE)
public class SlotTransformer
extends AbstractMojo {
    @Parameter(defaultValue="${project}", readonly=true, required=true)
    private MavenProject project;
    @Parameter(property="slot.sourceDir", required=true, defaultValue="${project.build.directory}")
    private File sourceDir;
    @Parameter(property="slot.sourceJar", required=true, defaultValue="${project.build.finalName}.jar")
    private String sourceJar;
    @Parameter(property="slot.targetDir", required=true, defaultValue="${project.build.directory}")
    private File targetDir;
    @Parameter(property="slot.targetJar", required=true, defaultValue="${project.build.finalName}-slot.jar")
    private String targetJar;

    public void execute() throws MojoExecutionException, MojoFailureException {
        Log log = this.getLog();
        String packaging = this.project.getPackaging();
        if (!"jar".equalsIgnoreCase(packaging)) {
            throw new MojoExecutionException("Could not transform project with packaging: " + packaging);
        }
        Build build = this.project.getBuild();
        Map plugins = build.getPluginsAsMap();
        Plugin plugin = (Plugin)plugins.get("org.springframework.boot:spring-boot-maven-plugin");
        if (plugin == null) {
            throw new MojoFailureException("Could not transform not spring-boot project");
        }
        Object configuration = plugin.getConfiguration();
        if (configuration instanceof Xpp3Dom) {
            String embeddedLaunchScript;
            String executable;
            Xpp3Dom dom = (Xpp3Dom)configuration;
            Xpp3Dom child = dom.getChild("executable");
            String string = executable = child != null ? child.getValue() : null;
            if ("true".equalsIgnoreCase(executable)) {
                String msg = "Unsupported to transform <executable>true</executable> spring boot JAR file, ";
                msg = msg + "maybe you should upgrade slot-maven-plugin dependency if it have been supported in the later versions,";
                msg = msg + "if not, delete <executable>true</executable> or set executable as false for the configuration of spring-boot-maven-plugin.";
                throw new MojoFailureException(msg);
            }
            child = dom.getChild("embeddedLaunchScript");
            String string2 = embeddedLaunchScript = child != null ? child.getValue() : null;
            if (embeddedLaunchScript != null) {
                String msg = "Unsupported to transform <embeddedLaunchScript>...</embeddedLaunchScript> spring boot JAR file, ";
                msg = msg + "maybe you should upgrade slot-maven-plugin dependency if it have been supported in the later versions,";
                msg = msg + "if not, delete <embeddedLaunchScript>...</embeddedLaunchScript> for the configuration of spring-boot-maven-plugin.";
                throw new MojoFailureException(msg);
            }
        }
        try {
            File src = new File(this.sourceDir, this.sourceJar);
            File dest = new File(this.targetDir, this.targetJar);
            File folder = dest.getParentFile();
            if (!(folder.exists() || folder.mkdirs() || folder.exists())) {
                throw new IOException("could not make directory: " + folder);
            }
            log.info((CharSequence)("Transforming " + src + " to " + dest));
            BootSlotter slotter = new BootSlotter();
            slotter.slot(src, dest);
        }
        catch (IOException e) {
            throw new MojoFailureException(e.getMessage(), (Throwable)e);
        }
    }

    public MavenProject getProject() {
        return this.project;
    }

    public void setProject(MavenProject project) {
        this.project = project;
    }

    public File getSourceDir() {
        return this.sourceDir;
    }

    public void setSourceDir(File sourceDir) {
        this.sourceDir = sourceDir;
    }

    public String getSourceJar() {
        return this.sourceJar;
    }

    public void setSourceJar(String sourceJar) {
        this.sourceJar = sourceJar;
    }

    public File getTargetDir() {
        return this.targetDir;
    }

    public void setTargetDir(File targetDir) {
        this.targetDir = targetDir;
    }

    public String getTargetJar() {
        return this.targetJar;
    }

    public void setTargetJar(String targetJar) {
        this.targetJar = targetJar;
    }
}
