/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.apache.commons.compress.archivers.ArchiveEntry
 *  org.apache.commons.compress.archivers.jar.JarArchiveEntry
 *  org.apache.commons.compress.archivers.jar.JarArchiveInputStream
 *  org.apache.commons.compress.archivers.jar.JarArchiveOutputStream
 */
package io.slot;

import io.slot.FileSlotter;
import io.slot.IOKit;
import io.slot.Slotter;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.Closeable;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.jar.Attributes;
import java.util.jar.Manifest;
import java.util.zip.CRC32;
import java.util.zip.CheckedOutputStream;
import org.apache.commons.compress.archivers.ArchiveEntry;
import org.apache.commons.compress.archivers.jar.JarArchiveEntry;
import org.apache.commons.compress.archivers.jar.JarArchiveInputStream;
import org.apache.commons.compress.archivers.jar.JarArchiveOutputStream;

public class BootSlotter
extends FileSlotter
implements Slotter {
    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void slot(InputStream in, OutputStream out) throws IOException {
        JarArchiveInputStream zis = null;
        JarArchiveOutputStream zos = null;
        try {
            JarArchiveEntry entry;
            zis = new JarArchiveInputStream(in);
            zos = new JarArchiveOutputStream(out);
            while ((entry = zis.getNextJarEntry()) != null) {
                JarArchiveEntry jarArchiveEntry;
                JarArchiveEntry jarArchiveEntry2;
                if (entry.isDirectory()) {
                    jarArchiveEntry2 = new JarArchiveEntry(entry.getName());
                    jarArchiveEntry2.setTime(entry.getTime());
                    zos.putArchiveEntry((ArchiveEntry)jarArchiveEntry2);
                } else if (entry.getName().endsWith(".jar")) {
                    ByteArrayOutputStream bos = new ByteArrayOutputStream();
                    CheckedOutputStream cos = new CheckedOutputStream(bos, new CRC32());
                    IOKit.transfer((InputStream)zis, cos);
                    jarArchiveEntry = new JarArchiveEntry(entry.getName());
                    jarArchiveEntry.setMethod(0);
                    jarArchiveEntry.setSize((long)bos.size());
                    jarArchiveEntry.setTime(entry.getTime());
                    jarArchiveEntry.setCrc(cos.getChecksum().getValue());
                    zos.putArchiveEntry((ArchiveEntry)jarArchiveEntry);
                    ByteArrayInputStream bis = new ByteArrayInputStream(bos.toByteArray());
                    IOKit.transfer((InputStream)bis, (OutputStream)zos);
                } else if (entry.getName().equals("META-INF/MANIFEST.MF")) {
                    Manifest manifest = new Manifest((InputStream)zis);
                    Attributes attributes = manifest.getMainAttributes();
                    attributes.putValue("Main-Class", "io.slot.BootLauncher");
                    jarArchiveEntry = new JarArchiveEntry(entry.getName());
                    jarArchiveEntry.setTime(entry.getTime());
                    zos.putArchiveEntry((ArchiveEntry)jarArchiveEntry);
                    manifest.write((OutputStream)zos);
                } else {
                    jarArchiveEntry2 = new JarArchiveEntry(entry.getName());
                    jarArchiveEntry2.setTime(entry.getTime());
                    zos.putArchiveEntry((ArchiveEntry)jarArchiveEntry2);
                    IOKit.transfer((InputStream)zis, (OutputStream)zos);
                }
                zos.closeArchiveEntry();
            }
            IOKit.embed("io/slot/**", zos);
            IOKit.embed("io/loadkit/**", zos);
            zos.finish();
        }
        catch (Throwable throwable) {
            IOKit.close(zis);
            IOKit.close(zos);
            throw throwable;
        }
        IOKit.close((Closeable)zis);
        IOKit.close((Closeable)zos);
    }
}
