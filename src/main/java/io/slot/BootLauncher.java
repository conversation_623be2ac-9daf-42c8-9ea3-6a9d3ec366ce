/*
 * Decompiled with CFR 0.152.
 */
package io.slot;

import io.loadkit.Loaders;
import io.loadkit.Resource;
import java.io.File;
import java.net.URL;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Enumeration;
import java.util.LinkedHashSet;
import java.util.List;
import org.springframework.boot.loader.JarLauncher;

public class BootLauncher
extends JarLauncher {
    private static final String SLOT_ROOT = "--slot.root=";
    private static final String SLOT_PATH = "--slot.path=";
    private final String root;
    private final List<String> paths;

    public BootLauncher(String root, List<String> paths) {
        if (root == null) {
            throw new NullPointerException("root must not be null");
        }
        if (paths == null) {
            paths = Collections.emptyList();
        }
        this.root = root;
        this.paths = paths;
    }

    public static void main(String[] args) throws Exception {
        String root = System.getProperty("user.dir");
        ArrayList<String> paths = new ArrayList<String>();
        ArrayList<String> arguments = new ArrayList<String>();
        for (String arg : args) {
            if (arg.startsWith(SLOT_ROOT)) {
                root = arg.substring(SLOT_ROOT.length());
                continue;
            }
            if (arg.startsWith(SLOT_PATH)) {
                String path = arg.substring(SLOT_PATH.length());
                paths.add(path);
                continue;
            }
            arguments.add(arg);
        }
        new BootLauncher(root, paths).launch(arguments.toArray(new String[0]));
    }

    @Override
    protected ClassLoader createClassLoader(URL[] urls) throws Exception {
        LinkedHashSet<URL> slots = new LinkedHashSet<URL>(Arrays.asList(urls));
        for (String path : this.paths) {
            Enumeration<Resource> resources = Loaders.ant(Loaders.file(new File(this.root))).load(path);
            while (resources.hasMoreElements()) {
                Resource resource = resources.nextElement();
                slots.add(resource.getUrl());
            }
        }
        return super.createClassLoader(slots.toArray(new URL[0]));
    }
}
