/*
 * Decompiled with CFR 0.152.
 */
package io.slot;

import io.slot.Slotter;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;

public abstract class FileSlotter
implements Slotter {
    @Override
    public void slot(String src, String dest) throws IOException {
        this.slot(new File(src), new File(dest));
    }

    @Override
    public void slot(File src, File dest) throws IOException {
        try (FileInputStream in = new FileInputStream(src);
             FileOutputStream out = new FileOutputStream(dest);){
            this.slot(in, out);
        }
    }
}
