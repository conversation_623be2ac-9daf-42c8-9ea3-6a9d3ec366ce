/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.apache.commons.compress.archivers.ArchiveEntry
 *  org.apache.commons.compress.archivers.jar.JarArchiveEntry
 *  org.apache.commons.compress.archivers.jar.JarArchiveOutputStream
 */
package io.slot;

import io.loadkit.Loaders;
import io.loadkit.Resource;
import java.io.ByteArrayOutputStream;
import java.io.Closeable;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.OutputStreamWriter;
import java.io.Reader;
import java.io.Writer;
import java.util.Collections;
import java.util.Enumeration;
import java.util.HashSet;
import org.apache.commons.compress.archivers.ArchiveEntry;
import org.apache.commons.compress.archivers.jar.JarArchiveEntry;
import org.apache.commons.compress.archivers.jar.JarArchiveOutputStream;

public class IOKit {
    public static void embed(String ant, JarArchiveOutputStream zos) throws IOException {
        HashSet<String> directories = new HashSet<String>();
        Enumeration<Resource> resources = Loaders.ant().load(ant);
        while (resources.hasMoreElements()) {
            Resource resource = resources.nextElement();
            String name = resource.getName();
            String directory = name.substring(0, name.lastIndexOf(47) + 1);
            if (directories.add(directory)) {
                JarArchiveEntry xDirEntry = new JarArchiveEntry(directory);
                xDirEntry.setTime(System.currentTimeMillis());
                zos.putArchiveEntry((ArchiveEntry)xDirEntry);
                zos.closeArchiveEntry();
            }
            JarArchiveEntry xJarEntry = new JarArchiveEntry(name);
            xJarEntry.setTime(System.currentTimeMillis());
            zos.putArchiveEntry((ArchiveEntry)xJarEntry);
            try (InputStream ris = resource.getInputStream();){
                IOKit.transfer(ris, (OutputStream)zos);
            }
            zos.closeArchiveEntry();
        }
    }

    public static byte[] readln(InputStream in) throws IOException {
        int b = in.read();
        if (b == -1) {
            return null;
        }
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        while (b != -1) {
            switch (b) {
                case 13: {
                    break;
                }
                case 10: {
                    return bos.toByteArray();
                }
                default: {
                    bos.write(b);
                }
            }
            b = in.read();
        }
        return bos.toByteArray();
    }

    public static void writeln(OutputStream out, byte[] line) throws IOException {
        if (line == null) {
            return;
        }
        out.write(line);
        out.write(13);
        out.write(10);
    }

    public static void close(Closeable closeable) {
        try {
            IOKit.close(closeable, true);
        }
        catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public static void close(Closeable closeable, boolean quietly) throws IOException {
        block3: {
            if (closeable == null) {
                return;
            }
            try {
                closeable.close();
            }
            catch (IOException e) {
                if (quietly) break block3;
                throw e;
            }
        }
    }

    public static long transfer(InputStream in, OutputStream out) throws IOException {
        int length;
        long total = 0L;
        byte[] buffer = new byte[4096];
        while ((length = in.read(buffer)) != -1) {
            out.write(buffer, 0, length);
            total += (long)length;
        }
        out.flush();
        return total;
    }

    public static long transfer(Reader reader, Writer writer) throws IOException {
        int length;
        long total = 0L;
        char[] buffer = new char[4096];
        while ((length = reader.read(buffer)) != -1) {
            writer.write(buffer, 0, length);
            total += (long)length;
        }
        writer.flush();
        return total;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public static long transfer(InputStream in, File file) throws IOException {
        long l;
        FileOutputStream out = null;
        try {
            out = new FileOutputStream(file);
            l = IOKit.transfer(in, out);
        }
        catch (Throwable throwable) {
            IOKit.close(out);
            throw throwable;
        }
        IOKit.close(out);
        return l;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public static long transfer(Reader reader, File file) throws IOException {
        long l;
        FileOutputStream out = null;
        OutputStreamWriter writer = null;
        try {
            out = new FileOutputStream(file);
            writer = new OutputStreamWriter(out);
            l = IOKit.transfer(reader, writer);
        }
        catch (Throwable throwable) {
            IOKit.close(writer);
            IOKit.close(out);
            throw throwable;
        }
        IOKit.close(writer);
        IOKit.close(out);
        return l;
    }

    public static boolean delete(File file) {
        return IOKit.delete(file, false);
    }

    public static boolean delete(File file, boolean recursively) {
        if (file.isDirectory() && recursively) {
            boolean deleted = true;
            File[] files = file.listFiles();
            for (int i = 0; files != null && i < files.length; ++i) {
                deleted &= IOKit.delete(files[i], true);
            }
            return deleted && file.delete();
        }
        return file.delete();
    }

    public static boolean isRelative(String path) {
        return !IOKit.isAbsolute(path);
    }

    public static boolean isAbsolute(String path) {
        if (path.startsWith("/")) {
            return true;
        }
        HashSet roots = new HashSet();
        Collections.addAll(roots, File.listRoots());
        File root = new File(path);
        while (root.getParentFile() != null) {
            root = root.getParentFile();
        }
        return roots.contains(root);
    }

    public static String absolutize(String path) {
        return IOKit.normalize(IOKit.isAbsolute(path) ? path : System.getProperty("user.dir") + File.separator + path);
    }

    public static String normalize(String path) {
        return path.replaceAll("[/\\\\]+", "/");
    }
}
