/*
 * Decompiled with CFR 0.152.
 */
package com.fulongtech.tas.response;

public enum ResponseCode {
    SUCCESS(10001),
    FAIL(10002),
    USER_LOGGED_IN(10003),
    USER_NOT_LOGGED_IN(10004),
    USER_LOG_OUT(10005),
    USER_LOG_OUT_FAIL(10006);

    private Integer code;

    private ResponseCode(Integer code) {
        this.code = code;
    }

    public Integer getCode() {
        return this.code;
    }

    public String toString() {
        return Integer.toString(this.code);
    }
}
