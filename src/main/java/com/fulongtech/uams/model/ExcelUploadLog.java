/*
 * Decompiled with CFR 0.152.
 */
package com.fulongtech.uams.model;

import com.fulongtech.uams.model.ExcelLogType;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ExcelUploadLog {
    private int addNum = 0;
    private int updateNum = 0;
    private int failNum = 0;
    private Map<String, List<String>> error = new HashMap<String, List<String>>();
    private Map<String, List<String>> warn = new HashMap<String, List<String>>();
    private List<String> info = new ArrayList<String>(30);

    public int getAddNum() {
        return this.addNum;
    }

    public void setAddNum(int addNum) {
        this.addNum = addNum;
    }

    public int getUpdateNum() {
        return this.updateNum;
    }

    public void setUpdateNum(int updateNum) {
        this.updateNum = updateNum;
    }

    public int getFailNum() {
        return this.failNum;
    }

    public int addFailNum() {
        ++this.failNum;
        return this.failNum;
    }

    public void setFailNum(int failNum) {
        this.failNum = failNum;
    }

    public Map<String, List<String>> getError() {
        return this.error;
    }

    public void addError(ExcelLogType type, int index, String value) {
        switch (type) {
            case HEADER: {
                String header = "HEADER";
                if (this.error.containsKey(header)) {
                    this.error.get(header).add(value);
                    break;
                }
                ArrayList<String> values = new ArrayList<String>(5);
                values.add(value);
                this.error.put(header, values);
                break;
            }
            case LINE: {
                if (this.error.containsKey(String.valueOf(index))) {
                    this.error.get(String.valueOf(index)).add(value);
                    break;
                }
                ArrayList<String> values = new ArrayList<String>(5);
                values.add(value);
                this.error.put(String.valueOf(index), values);
                break;
            }
            case PERMISSION: {
                String permission = "PERMISSION";
                if (this.error.containsKey(permission)) {
                    this.error.get(permission).add(value);
                    break;
                }
                ArrayList<String> values = new ArrayList<String>(5);
                values.add(value);
                this.error.put(permission, values);
                break;
            }
            case TYPE: {
                String typeKey = "TYPE";
                this.error.put(typeKey, Arrays.asList(value));
                break;
            }
        }
    }

    public Map<String, List<String>> getWarn() {
        return this.warn;
    }

    public void addWarn(ExcelLogType type, int index, String value) {
        switch (type) {
            case HEADER: {
                String header = "HEADER";
                if (this.warn.containsKey(header)) {
                    this.warn.get(header).add(value);
                    break;
                }
                ArrayList<String> values = new ArrayList<String>(5);
                values.add(value);
                this.warn.put(header, values);
                break;
            }
            case LINE: {
                if (this.warn.containsKey(String.valueOf(index))) {
                    this.warn.get(String.valueOf(index)).add(value);
                    break;
                }
                ArrayList<String> values = new ArrayList<String>(5);
                values.add(value);
                this.warn.put(String.valueOf(index), values);
                break;
            }
            case PERMISSION: {
                String permission = "PERMISSION";
                if (this.warn.containsKey(permission)) {
                    this.warn.get(permission).add(value);
                    break;
                }
                ArrayList<String> values = new ArrayList<String>(5);
                values.add(value);
                this.warn.put(permission, values);
                break;
            }
        }
    }

    public List<String> getInfo() {
        return this.info;
    }

    public void setInfo(List<String> info) {
        this.info = info;
    }

    public void merge(ExcelUploadLog log) {
        for (Map.Entry<String, List<String>> map : log.getError().entrySet()) {
            if (this.getError().containsKey(map.getKey())) {
                this.getError().get(map.getKey()).addAll((Collection<String>)map.getValue());
                continue;
            }
            this.getError().put(map.getKey(), map.getValue());
        }
        for (Map.Entry<String, List<String>> map : log.getWarn().entrySet()) {
            if (this.getWarn().containsKey(map.getKey())) {
                this.getWarn().get(map.getKey()).addAll((Collection<String>)map.getValue());
                continue;
            }
            this.getWarn().put(map.getKey(), map.getValue());
        }
        for (String s : log.getInfo()) {
            this.getInfo().add(s);
        }
        this.failNum += log.failNum;
    }
}
