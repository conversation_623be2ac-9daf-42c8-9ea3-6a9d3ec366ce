/*
 * Decompiled with CFR 0.152.
 */
package com.fulongtech.uams.model;

import java.util.ArrayList;
import java.util.List;

public class UploadLog {
    private Integer addNum = 0;
    private Integer updateNum = 0;
    private Integer failNum = 0;
    private List<String> info = new ArrayList<String>();
    private List<String> warn = new ArrayList<String>();
    private List<String> error = new ArrayList<String>();

    public void merge(UploadLog add) {
        for (String s : add.getWarn()) {
            this.getWarn().add(s);
        }
        for (String s : add.getError()) {
            this.getError().add(s);
        }
        for (String s : add.getInfo()) {
            this.getInfo().add(s);
        }
    }

    public Integer getAddNum() {
        return this.addNum;
    }

    public void setAddNum(Integer addNum) {
        this.addNum = addNum;
    }

    public Integer getUpdateNum() {
        return this.updateNum;
    }

    public void setUpdateNum(Integer updateNum) {
        this.updateNum = updateNum;
    }

    public Integer getFailNum() {
        return this.failNum;
    }

    public void setFailNum(Integer failNum) {
        this.failNum = failNum;
    }

    public List<String> getInfo() {
        return this.info;
    }

    public void setInfo(List<String> info) {
        this.info = info;
    }

    public List<String> getWarn() {
        return this.warn;
    }

    public void setWarn(List<String> warn) {
        this.warn = warn;
    }

    public List<String> getError() {
        return this.error;
    }

    public void setError(List<String> error) {
        this.error = error;
    }
}
