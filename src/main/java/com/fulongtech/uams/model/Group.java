/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  com.baomidou.mybatisplus.annotation.IdType
 *  com.baomidou.mybatisplus.annotation.TableId
 *  com.baomidou.mybatisplus.annotation.TableName
 *  javax.validation.constraints.Size
 *  org.hibernate.validator.constraints.NotEmpty
 */
package com.fulongtech.uams.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import javax.validation.constraints.Size;
import org.hibernate.validator.constraints.NotEmpty;

@TableName(value="SYS_GROUP")
public class Group
implements Serializable {
    @TableId(type=IdType.ASSIGN_UUID)
    private String id;
    private String parentId;
    @Size(max=50, message="\u7528\u6237\u7ec4\u540d\u4e0d\u80fd\u8d85\u8fc750\u4e2a\u5b57\u7b26")
    @NotEmpty(message="\u7528\u6237\u7ec4\u540d\u4e0d\u80fd\u4e3a\u7a7a")
    private String name;
    private boolean isSysGroup;

    public String getId() {
        return this.id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getParentId() {
        return this.parentId;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    public String getName() {
        return this.name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public boolean isSysGroup() {
        return this.isSysGroup;
    }

    public void setSysGroup(boolean sysGroup) {
        this.isSysGroup = sysGroup;
    }
}
