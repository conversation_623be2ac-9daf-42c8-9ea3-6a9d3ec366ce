/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  com.baomidou.mybatisplus.annotation.FieldStrategy
 *  com.baomidou.mybatisplus.annotation.IdType
 *  com.baomidou.mybatisplus.annotation.TableField
 *  com.baomidou.mybatisplus.annotation.TableId
 *  com.baomidou.mybatisplus.annotation.TableName
 *  org.apache.ibatis.type.JdbcType
 */
package com.fulongtech.uams.model;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Objects;
import org.apache.ibatis.type.JdbcType;

@TableName(value="EVENT")
public class Event
implements Serializable {
    @TableId(type=IdType.ASSIGN_UUID)
    private String id;
    private String userId;
    private String groupId;
    private String ip;
    @TableField(insertStrategy=FieldStrategy.IGNORED, jdbcType=JdbcType.TIME)
    private Timestamp eventTime;
    private String eventType;

    public String getId() {
        return this.id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getUserId() {
        return this.userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getGroupId() {
        return this.groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }

    public String getIp() {
        return this.ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public Timestamp getEventTime() {
        return this.eventTime;
    }

    public void setEventTime(Timestamp eventTime) {
        this.eventTime = eventTime;
    }

    public String getEventType() {
        return this.eventType;
    }

    public void setEventType(String eventType) {
        this.eventType = eventType;
    }

    public String toString() {
        return "Event{id='" + this.id + '\'' + ", userId='" + this.userId + '\'' + ", groupId='" + this.groupId + '\'' + ", ip='" + this.ip + '\'' + ", eventTime=" + this.eventTime + ", eventType='" + this.eventType + '\'' + '}';
    }

    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || this.getClass() != o.getClass()) {
            return false;
        }
        Event event = (Event)o;
        return Objects.equals(this.id, event.id) && Objects.equals(this.userId, event.userId) && Objects.equals(this.groupId, event.groupId) && Objects.equals(this.ip, event.ip) && Objects.equals(this.eventTime, event.eventTime) && Objects.equals(this.eventType, event.eventType);
    }

    public int hashCode() {
        return Objects.hash(this.id, this.userId, this.groupId, this.ip, this.eventTime, this.eventType);
    }
}
