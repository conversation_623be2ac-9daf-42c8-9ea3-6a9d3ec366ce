/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  com.baomidou.mybatisplus.annotation.FieldStrategy
 *  com.baomidou.mybatisplus.annotation.IdType
 *  com.baomidou.mybatisplus.annotation.TableField
 *  com.baomidou.mybatisplus.annotation.TableId
 *  com.fasterxml.jackson.annotation.JsonIgnore
 *  javax.validation.constraints.Size
 *  org.apache.ibatis.type.Alias
 *  org.apache.ibatis.type.JdbcType
 *  org.springframework.data.annotation.Transient
 *  org.springframework.util.StringUtils
 */
package com.fulongtech.uams.model;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fulongtech.uams.annotation.ExcelProperty;
import com.fulongtech.uams.model.Group;
import java.io.Serializable;
import java.lang.reflect.Field;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import javax.validation.constraints.Size;
import org.apache.ibatis.type.Alias;
import org.apache.ibatis.type.JdbcType;
import org.springframework.data.annotation.Transient;
import org.springframework.util.StringUtils;

@Alias(value="SysUser")
public class SysUser
implements Serializable {
    private static final long serialVersionUID = 1L;
    @TableId(type=IdType.ASSIGN_UUID)
    private String id;
    @ExcelProperty(value="\u5bc6\u7801")
    @TableField(updateStrategy=FieldStrategy.NEVER)
    @JsonIgnore
    private String password;
    @ExcelProperty(value="\u7528\u6237\u540d", require=true)
    private String username;
    @ExcelProperty(value="\u59d3\u540d", require=true)
    @Size(min=2, max=20, message="\u59d3\u540d\u957f\u5ea6\u9700\u8981\u57282-20\u4e2a\u5b57\u7b26\u4e4b\u95f4")
    private String name;
    @ExcelProperty(value="\u90ae\u7bb1")
    @TableField(updateStrategy=FieldStrategy.IGNORED, jdbcType=JdbcType.VARCHAR)
    private String email;
    @ExcelProperty(value="\u5ea7\u673a\u53f7")
    @TableField(updateStrategy=FieldStrategy.IGNORED, jdbcType=JdbcType.VARCHAR)
    private String phone;
    @ExcelProperty(value="\u624b\u673a")
    @TableField(updateStrategy=FieldStrategy.IGNORED, jdbcType=JdbcType.VARCHAR)
    private String mobilePhone;
    @JsonIgnore
    private Timestamp changePwdTime;
    @ExcelProperty(value="\u8eab\u4efd\u8bc1")
    @TableField(updateStrategy=FieldStrategy.IGNORED, jdbcType=JdbcType.VARCHAR)
    private String idCard;
    @JsonIgnore
    private String createBy;
    @ExcelProperty(value="\u7528\u6237\u7ec4", multi=true)
    @TableField(exist=false)
    @Transient
    private List<Group> groups;
    @ExcelProperty(value="\u7528\u6237\u7ba1\u7406\u7684\u7528\u6237\u7ec4", multi=true)
    @TableField(exist=false)
    @Transient
    private List<String> managedGroups;

    public List<Group> getGroups() {
        return this.groups;
    }

    public void setGroups(List<Group> groups) {
        this.groups = groups;
    }

    public Timestamp getChangePwdTime() {
        return this.changePwdTime;
    }

    public void setChangePwdTime(Timestamp userChangePwdTime) {
        this.changePwdTime = userChangePwdTime;
    }

    public String getId() {
        return this.id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getPassword() {
        return this.password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getUsername() {
        return this.username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getName() {
        return this.name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getEmail() {
        return this.email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getPhone() {
        return this.phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getMobilePhone() {
        return this.mobilePhone;
    }

    public void setMobilePhone(String mobilePhone) {
        this.mobilePhone = mobilePhone;
    }

    public String getCreateBy() {
        return this.createBy;
    }

    public List<String> getManagedGroups() {
        return this.managedGroups;
    }

    public void setManagedGroups(List<String> managedGroups) {
        this.managedGroups = managedGroups;
    }

    public String toString() {
        return "SysUser{id='" + this.id + '\'' + ", password='" + this.password + '\'' + ", username='" + this.username + '\'' + ", name='" + this.name + '\'' + ", email='" + this.email + '\'' + ", phone='" + this.phone + '\'' + ", mobilePhone='" + this.mobilePhone + '\'' + ", changePwdTime=" + this.changePwdTime + ", idCard='" + this.idCard + '\'' + ", createBy='" + this.createBy + '\'' + ", groups=" + this.groups + ", managedGroups=" + this.managedGroups + '}';
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public String getIdCard() {
        return this.idCard;
    }

    public void setIdCard(String idCard) {
        this.idCard = idCard;
    }

    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || this.getClass() != o.getClass()) {
            return false;
        }
        SysUser sysUser = (SysUser)o;
        if (!this.id.equals(sysUser.id)) {
            return false;
        }
        return this.username.equals(sysUser.username);
    }

    public int hashCode() {
        int result = this.id == null ? 123 : this.id.hashCode();
        result = 31 * result + (this.username == null ? 234 : this.username.hashCode());
        return result;
    }

    public boolean isEmpty() {
        try {
            for (Field f : this.getClass().getDeclaredFields()) {
                if ("serialVersionUID".equals(f.getName())) continue;
                f.setAccessible(true);
                if (StringUtils.isEmpty((Object)f.get(this))) continue;
                return false;
            }
        }
        catch (IllegalAccessException e) {
            e.printStackTrace();
        }
        return true;
    }

    public boolean addGroupIdToManagedGroups(String groupId) {
        if (this.managedGroups == null) {
            this.managedGroups = new ArrayList<String>();
        }
        return this.managedGroups.add(groupId);
    }
}
