/*
 * Decompiled with CFR 0.152.
 */
package com.fulongtech.uams.model;

import com.fulongtech.uams.model.ExcelLogType;

static class ExcelUploadLog.1 {
    static final /* synthetic */ int[] $SwitchMap$com$fulongtech$uams$model$ExcelLogType;

    static {
        $SwitchMap$com$fulongtech$uams$model$ExcelLogType = new int[ExcelLogType.values().length];
        try {
            ExcelUploadLog.1.$SwitchMap$com$fulongtech$uams$model$ExcelLogType[ExcelLogType.HEADER.ordinal()] = 1;
        }
        catch (NoSuchFieldError noSuchFieldError) {
            // empty catch block
        }
        try {
            ExcelUploadLog.1.$SwitchMap$com$fulongtech$uams$model$ExcelLogType[ExcelLogType.LINE.ordinal()] = 2;
        }
        catch (NoSuchFieldError noSuchFieldError) {
            // empty catch block
        }
        try {
            ExcelUploadLog.1.$SwitchMap$com$fulongtech$uams$model$ExcelLogType[ExcelLogType.PERMISSION.ordinal()] = 3;
        }
        catch (NoSuchFieldError noSuchFieldError) {
            // empty catch block
        }
        try {
            ExcelUploadLog.1.$SwitchMap$com$fulongtech$uams$model$ExcelLogType[ExcelLogType.TYPE.ordinal()] = 4;
        }
        catch (NoSuchFieldError noSuchFieldError) {
            // empty catch block
        }
    }
}
