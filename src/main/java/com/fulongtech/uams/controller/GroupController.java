/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.servlet.http.HttpServletRequest
 *  javax.validation.Valid
 *  org.springframework.beans.factory.annotation.Autowired
 *  org.springframework.context.MessageSource
 *  org.springframework.util.StringUtils
 *  org.springframework.validation.BindingResult
 *  org.springframework.validation.ObjectError
 *  org.springframework.web.bind.annotation.DeleteMapping
 *  org.springframework.web.bind.annotation.GetMapping
 *  org.springframework.web.bind.annotation.PathVariable
 *  org.springframework.web.bind.annotation.PostMapping
 *  org.springframework.web.bind.annotation.PutMapping
 *  org.springframework.web.bind.annotation.RequestBody
 *  org.springframework.web.bind.annotation.RequestMapping
 *  org.springframework.web.bind.annotation.RestController
 *  org.springframework.web.servlet.support.RequestContextUtils
 */
package com.fulongtech.uams.controller;

import com.fulongtech.uams.aspect.SysManagerPermission;
import com.fulongtech.uams.exception.ErrorCode;
import com.fulongtech.uams.exception.UamsException;
import com.fulongtech.uams.model.Group;
import com.fulongtech.uams.model.SysUser;
import com.fulongtech.uams.service.GroupService;
import com.fulongtech.uams.service.RelationService;
import com.fulongtech.uams.service.UserService;
import java.util.Comparator;
import java.util.List;
import java.util.Locale;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.util.StringUtils;
import org.springframework.validation.BindingResult;
import org.springframework.validation.ObjectError;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.support.RequestContextUtils;

@RestController
@RequestMapping(value={"/groups"})
public class GroupController {
    @Resource
    private GroupService groupService;
    @Resource
    private UserService userService;
    @Resource
    private RelationService relationService;
    @Autowired
    private MessageSource messageSource;
    private static final String SYSTEM_GROUP_ID = "BEFE2FF1A20F4C419C8A94B7213C5218";
    private static final String SYSTEM_GROUP_NAME = "\u7cfb\u7edf\u7ba1\u7406\u7ec4";
    private static final String ADMIN_ID = "BEFE2FF1A20F4C419C8A94B7213C5219";
    private static final String CHARACTER_REGEX = "[`~!@$%^&*()+=|{}':;',\\[\\]\\\\.<>?~\uff01@\uffe5%\u2026\u2026&*\uff08\uff09\u2014\u2014+|{}\u3010\u3011\u2018\uff1b\uff1a\u201d\u201c\u2019\u3002\uff0c\u3001\uff1f]";
    private static final int NAME_LENGTH = 20;
    private static final String EMPTY_GROUP_ID = "990F7CEB6F22435BAB2D67BADF18D470";

    @GetMapping
    public List<Group> groups() {
        List<Group> groups = this.groupService.getGroups().stream().sorted(new Comparator<Group>(){

            @Override
            public int compare(Group o1, Group o2) {
                if (GroupController.SYSTEM_GROUP_NAME.equals(o1.getName())) {
                    return -1;
                }
                if (GroupController.SYSTEM_GROUP_NAME.equals(o2.getName())) {
                    return 1;
                }
                return o1.getName().compareTo(o2.getName());
            }
        }).collect(Collectors.toList());
        return groups;
    }

    @PostMapping
    @SysManagerPermission.HasPermission(value="\u6dfb\u52a0\u7528\u6237\u7ec4")
    public String addGroup(@RequestBody @Valid Group group, BindingResult bindingResult, HttpServletRequest request) {
        Locale locale = RequestContextUtils.getLocale((HttpServletRequest)request);
        if (bindingResult.hasErrors()) {
            throw new UamsException(ErrorCode.UNPROCESABLE_ENTITY, ((ObjectError)bindingResult.getAllErrors().get(0)).getDefaultMessage());
        }
        if (SYSTEM_GROUP_ID.equals(group.getParentId())) {
            throw new UamsException(ErrorCode.FORBIDDEN_OPERATE, this.messageSource.getMessage("group.controller.permission.add.child", null, locale));
        }
        group.setId(null);
        if (this.groupService.getGroupByName(group.getName()) != null) {
            throw new UamsException(ErrorCode.RESOURCE_ALREADY_EXISTS, this.messageSource.getMessage("group.controller.group.exist.pre", null, locale) + group.getName() + this.messageSource.getMessage("group.controller.group.exist.order", null, locale));
        }
        if (!StringUtils.isEmpty((Object)group.getParentId()) && this.groupService.getGroupById(group.getParentId()) == null) {
            throw new UamsException(ErrorCode.UNPROCESABLE_ENTITY, this.messageSource.getMessage("group.controller.group.value.empty", null, locale));
        }
        return this.groupService.add(group).getId();
    }

    @DeleteMapping
    @SysManagerPermission.HasPermission(value="\u5220\u9664\u7528\u6237\u7ec4")
    public void deleteGroup(@RequestBody List<String> groupIds, HttpServletRequest request) {
        if (groupIds.contains(SYSTEM_GROUP_ID)) {
            Locale locale = RequestContextUtils.getLocale((HttpServletRequest)request);
            throw new UamsException(ErrorCode.FORBIDDEN_OPERATE, this.messageSource.getMessage("group.controller.system.group.id", null, locale));
        }
        if (groupIds.size() > 0) {
            this.groupService.deleteGroups(groupIds);
        }
    }

    @PutMapping(value={"/info"})
    @Deprecated
    public Group modifyGroup(@RequestBody @Valid Group group, BindingResult bindingResult, HttpServletRequest request) {
        Locale locale = RequestContextUtils.getLocale((HttpServletRequest)request);
        if (bindingResult.hasErrors()) {
            throw new UamsException(ErrorCode.UNPROCESABLE_ENTITY, ((ObjectError)bindingResult.getAllErrors().get(0)).getDefaultMessage());
        }
        if (StringUtils.isEmpty((Object)group.getId())) {
            throw new UamsException(ErrorCode.UNPROCESABLE_ENTITY, this.messageSource.getMessage("group.controller.name.empty", null, locale));
        }
        Group oldGroup = this.groupService.getGroupById(group.getId());
        if (oldGroup == null) {
            throw new UamsException(ErrorCode.RESOURCE_NOT_FOUND, this.messageSource.getMessage("group.controller.group.value.empty", null, locale));
        }
        Group groupByName = this.groupService.getGroupByName(group.getName());
        if (groupByName != null && !groupByName.getId().equals(group.getId())) {
            throw new UamsException(ErrorCode.RESOURCE_ALREADY_EXISTS, this.messageSource.getMessage("group.controller.group.exist.pre", null, locale) + group.getName() + this.messageSource.getMessage("group.controller.group.exist.order", null, locale));
        }
        return this.groupService.updateGroupInfo(group);
    }

    @GetMapping(value={"/{groupId}"})
    public Group getGroupById(@PathVariable(value="groupId") String groupId, HttpServletRequest request) {
        Group group = this.groupService.getGroupById(groupId);
        if (group == null) {
            throw new UamsException(ErrorCode.RESOURCE_NOT_FOUND, this.messageSource.getMessage("group.controller.group.value.empty", null, RequestContextUtils.getLocale((HttpServletRequest)request)));
        }
        return group;
    }

    @GetMapping(value={"/{groupId}/users"})
    public List<SysUser> getUsersByGroupId(@PathVariable(value="groupId") String groupId, HttpServletRequest request) {
        if (EMPTY_GROUP_ID.equals(groupId)) {
            return this.userService.getSysUsersWithoutGroups();
        }
        if (this.groupService.getGroupById(groupId) == null) {
            throw new UamsException(ErrorCode.RESOURCE_NOT_FOUND, this.messageSource.getMessage("group.controller.group.value.empty", null, RequestContextUtils.getLocale((HttpServletRequest)request)));
        }
        return this.groupService.getGroupUser(groupId);
    }

    @PostMapping(value={"/users"})
    public List<SysUser> getUsersByGroupIds(String groupIds, HttpServletRequest request) {
        String[] ids = groupIds.split("\\,");
        return this.groupService.getUsersByGroupIds(ids);
    }

    @GetMapping(value={"/{groupId}/users/withGroups"})
    public List<SysUser> getUsersWithGroupsByGroupId(@PathVariable(value="groupId") String groupId, HttpServletRequest request) {
        Locale locale = RequestContextUtils.getLocale((HttpServletRequest)request);
        if (EMPTY_GROUP_ID.equals(groupId)) {
            return this.userService.getSysUsersWithoutGroups();
        }
        if (this.groupService.getGroupById(groupId) == null) {
            throw new UamsException(ErrorCode.RESOURCE_NOT_FOUND, this.messageSource.getMessage("group.controller.group.id.empty.pre", null, locale) + groupId + this.messageSource.getMessage("group.controller.group.id.empty.order", null, locale));
        }
        List<SysUser> users = this.groupService.getGroupUser(groupId);
        SysUser currentUser = (SysUser)request.getSession().getAttribute("currentUser");
        return this.userService.getSysUsersWithGroups(users, currentUser);
    }

    private boolean checkName(String name) {
        Pattern p = Pattern.compile(CHARACTER_REGEX);
        return !p.matcher(name).find();
    }

    @PutMapping
    @SysManagerPermission.HasPermission(value="\u66f4\u65b0\u7528\u6237\u7ec4")
    public void updateGroup(@RequestBody @Valid Group group, BindingResult bindingResult, HttpServletRequest request) {
        Locale locale = RequestContextUtils.getLocale((HttpServletRequest)request);
        if (bindingResult.hasErrors()) {
            throw new UamsException(ErrorCode.UNPROCESABLE_ENTITY, ((ObjectError)bindingResult.getAllErrors().get(0)).getDefaultMessage());
        }
        if (StringUtils.isEmpty((Object)group.getId())) {
            throw new UamsException(ErrorCode.RESOURCE_NOT_FOUND, this.messageSource.getMessage("group.controller.group.value.empty", null, locale));
        }
        if (SYSTEM_GROUP_ID.equals(group.getId())) {
            throw new UamsException(ErrorCode.FORBIDDEN_OPERATE, this.messageSource.getMessage("group.controller.permission.modify", null, locale));
        }
        group.setParentId(null);
        Group oldGroup = this.groupService.getGroupById(group.getId());
        if (oldGroup == null) {
            throw new UamsException(ErrorCode.RESOURCE_NOT_FOUND, this.messageSource.getMessage("group.controller.group.value.empty", null, locale));
        }
        Group tempGroup = this.groupService.getGroupByName(group.getName());
        if (tempGroup != null) {
            throw new UamsException(ErrorCode.RESOURCE_ALREADY_EXISTS, this.messageSource.getMessage("group.controller.group.exist.pre", null, locale) + tempGroup.getName() + this.messageSource.getMessage("group.controller.group.exist.order", null, locale));
        }
        this.groupService.updateGroupInfo(group);
    }

    @GetMapping(value={"/{groupId}/offspring"})
    public List<Group> getGroupChildrenByGroupId(@PathVariable(value="groupId") String groupId, HttpServletRequest request) {
        if (this.groupService.getGroupById(groupId) == null) {
            throw new UamsException(ErrorCode.RESOURCE_NOT_FOUND, this.messageSource.getMessage("group.controller.group.value.empty", null, RequestContextUtils.getLocale((HttpServletRequest)request)));
        }
        return this.groupService.getGroupChildren(groupId);
    }

    @PostMapping(value={"/{groupId}/users"})
    @SysManagerPermission.HasPermission(value="\u7ed9\u7528\u6237\u7ec4\u5206\u914d\u7528\u6237")
    public void distributeRelation(@PathVariable(value="groupId") String groupId, @RequestBody List<String> userIds, HttpServletRequest request) {
        Locale locale = RequestContextUtils.getLocale((HttpServletRequest)request);
        SysUser currentUser = (SysUser)request.getSession().getAttribute("currentUser");
        if (SYSTEM_GROUP_ID.equals(groupId) && !ADMIN_ID.equals(currentUser.getId())) {
            throw new UamsException(ErrorCode.UNAUTHORIZED, this.messageSource.getMessage("group.controller.permission.distribute.user", null, locale));
        }
        if (userIds.size() == 0) {
            throw new UamsException(ErrorCode.UNPROCESABLE_ENTITY, this.messageSource.getMessage("group.controller.users.empty", null, locale));
        }
        Group group = this.groupService.getGroupById(groupId);
        if (group == null) {
            throw new UamsException(ErrorCode.RESOURCE_NOT_FOUND, this.messageSource.getMessage("group.controller.group.value.empty", null, locale));
        }
        for (int i = 0; i < userIds.size(); ++i) {
            SysUser sysUser = this.userService.getById(userIds.get(i));
            if (sysUser != null) continue;
            throw new UamsException(ErrorCode.RESOURCE_NOT_FOUND, this.messageSource.getMessage("group.controller.user.empty", null, locale));
        }
        this.relationService.addRelations(groupId, userIds);
    }

    @DeleteMapping(value={"/{groupId}/users"})
    @SysManagerPermission.HasPermission(value="\u5220\u9664\u7528\u6237\u7ec4\u4e2d\u7684\u7528\u6237")
    public void deleteRelation(@PathVariable(value="groupId") String groupId, @RequestBody List<String> userIds, HttpServletRequest request) {
        Locale locale = RequestContextUtils.getLocale((HttpServletRequest)request);
        SysUser currentUser = (SysUser)request.getSession().getAttribute("currentUser");
        if (SYSTEM_GROUP_ID.equals(groupId) && !ADMIN_ID.equals(currentUser.getId())) {
            throw new UamsException(ErrorCode.FORBIDDEN_OPERATE, this.messageSource.getMessage("group.controller.permission.relationship.cancel", null, locale));
        }
        for (int i = 0; i < userIds.size(); ++i) {
            if (SYSTEM_GROUP_ID.equals(groupId) && ADMIN_ID.equals(userIds.get(i))) {
                throw new UamsException(ErrorCode.FORBIDDEN_OPERATE, this.messageSource.getMessage("group.controller.admin.group.system", null, locale));
            }
            this.relationService.deleteRelationByGroupIdAndUserId(groupId, userIds.get(i));
        }
    }
}
