/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  cn.hutool.core.util.ObjectUtil
 *  com.google.common.collect.Maps
 *  com.google.gson.Gson
 *  javax.servlet.http.Cookie
 *  javax.servlet.http.HttpServletRequest
 *  javax.servlet.http.HttpServletResponse
 *  org.apache.commons.lang3.StringUtils
 *  org.apache.log4j.LogManager
 *  org.apache.log4j.Logger
 *  org.apache.shiro.SecurityUtils
 *  org.apache.shiro.authc.AuthenticationException
 *  org.apache.shiro.authc.AuthenticationToken
 *  org.apache.shiro.authc.ExcessiveAttemptsException
 *  org.apache.shiro.authc.IncorrectCredentialsException
 *  org.apache.shiro.authc.LockedAccountException
 *  org.apache.shiro.authc.UsernamePasswordToken
 *  org.apache.shiro.subject.Subject
 *  org.crazycake.shiro.RedisCache
 *  org.springframework.beans.factory.annotation.Autowired
 *  org.springframework.beans.factory.annotation.Value
 *  org.springframework.context.MessageSource
 *  org.springframework.session.Session
 *  org.springframework.session.data.redis.RedisOperationsSessionRepository
 *  org.springframework.stereotype.Controller
 *  org.springframework.web.bind.annotation.GetMapping
 *  org.springframework.web.bind.annotation.PostMapping
 *  org.springframework.web.bind.annotation.RequestBody
 *  org.springframework.web.bind.annotation.RequestMapping
 *  org.springframework.web.bind.annotation.RequestMethod
 *  org.springframework.web.bind.annotation.RequestParam
 *  org.springframework.web.bind.annotation.ResponseBody
 *  org.springframework.web.servlet.LocaleResolver
 *  org.springframework.web.servlet.ModelAndView
 *  org.springframework.web.servlet.View
 *  org.springframework.web.servlet.support.RequestContextUtils
 *  org.springframework.web.servlet.view.RedirectView
 */
package com.fulongtech.uams.controller;

import cn.hutool.core.util.ObjectUtil;
import com.fulongtech.tas.response.ResponseCode;
import com.fulongtech.tas.response.ResponseMsg;
import com.fulongtech.uams.exception.ErrorCode;
import com.fulongtech.uams.exception.ExceptionHandler;
import com.fulongtech.uams.exception.UamsException;
import com.fulongtech.uams.model.Group;
import com.fulongtech.uams.model.SysUser;
import com.fulongtech.uams.properties.LoginPage;
import com.fulongtech.uams.properties.SecurityProper;
import com.fulongtech.uams.properties.SmsLoginProper;
import com.fulongtech.uams.properties.SystemPassword;
import com.fulongtech.uams.properties.ThirdAuthenticationProper;
import com.fulongtech.uams.service.EventService;
import com.fulongtech.uams.service.GroupService;
import com.fulongtech.uams.service.RelationService;
import com.fulongtech.uams.service.UserService;
import com.fulongtech.uams.service.impl.RedisServiceImpl;
import com.fulongtech.uams.shiro.cache.RetryCache;
import com.fulongtech.uams.shiro.token.UamsAuthenticationToken;
import com.fulongtech.uams.sms.VerificationCodeInfo;
import com.fulongtech.uams.util.Base64Decode;
import com.fulongtech.uams.util.DESUtil;
import com.fulongtech.uams.util.IPUtil;
import com.fulongtech.uams.util.JwtUtils;
import com.fulongtech.uams.util.LoginKeySingleton;
import com.fulongtech.uams.util.Md5Util;
import com.fulongtech.uams.util.RSAUtil;
import com.fulongtech.uams.util.SessionUtil;
import com.fulongtech.uams.util.SysUserValidator;
import com.fulongtech.uams.util.VerificationCodeUtil;
import com.fulongtech.uams.util.redis.RedisSessionForUser;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import java.io.IOException;
import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.lang.reflect.Method;
import java.net.URLDecoder;
import java.time.Instant;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import javax.annotation.Resource;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.LogManager;
import org.apache.log4j.Logger;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authc.AuthenticationException;
import org.apache.shiro.authc.AuthenticationToken;
import org.apache.shiro.authc.ExcessiveAttemptsException;
import org.apache.shiro.authc.IncorrectCredentialsException;
import org.apache.shiro.authc.LockedAccountException;
import org.apache.shiro.authc.UsernamePasswordToken;
import org.apache.shiro.subject.Subject;
import org.crazycake.shiro.RedisCache;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.MessageSource;
import org.springframework.session.Session;
import org.springframework.session.data.redis.RedisOperationsSessionRepository;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.LocaleResolver;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.View;
import org.springframework.web.servlet.support.RequestContextUtils;
import org.springframework.web.servlet.view.RedirectView;

@Controller
public class IndexController
implements Serializable {
    private static final long serialVersionUID = 1L;
    @Autowired
    private MessageSource messageSource;
    @Autowired
    private RedisCache retryCache;
    @Resource
    private UserService userService;
    @Resource
    private GroupService groupService;
    @Resource
    private RelationService relationService;
    @Resource
    private EventService eventService;
    @Resource
    private DESUtil desUtil;
    @Resource
    private SystemPassword systemPassword;
    @Resource
    private SecurityProper securityProper;
    @Value(value="${uams.redis.maxInactiveIntervalInSeconds}")
    private String maxInactiveIntervalInSeconds;
    @Resource
    private RedisOperationsSessionRepository redisOperationsSessionRepository;
    private Object loginLock = new Object();
    @Resource
    private ThirdAuthenticationProper thirdAuthenticationProper;
    @Resource
    private SessionUtil sessionUtil;
    @Resource
    private SmsLoginProper smsLoginProper;
    @Autowired
    private LocaleResolver localeResolver;
    @Resource
    private RedisServiceImpl redisService;
    private RetryCache passwordRetryCache;
    private static final Logger log = LogManager.getLogger(IndexController.class);
    private static final String UPDATE_PASSWORD = "updatePassword";
    private static final String FORCE_CHANGE_PASSWORD = "changePassword";
    private static final String RETURN_URL = "returnUrl";
    private static final String KICK_OUT = "kickout";
    private static final String CURRENT_USER = "currentUser";
    private static final String EVENT_TYPE = "LOGIN";
    @Value(value="${uams.user.enableSynchronize:false}")
    private boolean enableSynchronize;
    @Value(value="${uams.security-policy.token-expire-time}")
    private long tokenExpires;
    private final List<String> supportLang = Arrays.asList("zh_CN", "ru_RU", "en_US");

    @RequestMapping(value={"index"})
    public ModelAndView index(HttpServletRequest request, String returnURL) throws UnsupportedEncodingException {
        if (SecurityUtils.getSubject().getPrincipal() != null) {
            String userName = SecurityUtils.getSubject().getPrincipal().toString();
            SysUser user = this.userService.findByUsername(userName);
            List<Group> groups = this.groupService.getGroupByUserId(user.getId());
            user.setGroups(groups);
            request.getSession().setAttribute(CURRENT_USER, (Object)user);
        }
        if (returnURL != null && !"".equals(returnURL)) {
            return new ModelAndView((View)new RedirectView(URLDecoder.decode(returnURL, "UTF-8")));
        }
        return new ModelAndView("index");
    }

    @RequestMapping(value={"ownAuthentication"})
    public ModelAndView ownAuthentication(HttpServletRequest request, String returnURL) {
        request.getSession().setAttribute("enableCaptcha", (Object)this.securityProper.isEnableCaptcha());
        return new ModelAndView(LoginPage.loginPage).addObject("returnURL", (Object)returnURL);
    }

    @RequestMapping(value={"login"}, method={RequestMethod.GET})
    public ModelAndView toLogin(HttpServletResponse response, HttpServletRequest request, String order) throws UnsupportedEncodingException {
        String returnURL = "";
        Cookie[] cookies = request.getCookies();
        if (cookies != null) {
            for (Cookie cookie : cookies) {
                if (!RETURN_URL.equals(cookie.getName())) continue;
                returnURL = cookie.getValue();
                break;
            }
        }
        if (!StringUtils.isEmpty((CharSequence)request.getParameter(RETURN_URL))) {
            returnURL = request.getParameter(RETURN_URL);
        }
        String kickout = request.getParameter(KICK_OUT);
        if (request.getSession().getAttribute(CURRENT_USER) != null || SecurityUtils.getSubject().isAuthenticated()) {
            if (kickout != null) {
                SecurityUtils.getSubject().logout();
                request.getSession().invalidate();
            } else {
                if (returnURL == null || "".equals(returnURL)) {
                    return new ModelAndView((View)new RedirectView(request.getContextPath() + "/"));
                }
                SysUser user = this.userService.findByUsername(SecurityUtils.getSubject().getPrincipal().toString());
                if (user == null) {
                    user = new SysUser();
                    user.setUsername(SecurityUtils.getSubject().getPrincipal().toString());
                }
                return new ModelAndView((View)new RedirectView(URLDecoder.decode(returnURL, "UTF-8")));
            }
        }
        if (this.thirdAuthenticationProper.getAuthenticationType().contains("tas-jar")) {
            try {
                Class<?> TASService = Class.forName("com.fulongtech.tas.service.TASServiceImpl");
                Method auth = TASService.getDeclaredMethod("check", HttpServletRequest.class, HttpServletResponse.class);
                ResponseMsg authMsg = (ResponseMsg)auth.invoke(TASService.newInstance(), request, response);
                if (ResponseCode.USER_LOGGED_IN.getCode().equals(authMsg.getCode())) {
                    HashMap data = (HashMap)authMsg.getData();
                    String username = (String)data.get("username");
                    return this.setSession(username, request, response, returnURL);
                }
                if (ResponseCode.USER_NOT_LOGGED_IN.getCode().equals(authMsg.getCode())) {
                    HashMap data = (HashMap)authMsg.getData();
                    boolean bool = (Boolean)data.get("hasLoginPage");
                    if (bool) {
                        String thirdLoginUrl = (String)data.get("url");
                        if (StringUtils.isEmpty((CharSequence)thirdLoginUrl)) {
                            System.out.println("\u7b2c\u4e09\u65b9\u767b\u5f55\u5730\u5740\u4e3a\u7a7a\uff0c\u8bf7\u68c0\u67e5tas.check()\u65b9\u6cd5");
                            throw new Exception("\u7b2c\u4e09\u65b9\u767b\u5f55\u5730\u5740\u4e3a\u7a7a\uff01");
                        }
                        this.transfer(request, response, thirdLoginUrl);
                        return null;
                    }
                    request.getSession().setAttribute("enableCaptcha", (Object)this.securityProper.isEnableCaptcha());
                    request.getSession().setAttribute("enableRememberMe", (Object)this.securityProper.isEnableRememberMe());
                    request.getSession().setAttribute("enableSmsLogin", (Object)this.smsLoginProper.isEnableSmsLogin());
                    request.getSession().setAttribute("enableHumanVerification", (Object)this.smsLoginProper.isEnableHumanVerification());
                    request.getSession().setAttribute("smsTimeInterval", (Object)this.smsLoginProper.getTimeInterval());
                    return new ModelAndView(LoginPage.loginPage).addObject("maxInactiveIntervalInSeconds", (Object)this.maxInactiveIntervalInSeconds);
                }
            }
            catch (Exception e) {
                e.printStackTrace();
            }
            return null;
        }
        if (SecurityUtils.getSubject().isRemembered() && this.securityProper.isEnableRememberMe()) {
            return this.setSession(SecurityUtils.getSubject().getPrincipal().toString(), request, response, returnURL);
        }
        request.getSession().setAttribute("enableCaptcha", (Object)this.securityProper.isEnableCaptcha());
        request.getSession().setAttribute("enableRememberMe", (Object)this.securityProper.isEnableRememberMe());
        request.getSession().setAttribute("enableSmsLogin", (Object)this.smsLoginProper.isEnableSmsLogin());
        request.getSession().setAttribute("enableHumanVerification", (Object)this.smsLoginProper.isEnableHumanVerification());
        request.getSession().setAttribute("smsTimeInterval", (Object)this.smsLoginProper.getTimeInterval());
        HashMap<String, String> param = new HashMap<String, String>(1);
        if (!StringUtils.isEmpty((CharSequence)order)) {
            param.put("order", order);
        }
        return new ModelAndView(LoginPage.loginPage, param).addObject("maxInactiveIntervalInSeconds", (Object)this.maxInactiveIntervalInSeconds);
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @RequestMapping(value={"login"}, method={RequestMethod.POST})
    public ModelAndView login(@RequestParam(value="username") String username, @RequestParam(value="password") String password, @RequestParam(value="rememberMe", required=false) String rememberMe, String returnURL, HttpServletResponse response, HttpServletRequest request) throws Exception {
        Object auth;
        Locale locale = RequestContextUtils.getLocale((HttpServletRequest)request);
        String usernameInStack = username;
        String passwordInStack = password;
        LoginKeySingleton loginKeySingleton = LoginKeySingleton.getInstance();
        ConcurrentHashMap<String, LoginKeySingleton.CacheProper> keyMap = loginKeySingleton.getMap();
        LoginKeySingleton.CacheProper cacheProper = keyMap.get(request.getSession().getId());
        if (cacheProper == null) {
            request.setAttribute("fail", (Object)this.messageSource.getMessage("login.error.networkStack", null, locale));
            request.getSession().setAttribute("enableCaptcha", (Object)this.securityProper.isEnableCaptcha());
            request.getSession().setAttribute("enableRememberMe", (Object)this.securityProper.isEnableRememberMe());
            request.getSession().setAttribute("enableSmsLogin", (Object)this.smsLoginProper.isEnableSmsLogin());
            request.getSession().setAttribute("enableHumanVerification", (Object)this.smsLoginProper.isEnableHumanVerification());
            request.getSession().setAttribute("smsTimeInterval", (Object)this.smsLoginProper.getTimeInterval());
            return new ModelAndView(LoginPage.loginPage).addObject(RETURN_URL, (Object)returnURL);
        }
        String key = cacheProper.getValue();
        if (passwordInStack != null) {
            passwordInStack = DESUtil.decryptContent(passwordInStack, key);
        }
        Subject currUser = SecurityUtils.getSubject();
        if (this.thirdAuthenticationProper.getAuthenticationType().contains("tas-jar")) {
            try {
                Class<?> TASService = Class.forName("com.fulongtech.tas.service.TASServiceImpl");
                auth = TASService.getDeclaredMethod("auth", String.class, String.class, HttpServletRequest.class, HttpServletResponse.class);
                Cookie[] responseMsg = (Cookie[])((Method)auth).invoke(TASService.newInstance(), usernameInStack, passwordInStack, request, response);
                Integer code = responseMsg.getCode();
                if (ResponseCode.SUCCESS.getCode().equals(code)) {
                    HashMap data = (HashMap)responseMsg.getData();
                    String uamsUsername = (String)data.get("username");
                    return this.loginSucceed(returnURL, response, request, currUser, uamsUsername);
                }
                if (ResponseCode.FAIL.getCode().equals(code)) {
                    HashMap data = (HashMap)responseMsg.getData();
                    String error = (String)data.get("error");
                    request.setAttribute("fail", (Object)error);
                    request.getSession().setAttribute("enableCaptcha", (Object)this.securityProper.isEnableCaptcha());
                    request.getSession().setAttribute("enableRememberMe", (Object)this.securityProper.isEnableRememberMe());
                    request.getSession().setAttribute("enableSmsLogin", (Object)this.smsLoginProper.isEnableSmsLogin());
                    request.getSession().setAttribute("enableHumanVerification", (Object)this.smsLoginProper.isEnableHumanVerification());
                    request.getSession().setAttribute("smsTimeInterval", (Object)this.smsLoginProper.getTimeInterval());
                    return new ModelAndView(LoginPage.loginPage).addObject(RETURN_URL, (Object)returnURL);
                }
            }
            catch (Exception e) {
                e.printStackTrace();
            }
        }
        assert (passwordInStack != null);
        passwordInStack = Base64Decode.unescape(passwordInStack);
        UsernamePasswordToken token = new UsernamePasswordToken(usernameInStack, Md5Util.getMD5(passwordInStack));
        token.setRememberMe(new Boolean(rememberMe).booleanValue());
        try {
            Cookie[] cookies;
            auth = this.loginLock;
            synchronized (auth) {
                currUser.login((AuthenticationToken)token);
                this.sessionUtil.kicOut();
            }
            log.info((Object)(SecurityUtils.getSubject().getPrincipal().toString() + "\uff1a\u767b\u5f55\u6210\u529f\u3002"));
            loginKeySingleton.remove(key);
            for (Cookie cookie : cookies = request.getCookies()) {
                if (!RETURN_URL.equals(cookie.getName())) continue;
                String newUrl = cookie.getValue();
                return this.setSession(usernameInStack, request, response, newUrl);
            }
            return this.setSession(usernameInStack, request, response, returnURL);
        }
        catch (AuthenticationException e) {
            if (e instanceof ExcessiveAttemptsException) {
                request.setAttribute("fail", (Object)(this.messageSource.getMessage("login.error.retryLoginStart", null, locale) + this.systemPassword.getRetryCount() + " " + this.messageSource.getMessage("login.error.retryLoginMid", null, locale) + e.getMessage() + " " + this.messageSource.getMessage("login.error.retryLoginEnd", null, locale)));
            } else if (e instanceof LockedAccountException) {
                request.setAttribute("fail", (Object)this.messageSource.getMessage("login.error.locked", null, locale));
            } else if (e instanceof IncorrectCredentialsException) {
                this.passwordRetryCache = (RetryCache)this.retryCache.get((Object)usernameInStack);
                Integer errorCount = this.systemPassword.getRetryCount() - this.passwordRetryCache.getRetryCount().get();
                request.setAttribute("fail", (Object)(this.messageSource.getMessage("login.error.passwordStart", null, locale) + errorCount + this.messageSource.getMessage("login.error.passwordEnd", null, locale)));
            } else {
                Throwable cause = e.getCause();
                if (cause != null) {
                    String reason = ExceptionHandler.DataBaseException(cause);
                    if (!StringUtils.isEmpty((CharSequence)reason)) {
                        request.setAttribute("fail", (Object)this.messageSource.getMessage("database-error", null, locale));
                    } else {
                        log.error((Object)"\u672a\u77e5\u9519\u8bef\uff01\u8bf7\u8bb0\u5f55\u73af\u5883\u4fe1\u606f\uff0c\u8054\u7cfbUAMS\u7684\u5f00\u53d1");
                        request.setAttribute("fail", (Object)this.messageSource.getMessage("unknown-error", null, locale));
                        e.printStackTrace();
                    }
                } else {
                    log.error((Object)"\u672a\u77e5\u9519\u8bef\uff01\u8bf7\u8bb0\u5f55\u73af\u5883\u4fe1\u606f\uff0c\u8054\u7cfbUAMS\u7684\u5f00\u53d1");
                    request.setAttribute("fail", (Object)this.messageSource.getMessage("unknown-error", null, locale));
                    e.printStackTrace();
                }
            }
            request.getSession().setAttribute("enableCaptcha", (Object)this.securityProper.isEnableCaptcha());
            request.getSession().setAttribute("enableRememberMe", (Object)this.securityProper.isEnableRememberMe());
            request.getSession().setAttribute("enableSmsLogin", (Object)this.smsLoginProper.isEnableSmsLogin());
            request.getSession().setAttribute("enableHumanVerification", (Object)this.smsLoginProper.isEnableHumanVerification());
            request.getSession().setAttribute("smsTimeInterval", (Object)this.smsLoginProper.getTimeInterval());
            HashMap<String, String> param = new HashMap<String, String>(1);
            if (!StringUtils.isEmpty((CharSequence)request.getParameter("order"))) {
                param.put("order", request.getParameter("order"));
            }
            if (!StringUtils.isEmpty((CharSequence)returnURL)) {
                param.put("returnURL", returnURL);
            }
            param.put("maxInactiveIntervalInSeconds", this.maxInactiveIntervalInSeconds);
            return new ModelAndView(LoginPage.loginPage, param);
        }
    }

    @RequestMapping(value={"loginOfSms"}, method={RequestMethod.POST})
    public ModelAndView loginOfSms(@RequestParam(value="mobilePhone") String mobilePhone, @RequestParam(value="userInputCode") String userInputCode, String returnURL, HttpServletResponse response, HttpServletRequest request) {
        Locale locale = RequestContextUtils.getLocale((HttpServletRequest)request);
        if (ObjectUtil.isEmpty((Object)mobilePhone) || ObjectUtil.isNull((Object)mobilePhone)) {
            throw new UamsException(ErrorCode.UNPROCESABLE_ENTITY, this.messageSource.getMessage("index.controller.loginSms.mobile.phone.empty", null, locale));
        }
        if (SysUserValidator.checkMobilePhone(mobilePhone)) {
            Integer mobilePhoneNotBindedByUserNumber = this.userService.mobilePhoneNotBindedByUserNumber(mobilePhone);
            if (mobilePhoneNotBindedByUserNumber > 1) {
                log.info((Object)(mobilePhone + "\u7ed1\u5b9a\u7684\u7528\u6237\u6570\u4e3a" + mobilePhoneNotBindedByUserNumber));
                throw new UamsException(ErrorCode.RESOURCE_ALREADY_EXISTS, this.messageSource.getMessage("sms.validator.mobile.phone.exist.pre", null, locale) + mobilePhone + this.messageSource.getMessage("sms.validator.mobile.phone.exist.order", null, locale));
            }
            SysUser user = this.userService.findByMobilePhone(mobilePhone);
            if (ObjectUtil.isNotNull((Object)user)) {
                VerificationCodeInfo verificationCodeInfo = this.redisService.getVerificationCodeInfo(user.getId());
                if (ObjectUtil.isNotEmpty((Object)verificationCodeInfo) && ObjectUtil.isNotNull((Object)verificationCodeInfo)) {
                    if (VerificationCodeUtil.verify(verificationCodeInfo.getVerificationCode(), userInputCode)) {
                        Subject currUser = SecurityUtils.getSubject();
                        String uamsUsername = user.getUsername();
                        log.info((Object)("returnURL" + returnURL));
                        return this.loginSucceed(returnURL, response, request, currUser, uamsUsername);
                    }
                    throw new UamsException(ErrorCode.VERIFICATION_CODE_ERROR, this.messageSource.getMessage("index.controller.loginSms.verification.code.error", null, locale));
                }
                throw new UamsException(ErrorCode.VERIFICATION_CODE_EXPIRED, this.messageSource.getMessage("index.controller.loginSms.verification.code.expired", null, locale));
            }
            throw new UamsException(ErrorCode.UNPROCESABLE_ENTITY, this.messageSource.getMessage("index.controller.loginSms.mobile.phone.unregistered", null, locale));
        }
        return new ModelAndView();
    }

    @PostMapping(value={"/auth"})
    @ResponseBody
    public Map<String, Object> Auth(@RequestParam String username, HttpServletRequest request) throws Exception {
        return this.thirdLogin(username, request);
    }

    @PostMapping(value={"/authEncrypt"})
    @ResponseBody
    public Map<String, Object> Auth(@RequestBody Map<String, String> map, HttpServletRequest request) throws Exception {
        String passwordDe;
        String usernameDe;
        String username = map.get("encryptedUsername");
        String password = map.get("encryptedPassword");
        try {
            usernameDe = RSAUtil.decrypt(username);
            System.out.println("\u89e3\u5bc6\u540e\u7528\u6237\u540d\u4e3a\uff1a" + usernameDe);
            passwordDe = RSAUtil.decrypt(password);
        }
        catch (Exception e) {
            throw new UamsException(ErrorCode.BAD_INPUT_PARAMETER, "\u89e3\u5bc6\u5931\u8d25\uff0c\u8bf7\u68c0\u67e5\u52a0\u5bc6\u8fc7\u7a0b");
        }
        SysUser user = this.userService.findByUsername(usernameDe);
        if (ObjectUtil.isNull((Object)user)) {
            throw new UamsException(ErrorCode.UNPROCESABLE_ENTITY, "\u7528\u6237" + usernameDe + "\u4e0d\u5b58\u5728");
        }
        String MD5Pwd = Md5Util.getMD5(passwordDe);
        if (!user.getPassword().equals(MD5Pwd)) {
            throw new UamsException(ErrorCode.UNPROCESABLE_ENTITY, "\u5bc6\u7801\u4e0d\u6b63\u786e");
        }
        return this.thirdLogin(usernameDe, request);
    }

    @PostMapping(value={"token"})
    @ResponseBody
    public String getToken(@RequestParam(value="serverName") String serverName, @RequestParam(value="credential") String credential) {
        try {
            credential = JwtUtils.decrypt(credential);
        }
        catch (Exception e) {
            e.printStackTrace();
        }
        HashMap<String, Object> claims = new HashMap<String, Object>(10);
        SysUser user = this.userService.findByUsername(credential);
        if (user != null) {
            claims.put("username", user.getUsername());
            claims.put("sub", user.getUsername());
            claims.put("id", user.getId());
            claims.put("iat", Date.from(Instant.ofEpochSecond(System.currentTimeMillis())));
            claims.put("exp", Date.from(Instant.ofEpochSecond(System.currentTimeMillis() + this.tokenExpires)));
            List<Group> groups = this.groupService.getGroupByUserId(user.getId());
            claims.put("groups", new Gson().toJson(groups));
            String token = JwtUtils.createToken(claims);
            return token;
        }
        throw new UamsException(ErrorCode.UNPROCESABLE_ENTITY, "\u8bf7\u6c42\u53c2\u6570\u9519\u8bef");
    }

    private Map<String, Object> thirdLogin(String username, HttpServletRequest request) {
        SysUser user = this.userService.findByUsername(username);
        Locale locale = RequestContextUtils.getLocale((HttpServletRequest)request);
        if (user == null) {
            throw new UamsException(ErrorCode.RESOURCE_NOT_FOUND, this.messageSource.getMessage("index.controller.user.exist.pre", null, locale) + username + this.messageSource.getMessage("index.controller.user.exist.order", null, locale));
        }
        Subject currUser = SecurityUtils.getSubject();
        if (!currUser.isAuthenticated()) {
            UamsAuthenticationToken token = new UamsAuthenticationToken(user.getUsername(), user.getPassword());
            currUser.login((AuthenticationToken)token);
        }
        List<Group> groups = this.groupService.getGroupByUserId(user.getId());
        user.setGroups(groups);
        request.setAttribute("isThird", (Object)true);
        request.getSession().setAttribute(CURRENT_USER, (Object)user);
        HashMap<String, Object> result = new HashMap<String, Object>(2);
        result.put("value", request.getSession().getId());
        result.put("name", "FULONGTECH_SESSION");
        return result;
    }

    private ModelAndView loginSucceed(String returnURL, HttpServletResponse response, HttpServletRequest request, Subject currUser, String uamsUsername) {
        Cookie[] cookies;
        String sessionToken = UUID.randomUUID().toString().replace("-", "");
        currUser.getSession().setAttribute((Object)"sessionToken", (Object)sessionToken);
        for (Cookie cookie : cookies = request.getCookies()) {
            if (!RETURN_URL.equals(cookie.getName())) continue;
            String newUrl = cookie.getValue();
            return this.setSession(uamsUsername, request, response, newUrl);
        }
        return this.setSession(uamsUsername, request, response, returnURL);
    }

    @RequestMapping(value={"exit", "logout"})
    public ModelAndView exit(HttpServletRequest request, HttpServletResponse response, @RequestParam(value="order", required=false) String order, String returnURL, String kickout) throws UnsupportedEncodingException {
        Subject currUser = SecurityUtils.getSubject();
        if (currUser.isAuthenticated() && this.thirdAuthenticationProper.getAuthenticationType().contains("tas-jar")) {
            try {
                Class<?> TASService = Class.forName("com.fulongtech.tas.service.TASServiceImpl");
                Method exit = TASService.getDeclaredMethod("exit", String.class, HttpServletRequest.class, HttpServletResponse.class);
                ResponseMsg responseMsg = (ResponseMsg)exit.invoke(TASService.newInstance(), currUser.getPrincipal().toString(), request, response);
                Integer code = responseMsg.getCode();
                if (ResponseCode.USER_LOG_OUT.getCode().equals(code)) {
                    HashMap data = (HashMap)responseMsg.getData();
                    boolean bool = (Boolean)data.get("hasLoginPage");
                    if (bool) {
                        currUser.logout();
                        log.info((Object)"\u9000\u51fa\u6210\u529f\u3002");
                        String thirdLoginUrl = (String)data.get("url");
                        if (StringUtils.isEmpty((CharSequence)thirdLoginUrl)) {
                            System.out.println("\u7b2c\u4e09\u65b9\u767b\u5f55\u5730\u5740\u4e3a\u7a7a\uff0c\u8bf7\u68c0\u67e5tas.exit()\u65b9\u6cd5");
                            throw new Exception("\u7b2c\u4e09\u65b9\u767b\u5f55\u5730\u5740\u4e3a\u7a7a\uff01");
                        }
                        this.transfer(request, response, thirdLoginUrl);
                        return null;
                    }
                } else if (10002 == code || 10006 == code) {
                    HashMap data = (HashMap)responseMsg.getData();
                    String error = (String)data.get("error");
                    request.setAttribute("fail", (Object)error);
                }
                request.getSession().setAttribute("enableCaptcha", (Object)this.securityProper.isEnableCaptcha());
                request.getSession().setAttribute("enableRememberMe", (Object)this.securityProper.isEnableRememberMe());
                request.getSession().setAttribute("enableSmsLogin", (Object)this.smsLoginProper.isEnableSmsLogin());
                request.getSession().setAttribute("enableHumanVerification", (Object)this.smsLoginProper.isEnableHumanVerification());
                request.getSession().setAttribute("smsTimeInterval", (Object)this.smsLoginProper.getTimeInterval());
                return new ModelAndView(LoginPage.loginPage).addObject("maxInactiveIntervalInSeconds", (Object)this.maxInactiveIntervalInSeconds);
            }
            catch (Exception e) {
                e.printStackTrace();
            }
        }
        currUser.logout();
        log.info((Object)"\u9000\u51fa\u6210\u529f\u3002");
        if (request.getRequestURI().contains("logout") && !StringUtils.isEmpty((CharSequence)request.getParameter(RETURN_URL))) {
            returnURL = request.getParameter(RETURN_URL);
            HashMap<String, String> param = new HashMap<String, String>();
            param.put("maxInactiveIntervalInSeconds", this.maxInactiveIntervalInSeconds);
            return new ModelAndView((View)new RedirectView(URLDecoder.decode(returnURL, "UTF-8"))).addObject(param);
        }
        if (!StringUtils.isEmpty((CharSequence)order)) {
            return this.toLogin(response, request, order);
        }
        return this.toLogin(response, request, "");
    }

    public ModelAndView setSession(@RequestParam(value="username") String username, HttpServletRequest request, HttpServletResponse response, String url) {
        long dTime;
        Subject currUser;
        Locale locale = RequestContextUtils.getLocale((HttpServletRequest)request);
        SysUser user = this.userService.findByUsername(username);
        if (user == null) {
            if (this.enableSynchronize) {
                user = new SysUser();
                user.setUsername(username);
                user.setPassword(this.systemPassword.getDefaultpwd());
                this.userService.addUser(user);
                user = this.userService.findByUsername(username);
            } else {
                HashMap<String, String> map = new HashMap<String, String>(3);
                map.put("error", this.messageSource.getMessage("index.controller.user.not.exist", null, locale));
                return new ModelAndView("error", map).addObject(RETURN_URL, (Object)String.valueOf(url));
            }
        }
        if (!(currUser = SecurityUtils.getSubject()).isAuthenticated()) {
            UamsAuthenticationToken token = new UamsAuthenticationToken(user.getUsername(), user.getPassword());
            if (currUser.isRemembered() && this.securityProper.isEnableRememberMe()) {
                token.setRememberMe(true);
            }
            currUser.login((AuthenticationToken)token);
        }
        List<Group> groups = this.groupService.getGroupByUserId(user.getId());
        user.setGroups(groups);
        List<String> managedGroupsBYUserId = this.relationService.getManagedGroupsBYUserId(user.getId());
        user.setManagedGroups(managedGroupsBYUserId);
        request.getSession().setAttribute(CURRENT_USER, (Object)user);
        String ipAddress = IPUtil.getIpAddress(request);
        this.eventService.addEvent(EVENT_TYPE, user, ipAddress);
        RedisSessionForUser.putSession(username, (Session)this.redisOperationsSessionRepository.getSession(request.getSession().getId()));
        if (this.systemPassword.isUpdateDefaultPassword() && user.getPassword().equals(this.systemPassword.getDefaultpwd())) {
            HashMap<String, Object> map = new HashMap<String, Object>(3);
            map.put("message", this.messageSource.getMessage("index.controller.password.first", null, locale));
            request.getSession().setAttribute(UPDATE_PASSWORD, (Object)true);
            map.put(FORCE_CHANGE_PASSWORD, true);
            map.put("maxInactiveIntervalInSeconds", this.maxInactiveIntervalInSeconds);
            return new ModelAndView("user/user_changepwd", map).addObject(RETURN_URL, (Object)String.valueOf(url));
        }
        if (this.systemPassword.getExpireTime() > 0L && user.getChangePwdTime() != null && (dTime = System.currentTimeMillis() - user.getChangePwdTime().getTime()) > this.systemPassword.getExpireTime()) {
            HashMap map = Maps.newHashMap();
            map.put("message", this.messageSource.getMessage("index.controller.password.expire", null, locale));
            request.getSession().setAttribute(UPDATE_PASSWORD, (Object)true);
            map.put(FORCE_CHANGE_PASSWORD, true);
            map.put("maxInactiveIntervalInSeconds", this.maxInactiveIntervalInSeconds);
            return new ModelAndView("user/user_changepwd", (Map)map).addObject(RETURN_URL, (Object)url);
        }
        if (url == null || "".equals(url)) {
            HashMap<String, String> param = new HashMap<String, String>();
            param.put("maxInactiveIntervalInSeconds", this.maxInactiveIntervalInSeconds);
            return new ModelAndView((View)new RedirectView(request.getContextPath() + "/")).addObject(param);
        }
        try {
            url = URLDecoder.decode(url, "UTF-8");
            if (url.contains("logout")) {
                url = url.replace("logout", "");
            }
        }
        catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        this.transfer(request, response, url);
        return null;
    }

    @RequestMapping(value={"transfer"}, method={RequestMethod.POST})
    public void transfer(HttpServletRequest request, HttpServletResponse response, String url) {
        String scheme = request.getScheme();
        response.setStatus(307);
        response.setHeader("Location", url);
        try {
            response.sendRedirect(url);
        }
        catch (IOException e) {
            e.printStackTrace();
        }
    }

    @RequestMapping(value={"/"})
    public ModelAndView managerIndex(HttpServletRequest request, HttpServletResponse response) {
        return new ModelAndView("managerindex");
    }

    @GetMapping(value={"/getKey"})
    @ResponseBody
    public String getKey(HttpServletRequest request) {
        LoginKeySingleton loginKeySingleton = LoginKeySingleton.getInstance();
        String key = this.desUtil.getKey();
        String sessionId = request.getSession().getId();
        loginKeySingleton.put(sessionId, key);
        return key;
    }

    @GetMapping(value={"/getLang"})
    @ResponseBody
    public String getLang(HttpServletRequest request) {
        Locale locale = this.localeResolver.resolveLocale(request);
        String lang = locale.toLanguageTag().replace("-", "_");
        return locale.toLanguageTag().replace("-", "_");
    }

    @PostMapping(value={"/setLang"})
    @ResponseBody
    public String setLang(HttpServletRequest request, HttpServletResponse response, @RequestParam String lang) {
        String id = request.getSession().getId();
        Locale oldLocale = RequestContextUtils.getLocale((HttpServletRequest)request);
        if (!this.supportLang.contains(lang)) {
            throw new UamsException(ErrorCode.RESOURCE_NOT_FOUND, this.messageSource.getMessage("index.controller.lang.support", null, oldLocale));
        }
        String[] locales = lang.split("_");
        Locale locale = new Locale(locales[0], locales[1]);
        this.localeResolver.setLocale(request, response, locale);
        Locale resolveLocale = this.localeResolver.resolveLocale(request);
        String replace = resolveLocale.toLanguageTag().replace("-", "_");
        return resolveLocale.toLanguageTag().replace("-", "_");
    }

    @GetMapping(value={"/isLogin"})
    @ResponseBody
    public boolean isLogin() {
        return true;
    }
}
