/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.servlet.http.HttpServletRequest
 *  org.springframework.web.bind.annotation.GetMapping
 *  org.springframework.web.bind.annotation.RequestParam
 *  org.springframework.web.bind.annotation.RestController
 */
package com.fulongtech.uams.controller;

import com.fulongtech.uams.exception.ErrorCode;
import com.fulongtech.uams.exception.UamsException;
import com.fulongtech.uams.model.SysUser;
import com.fulongtech.uams.service.EventService;
import com.fulongtech.uams.util.SysUserValidator;
import com.fulongtech.uams.vo.EventPagesVo;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class EventController {
    @Resource
    private EventService eventService;

    @GetMapping(value={"/events"})
    public EventPagesVo getLogs(@RequestParam(value="type") String type, @RequestParam(value="userId") String userId, @RequestParam(value="groupId") String groupId, @RequestParam(value="startTime") String startTime, @RequestParam(value="endTime") String endTime, @RequestParam(value="pageNo", defaultValue="1") Integer pageNO, @RequestParam(value="pageSize", defaultValue="100") Integer pageSize, HttpServletRequest request) {
        SysUser currentUser = (SysUser)request.getSession().getAttribute("currentUser");
        if (!SysUserValidator.checkIdentity(currentUser) && !userId.equals(currentUser.getId())) {
            throw new UamsException(ErrorCode.UNAUTHORIZED, "\u7528\u6237\u6ca1\u6709\u6743\u9650");
        }
        return this.eventService.searchByParams(type, userId, groupId, startTime, endTime, pageNO, pageSize);
    }
}
