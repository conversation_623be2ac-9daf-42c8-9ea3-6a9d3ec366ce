/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  cn.hutool.core.util.StrUtil
 *  javax.servlet.http.HttpServletRequest
 *  org.springframework.web.bind.annotation.GetMapping
 *  org.springframework.web.bind.annotation.RequestMapping
 *  org.springframework.web.bind.annotation.RequestParam
 *  org.springframework.web.bind.annotation.RestController
 */
package com.fulongtech.uams.controller;

import cn.hutool.core.util.StrUtil;
import com.fulongtech.uams.exception.ErrorCode;
import com.fulongtech.uams.exception.UamsException;
import com.fulongtech.uams.model.SysUser;
import com.fulongtech.uams.service.StatisticalService;
import com.fulongtech.uams.util.SysUserValidator;
import com.fulongtech.uams.vo.LoginRat;
import java.util.List;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value={"/statistical"})
public class StatisticalController {
    @Resource
    private StatisticalService statisticalService;

    @GetMapping(value={"/login_rat"})
    public List<LoginRat> loginRat(@RequestParam(value="groupIds") String groupIds, @RequestParam(value="startTime") String startTime, @RequestParam(value="endTime") String endTime, HttpServletRequest request) {
        SysUser currentUser = (SysUser)request.getSession().getAttribute("currentUser");
        if (!SysUserValidator.checkIdentity(currentUser)) {
            throw new UamsException(ErrorCode.UNAUTHORIZED, "\u7528\u6237\u6ca1\u6709\u6743\u9650");
        }
        if (StrUtil.isEmpty((CharSequence)groupIds)) {
            throw new UamsException(ErrorCode.UNPROCESABLE_ENTITY, "\u7528\u6237\u7ec4ID\u4e0d\u80fd\u4e3a\u7a7a");
        }
        return this.statisticalService.getLoginRat(groupIds, startTime, endTime);
    }
}
