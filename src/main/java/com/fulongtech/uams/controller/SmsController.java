/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  cn.hutool.core.date.DateTime
 *  cn.hutool.core.date.DateUnit
 *  cn.hutool.core.date.DateUtil
 *  cn.hutool.core.util.ObjectUtil
 *  cn.hutool.extra.servlet.ServletUtil
 *  javax.servlet.http.HttpServletRequest
 *  javax.servlet.http.HttpServletResponse
 *  org.apache.log4j.LogManager
 *  org.apache.log4j.Logger
 *  org.springframework.beans.factory.annotation.Autowired
 *  org.springframework.context.MessageSource
 *  org.springframework.stereotype.Controller
 *  org.springframework.web.bind.annotation.GetMapping
 *  org.springframework.web.bind.annotation.PostMapping
 *  org.springframework.web.bind.annotation.RequestMapping
 *  org.springframework.web.bind.annotation.RequestParam
 *  org.springframework.web.bind.annotation.ResponseBody
 *  org.springframework.web.servlet.support.RequestContextUtils
 */
package com.fulongtech.uams.controller;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.servlet.ServletUtil;
import com.fulongtech.uams.exception.ErrorCode;
import com.fulongtech.uams.exception.UamsException;
import com.fulongtech.uams.model.SysUser;
import com.fulongtech.uams.properties.SmsLoginProper;
import com.fulongtech.uams.service.UamsSmsService;
import com.fulongtech.uams.service.UserService;
import com.fulongtech.uams.service.impl.RedisServiceImpl;
import com.fulongtech.uams.sms.VerificationCodeInfo;
import com.fulongtech.uams.util.SysUserValidator;
import com.fulongtech.uams.util.VerificationCodeUtil;
import java.util.Date;
import java.util.Locale;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.log4j.LogManager;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.support.RequestContextUtils;

@Controller
@RequestMapping(value={"/sms"})
public class SmsController {
    @Resource
    private SmsLoginProper smsLoginProper;
    @Resource
    private RedisServiceImpl redisService;
    @Resource
    private UamsSmsService uamsSmsService;
    @Resource
    private UserService userService;
    @Autowired
    private MessageSource messageSource;
    private static final Logger log = LogManager.getLogger(SmsController.class);

    @GetMapping(value={"/enableSmsLogin"})
    @ResponseBody
    public boolean enableSmsLogin() {
        return this.smsLoginProper.isEnableSmsLogin();
    }

    /*
     * Enabled aggressive block sorting
     */
    @PostMapping(value={"/send"})
    @ResponseBody
    public void send(@RequestParam(value="mobilePhone") String mobilePhone, HttpServletRequest request, HttpServletResponse response) {
        Date date;
        Locale locale = RequestContextUtils.getLocale((HttpServletRequest)request);
        if (!this.smsLoginProper.isEnableSmsLogin()) {
            throw new UamsException(ErrorCode.FORBIDDEN_OPERATE, this.messageSource.getMessage("sms.validator.enableSmsLogin", null, locale));
        }
        if (ObjectUtil.isEmpty((Object)mobilePhone)) throw new UamsException(ErrorCode.UNPROCESABLE_ENTITY, this.messageSource.getMessage("sms.validator.mobilePhone.empty", null, locale));
        if (ObjectUtil.isNull((Object)mobilePhone)) {
            throw new UamsException(ErrorCode.UNPROCESABLE_ENTITY, this.messageSource.getMessage("sms.validator.mobilePhone.empty", null, locale));
        }
        if (!SysUserValidator.checkMobilePhone(mobilePhone)) return;
        Integer mobilePhoneNotBindedByUserNumber = this.userService.mobilePhoneNotBindedByUserNumber(mobilePhone);
        if (mobilePhoneNotBindedByUserNumber > 1) {
            log.info((Object)(mobilePhone + "\u7ed1\u5b9a\u7684\u7528\u6237\u6570\u4e3a" + mobilePhoneNotBindedByUserNumber));
            throw new UamsException(ErrorCode.RESOURCE_ALREADY_EXISTS, this.messageSource.getMessage("sms.validator.mobile.phone.exist.pre", null, locale) + mobilePhone + this.messageSource.getMessage("sms.validator.mobile.phone.exist.order", null, locale));
        }
        SysUser user = this.userService.findByMobilePhone(mobilePhone);
        if (!ObjectUtil.isNotNull((Object)user)) throw new UamsException(ErrorCode.UNPROCESABLE_ENTITY, this.messageSource.getMessage("sms.validator.mobile.phone.unregistered", null, locale));
        VerificationCodeInfo verificationCodeInfo = this.redisService.getVerificationCodeInfo(user.getId());
        if (ObjectUtil.isNotEmpty((Object)verificationCodeInfo) && ObjectUtil.isNotNull((Object)verificationCodeInfo) && DateUtil.isIn((Date)(date = new Date()), (Date)verificationCodeInfo.getSendTime(), (Date)verificationCodeInfo.getRefreshTime())) {
            throw new UamsException(ErrorCode.FORBIDDEN_OPERATE, this.messageSource.getMessage("sms.validator.verificationCode.frequently", null, locale));
        }
        Integer mobilePhoneNumber = 0;
        Integer ipNumber = 0;
        String clientIP = ServletUtil.getClientIP((HttpServletRequest)request, null);
        mobilePhoneNumber = this.redisService.getIntegerValue(mobilePhone);
        mobilePhoneNumber = mobilePhoneNumber == null ? 0 : mobilePhoneNumber;
        if (ObjectUtil.isNotNull((Object)mobilePhoneNumber) && this.smsLoginProper.getMobilePhoneSendNumber() <= mobilePhoneNumber) {
            throw new UamsException(ErrorCode.FORBIDDEN_OPERATE, this.messageSource.getMessage("sms.validator.mobile.phone.today", null, locale) + mobilePhone + this.messageSource.getMessage("sms.validator.mobile.phone.times", null, locale));
        }
        ipNumber = this.redisService.getIntegerValue(clientIP);
        ipNumber = ipNumber == null ? 0 : ipNumber;
        if (ObjectUtil.isNotNull((Object)ipNumber) && this.smsLoginProper.getIpSendNumber() <= ipNumber) {
            throw new UamsException(ErrorCode.FORBIDDEN_OPERATE, this.messageSource.getMessage("sms.validator.ip.today", null, locale) + clientIP + this.messageSource.getMessage("sms.validator.ip.times", null, locale));
        }
        log.info((Object)("\u624b\u673a\u53f7" + mobilePhone + "\u5df2\u7ecf\u9a8c\u8bc1\u7801\u53d1\u9001\u7684\u6b21\u6570" + mobilePhoneNumber));
        log.info((Object)("ip:" + clientIP + "\u5df2\u7ecf\u9a8c\u8bc1\u7801\u53d1\u9001\u7684\u6b21\u6570" + ipNumber));
        String generateCode = VerificationCodeUtil.generateCode();
        log.info((Object)("\u624b\u673a\u53f7\uff1a" + mobilePhone + "\u7684\u9a8c\u8bc1\u7801\uff1a" + generateCode));
        Date date2 = new Date();
        VerificationCodeInfo currentCodeInfo = new VerificationCodeInfo(mobilePhone, generateCode, date2, this.smsLoginProper.getTimeInterval(), this.smsLoginProper.getValidityPeriod());
        Boolean aBoolean = this.redisService.setVerificationCodeInfo(user.getId(), currentCodeInfo, this.smsLoginProper.getValidityPeriod());
        if (!aBoolean.booleanValue()) {
            log.info((Object)"redis\u4fdd\u5b58\u9a8c\u8bc1\u7801\u5931\u8d25");
            throw new UamsException(ErrorCode.RESOURCE_NOT_FOUND, this.messageSource.getMessage("sms.validator.send.error", null, locale));
        }
        if (this.uamsSmsService.sendVerificationCode(mobilePhone, generateCode, this.smsLoginProper.getSmsJarPath(), this.smsLoginProper.getValidityPeriod())) {
            Date currentDate = new Date();
            DateTime dateTime = DateUtil.endOfDay((Date)currentDate);
            Long between = DateUtil.between((Date)currentDate, (Date)dateTime, (DateUnit)DateUnit.MINUTE);
            log.info((Object)("\u5f53\u524d\u65f6\u95f4\uff1a" + DateUtil.format((Date)currentDate, (String)"yyyy-MM-dd HH:mm:ss")));
            log.info((Object)("\u4eca\u65e5\u7ed3\u675f\u65f6\u95f4\uff1a" + DateUtil.format((Date)dateTime, (String)"yyyy-MM-dd HH:mm:ss")));
            log.info((Object)("\u5dee\u503c\uff1a" + between + "\u5206\u949f"));
            Boolean aBoolean1 = this.redisService.setIntegerValue(mobilePhone, mobilePhoneNumber + 1, between);
            Boolean aBoolean2 = this.redisService.setIntegerValue(clientIP, ipNumber + 1, between);
            log.info((Object)("\u624b\u673a\u6b21\u6570\u5b58\u5165\uff1a" + aBoolean1));
            log.info((Object)("ip\u6b21\u6570\u5b58\u5165\uff1a" + aBoolean2));
            return;
        }
        this.redisService.deleteValue(user.getId());
        throw new UamsException(ErrorCode.SMS_SEND_ERROR, this.messageSource.getMessage("sms.validator.send.error", null, locale));
    }
}
