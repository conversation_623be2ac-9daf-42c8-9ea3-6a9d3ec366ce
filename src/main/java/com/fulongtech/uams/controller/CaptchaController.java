/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  com.google.code.kaptcha.Producer
 *  javax.servlet.ServletOutputStream
 *  javax.servlet.http.HttpServletRequest
 *  javax.servlet.http.HttpServletResponse
 *  org.springframework.stereotype.Controller
 *  org.springframework.web.bind.annotation.RequestMapping
 *  org.springframework.web.bind.annotation.ResponseBody
 */
package com.fulongtech.uams.controller;

import com.fulongtech.uams.util.CaptchaUtil;
import com.google.code.kaptcha.Producer;
import java.awt.image.BufferedImage;
import java.awt.image.RenderedImage;
import java.io.IOException;
import java.io.OutputStream;
import javax.annotation.Resource;
import javax.imageio.ImageIO;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping(value={"/captcha"})
public class CaptchaController {
    @Resource
    private Producer captchaProducer;

    @RequestMapping(value={"/picture"})
    private void getCaptcha(HttpServletRequest request, HttpServletResponse response) throws IOException {
        response.setDateHeader("Expires", 0L);
        response.setHeader("Cache-Control", "no-store, no-cache, must-revalidate");
        response.addHeader("Cache-Control", "post-check=0, pre-check=0");
        response.setHeader("Pragma", "no-cache");
        response.setContentType("image/jpeg");
        CaptchaUtil util = CaptchaUtil.Instance();
        String code = util.getString();
        request.getSession().setAttribute("KAPTCHA_SESSION_KEY", (Object)code);
        BufferedImage bi = util.getImage();
        ServletOutputStream out = response.getOutputStream();
        ImageIO.write((RenderedImage)bi, "jpg", (OutputStream)out);
        out.flush();
        out.close();
    }

    @RequestMapping(value={"/validate"})
    @ResponseBody
    private boolean validateCaptcha(HttpServletRequest request, HttpServletResponse response, String captchaCode) {
        String generateCode = (String)request.getSession().getAttribute("KAPTCHA_SESSION_KEY");
        if (generateCode == null || captchaCode == null) {
            return false;
        }
        return captchaCode.equalsIgnoreCase(generateCode);
    }
}
