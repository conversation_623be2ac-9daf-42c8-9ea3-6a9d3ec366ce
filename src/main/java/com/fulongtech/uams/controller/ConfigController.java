/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.springframework.beans.factory.annotation.Value
 *  org.springframework.web.bind.annotation.GetMapping
 *  org.springframework.web.bind.annotation.RequestMapping
 *  org.springframework.web.bind.annotation.RestController
 */
package com.fulongtech.uams.controller;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value={"/config"})
public class ConfigController {
    @Value(value="${uams.redis.maxInactiveIntervalInSeconds}")
    private long activeTime;

    @GetMapping(value={"activeTime"})
    public long getActiveTime() {
        if (this.activeTime > 0L) {
            return this.activeTime;
        }
        return 100L;
    }
}
