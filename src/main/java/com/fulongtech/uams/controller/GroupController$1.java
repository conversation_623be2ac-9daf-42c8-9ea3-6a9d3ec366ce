/*
 * Decompiled with CFR 0.152.
 */
package com.fulongtech.uams.controller;

import com.fulongtech.uams.controller.GroupController;
import com.fulongtech.uams.model.Group;
import java.util.Comparator;

class GroupController.1
implements Comparator<Group> {
    GroupController.1() {
    }

    @Override
    public int compare(Group o1, Group o2) {
        if (GroupController.SYSTEM_GROUP_NAME.equals(o1.getName())) {
            return -1;
        }
        if (GroupController.SYSTEM_GROUP_NAME.equals(o2.getName())) {
            return 1;
        }
        return o1.getName().compareTo(o2.getName());
    }
}
