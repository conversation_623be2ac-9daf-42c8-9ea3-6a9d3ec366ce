/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  com.google.common.collect.Maps
 *  javax.servlet.http.HttpServletRequest
 *  javax.servlet.http.HttpServletResponse
 *  javax.validation.Valid
 *  org.apache.log4j.Logger
 *  org.apache.shiro.SecurityUtils
 *  org.apache.shiro.authc.AuthenticationToken
 *  org.apache.shiro.subject.Subject
 *  org.springframework.beans.factory.annotation.Autowired
 *  org.springframework.context.MessageSource
 *  org.springframework.stereotype.Controller
 *  org.springframework.util.StringUtils
 *  org.springframework.validation.BindingResult
 *  org.springframework.validation.ObjectError
 *  org.springframework.web.bind.annotation.DeleteMapping
 *  org.springframework.web.bind.annotation.GetMapping
 *  org.springframework.web.bind.annotation.PathVariable
 *  org.springframework.web.bind.annotation.PostMapping
 *  org.springframework.web.bind.annotation.PutMapping
 *  org.springframework.web.bind.annotation.RequestBody
 *  org.springframework.web.bind.annotation.RequestMapping
 *  org.springframework.web.bind.annotation.RequestMethod
 *  org.springframework.web.bind.annotation.RequestParam
 *  org.springframework.web.bind.annotation.ResponseBody
 *  org.springframework.web.multipart.MultipartFile
 *  org.springframework.web.servlet.ModelAndView
 *  org.springframework.web.servlet.View
 *  org.springframework.web.servlet.support.RequestContextUtils
 *  org.springframework.web.servlet.view.RedirectView
 */
package com.fulongtech.uams.controller;

import com.fulongtech.uams.aspect.SysManagerPermission;
import com.fulongtech.uams.controller.IndexController;
import com.fulongtech.uams.exception.ErrorCode;
import com.fulongtech.uams.exception.UamsException;
import com.fulongtech.uams.model.ExcelUploadLog;
import com.fulongtech.uams.model.Group;
import com.fulongtech.uams.model.SysUser;
import com.fulongtech.uams.model.UserGroupRelation;
import com.fulongtech.uams.properties.GatewayProper;
import com.fulongtech.uams.properties.SmsLoginProper;
import com.fulongtech.uams.properties.SystemPassword;
import com.fulongtech.uams.service.GroupService;
import com.fulongtech.uams.service.RelationService;
import com.fulongtech.uams.service.UserService;
import com.fulongtech.uams.shiro.token.UamsAuthenticationToken;
import com.fulongtech.uams.util.DESUtil;
import com.fulongtech.uams.util.LoginKeySingleton;
import com.fulongtech.uams.util.Md5Util;
import com.fulongtech.uams.util.SessionUtil;
import com.fulongtech.uams.util.SysUserValidator;
import com.fulongtech.uams.vo.GroupParamModel;
import com.fulongtech.uams.vo.UserWrapper;
import com.google.common.collect.Maps;
import java.io.IOException;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import org.apache.log4j.Logger;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authc.AuthenticationToken;
import org.apache.shiro.subject.Subject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Controller;
import org.springframework.util.StringUtils;
import org.springframework.validation.BindingResult;
import org.springframework.validation.ObjectError;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.View;
import org.springframework.web.servlet.support.RequestContextUtils;
import org.springframework.web.servlet.view.RedirectView;

@Controller
@RequestMapping(value={"/users"})
public class UserController {
    private static final Logger log = Logger.getLogger(UserController.class);
    private static final String UPDATE_PASSWORD = "updatePassword";
    @Resource
    private UserService userService;
    @Resource
    private RelationService relationService;
    @Resource
    private GroupService groupService;
    @Resource
    private IndexController indexController;
    @Resource
    private SystemPassword systemPassword;
    @Resource
    private SmsLoginProper smsLoginProper;
    @Resource
    private GatewayProper gatewayProper;
    private static final String SYSTEM_GROUP_ID = "BEFE2FF1A20F4C419C8A94B7213C5218";
    private static final String ADMIN_ID = "BEFE2FF1A20F4C419C8A94B7213C5219";
    @Resource
    private SessionUtil sessionUtil;
    private static final String CURRENT_USER = "currentUser";
    @Autowired
    private MessageSource messageSource;
    private static final String FORCE_CHANGE_PASSWORD = "changePassword";

    @RequestMapping(value={"info"})
    public ModelAndView info(HttpServletRequest request) {
        return new ModelAndView((View)new RedirectView("user_info"));
    }

    @GetMapping(value={"/withGroups"})
    @ResponseBody
    public List<SysUser> getAllUsersWithGroups(HttpServletRequest request) {
        ArrayList<SysUser> users = new ArrayList();
        users = this.userService.getAll();
        SysUser currentUser = this.getCurrentUser(request);
        return this.userService.getSysUsersWithGroups(users, currentUser);
    }

    @PostMapping
    @ResponseBody
    @SysManagerPermission.HasPermission(value="\u6dfb\u52a0\u7528\u6237")
    public String add(@RequestBody @Valid SysUser user, BindingResult bindingResult, HttpServletRequest request) throws Exception {
        Locale locale = RequestContextUtils.getLocale((HttpServletRequest)request);
        if (bindingResult.hasErrors()) {
            throw new UamsException(ErrorCode.UNPROCESABLE_ENTITY, ((ObjectError)bindingResult.getAllErrors().get(0)).getDefaultMessage());
        }
        SysUserValidator.checkUserProperty(user, true, this.messageSource, locale, this.smsLoginProper.isEnableSmsLogin());
        SysUser currentUser = (SysUser)SecurityUtils.getSubject().getSession().getAttribute((Object)CURRENT_USER);
        if (this.userService.findByUsername(user.getUsername()) != null) {
            throw new UamsException(ErrorCode.RESOURCE_ALREADY_EXISTS, this.messageSource.getMessage("user.controller.user.add.exist.pre", null, locale) + user.getUsername() + this.messageSource.getMessage("user.controller.user.add.exist.order", null, locale));
        }
        if (this.smsLoginProper.isEnableSmsLogin() && !this.userService.mobilePhoneNotBindedByUser(user.getMobilePhone())) {
            throw new UamsException(ErrorCode.RESOURCE_ALREADY_EXISTS, this.messageSource.getMessage("user.controller.mobile.phone.add.exist.pre", null, locale) + user.getMobilePhone() + this.messageSource.getMessage("user.controller.mobile.phone.add.exist.order", null, locale));
        }
        user.setCreateBy(currentUser.getId());
        user.setPassword(this.systemPassword.getDefaultpwd());
        user.setChangePwdTime(new Timestamp(System.currentTimeMillis()));
        return this.userService.addUser(user).getId();
    }

    @PutMapping
    @ResponseBody
    public UserWrapper save(@RequestBody @Valid SysUser user, BindingResult bindingResult, HttpServletRequest request) throws Exception {
        SysUser oneUser;
        List<SysUser> usersListByMobilePhone;
        Locale locale = RequestContextUtils.getLocale((HttpServletRequest)request);
        if (bindingResult.hasErrors()) {
            throw new UamsException(ErrorCode.UNPROCESABLE_ENTITY, ((ObjectError)bindingResult.getAllErrors().get(0)).getDefaultMessage());
        }
        SysUser currentUser = (SysUser)request.getSession().getAttribute(CURRENT_USER);
        if (!ADMIN_ID.equals(currentUser.getId()) && ADMIN_ID.equals(user.getId())) {
            throw new UamsException(ErrorCode.FORBIDDEN_OPERATE, this.messageSource.getMessage("user.controller.permission.user.info.modify", null, locale));
        }
        if (!SysUserValidator.checkIdentity(currentUser) && !currentUser.getId().equals(user.getId())) {
            throw new UamsException(ErrorCode.UNAUTHORIZED, this.messageSource.getMessage("user.controller.permission.user.update.pre", null, locale) + currentUser.getUsername() + this.messageSource.getMessage("user.controller.permission.user.update.order", null, locale));
        }
        SysUserValidator.checkUserProperty(user, false, this.messageSource, locale, this.smsLoginProper.isEnableSmsLogin());
        if (this.userService.getById(user.getId()) == null) {
            throw new UamsException(ErrorCode.RESOURCE_NOT_FOUND, this.messageSource.getMessage("user.controller.user.empty", null, locale));
        }
        if (this.smsLoginProper.isEnableSmsLogin() && ((usersListByMobilePhone = this.userService.findUserListByMobilePhone(user.getMobilePhone())).size() == 1 ? !(oneUser = usersListByMobilePhone.get(0)).getId().equals(user.getId()) : usersListByMobilePhone.size() > 1)) {
            throw new UamsException(ErrorCode.RESOURCE_ALREADY_EXISTS, this.messageSource.getMessage("user.controller.mobile.phone.add.exist.pre", null, locale) + user.getMobilePhone() + this.messageSource.getMessage("user.controller.mobile.phone.add.exist.order", null, locale));
        }
        user.setUsername(null);
        SysUser newUser = this.userService.updateUser(user);
        if (StringUtils.hasText((String)newUser.getId()) && currentUser.getId().equals(newUser.getId())) {
            List<Group> groups = this.groupService.getGroupByUserId(user.getId());
            newUser.setGroups(groups);
            request.getSession().setAttribute(CURRENT_USER, (Object)newUser);
        }
        return UserWrapper.getInstance(this.userService.updateUser(user));
    }

    @DeleteMapping(value={"/{id}"})
    @ResponseBody
    @SysManagerPermission.HasPermission(value="\u5220\u9664\u7528\u6237")
    public void delete(@PathVariable String id, HttpServletRequest request) {
        Locale locale = RequestContextUtils.getLocale((HttpServletRequest)request);
        if (ADMIN_ID.equals(id)) {
            throw new UamsException(ErrorCode.FORBIDDEN_OPERATE, this.messageSource.getMessage("user.controller.permission.admin.delete", null, locale));
        }
        if (this.getCurrentUser(request).getId().equals(id)) {
            throw new UamsException(ErrorCode.FORBIDDEN_OPERATE, this.messageSource.getMessage("user.controller.permission.delete.pre", null, locale) + id + this.messageSource.getMessage("user.controller.permission.delete.order", null, locale));
        }
        SysUser deleteUser = this.userService.getById(id);
        if (deleteUser == null) {
            throw new UamsException(ErrorCode.RESOURCE_NOT_FOUND, this.messageSource.getMessage("user.controller.user.empty", null, locale));
        }
        List<Group> groups = this.groupService.getGroupByUserId(deleteUser.getId());
        if (groups != null && groups.stream().anyMatch(Group::isSysGroup) && !this.getCurrentUser(request).getId().equals(ADMIN_ID)) {
            throw new UamsException(ErrorCode.FORBIDDEN_OPERATE, this.messageSource.getMessage("user.controller.permission.delete.user", null, locale));
        }
        this.userService.delete(deleteUser);
    }

    @GetMapping(value={"/{id}"})
    @ResponseBody
    public UserWrapper get(@PathVariable(value="id") String id, HttpServletRequest request) {
        Locale locale = RequestContextUtils.getLocale((HttpServletRequest)request);
        SysUser user = this.userService.getById(id);
        if (user == null) {
            throw new UamsException(ErrorCode.RESOURCE_NOT_FOUND, this.messageSource.getMessage("user.controller.user.empty", null, locale));
        }
        return UserWrapper.getInstance(user);
    }

    @GetMapping(value={"/groups/{userId}"})
    @ResponseBody
    public List<Group> groups(@PathVariable(name="userId") String userId) {
        return this.groupService.getGroupByUserId(userId);
    }

    @RequestMapping(value={"toChangePwd"})
    public ModelAndView toChangePwd(@RequestParam(value="returnURL", required=false) String returnUrl, HttpServletRequest request) {
        boolean isAdd;
        HashMap map = Maps.newHashMap();
        if (request.getSession().getAttribute(UPDATE_PASSWORD) != null && (isAdd = ((Boolean)request.getSession().getAttribute(UPDATE_PASSWORD)).booleanValue())) {
            map.put(FORCE_CHANGE_PASSWORD, true);
        }
        System.out.println(returnUrl);
        return new ModelAndView("user/user_changepwd", (Map)map).addObject("returnUrl", (Object)returnUrl);
    }

    @GetMapping(value={"/currentUser"})
    @ResponseBody
    public SysUser getCurrentUser(HttpServletRequest request) {
        return (SysUser)request.getSession().getAttribute(CURRENT_USER);
    }

    @RequestMapping(value={"grouping"}, method={RequestMethod.POST})
    @ResponseBody
    @SysManagerPermission.HasPermission(value="\u5206\u914d\u7528\u6237\u7ec4")
    public void grouping(@RequestBody GroupParamModel groupParamModelList, HttpServletRequest request) throws Exception {
        String userId = groupParamModelList.getUserId();
        List<UserGroupRelation> userGroupRelationList = groupParamModelList.getGroupModelList();
        Locale locale = RequestContextUtils.getLocale((HttpServletRequest)request);
        if (StringUtils.isEmpty((Object)userId)) {
            throw new UamsException(ErrorCode.UNPROCESABLE_ENTITY, this.messageSource.getMessage("user.controller.add.user.id.empty", null, locale));
        }
        if (this.userService.getById(userId) == null) {
            throw new UamsException(ErrorCode.UNPROCESABLE_ENTITY, this.messageSource.getMessage("user.controller.user.empty", null, locale));
        }
        if (ADMIN_ID.equals(userId)) {
            throw new UamsException(ErrorCode.UNAUTHORIZED, this.messageSource.getMessage("user.controller.permission.current.user.distribute", null, locale));
        }
        SysUser currentUser = this.getCurrentUser(request);
        if (userId.equals(currentUser.getId())) {
            throw new UamsException(ErrorCode.UNAUTHORIZED, this.messageSource.getMessage("user.controller.permission.current.user.distribute.gorup.self", null, locale));
        }
        ArrayList<String> groupIds = new ArrayList<String>();
        for (UserGroupRelation userGroupRelation : userGroupRelationList) {
            groupIds.add(userGroupRelation.getGroupId());
        }
        boolean groupPermissions = this.relationService.checkGroupPermissions(currentUser.getId(), userGroupRelationList);
        if (!ADMIN_ID.equals(currentUser.getId()) && !groupPermissions) {
            List<Group> oldGroup = this.groupService.getGroupByUserId(userId);
            if (oldGroup != null && oldGroup.stream().filter(Group::isSysGroup).count() == 1L && !groupIds.contains(SYSTEM_GROUP_ID)) {
                throw new UamsException(ErrorCode.UNAUTHORIZED, this.messageSource.getMessage("user.controller.permission.current.user.cancel.relationship", null, locale));
            }
            if ((oldGroup == null || oldGroup.stream().filter(Group::isSysGroup).count() == 0L) && groupIds.contains(SYSTEM_GROUP_ID)) {
                throw new UamsException(ErrorCode.UNAUTHORIZED, this.messageSource.getMessage("user.controller.permission.current.user.cancel.system.relationship", null, locale));
            }
        }
        this.relationService.delRelationByUserId(userId);
        if (groupIds != null && !groupIds.isEmpty()) {
            this.relationService.addRelationBatch(userId, groupIds);
        }
        this.relationService.upDateGroupRelation(userId, userGroupRelationList);
    }

    public Boolean checkUserPwd(HttpServletRequest request, HttpServletResponse response) throws IOException {
        Subject subject = SecurityUtils.getSubject();
        SysUser user = this.userService.findByUsername(subject.getPrincipal().toString());
        String pwd = (String)request.getSession().getAttribute("userOldPwd");
        String MD5Pwd = Md5Util.getMD5(pwd);
        if (!user.getPassword().equals(MD5Pwd)) {
            response.getWriter().println("1");
            response.getWriter().close();
            return false;
        }
        response.getWriter().println("0");
        response.getWriter().close();
        return true;
    }

    @RequestMapping(value={"changePwd"})
    public void changePwd(@RequestParam(value="userNewPwd") String userNewPwd, HttpServletRequest request, HttpServletResponse response) {
        LoginKeySingleton loginKeySingleton = LoginKeySingleton.getInstance();
        ConcurrentHashMap<String, LoginKeySingleton.CacheProper> keyMap = loginKeySingleton.getMap();
        String key = keyMap.get(request.getSession().getId()).getValue();
        if (userNewPwd != null) {
            userNewPwd = DESUtil.decryptContent(userNewPwd, key);
        }
        Boolean result = false;
        try {
            String oldPwd = request.getParameter("userOldPwd");
            oldPwd = DESUtil.decryptContent(oldPwd, key);
            request.getSession().setAttribute("userOldPwd", (Object)oldPwd);
            result = this.checkUserPwd(request, response);
        }
        catch (Exception oldPwd) {
            // empty catch block
        }
        if (result.booleanValue()) {
            Subject subject = SecurityUtils.getSubject();
            SysUser user = this.userService.findByUsername(subject.getPrincipal().toString());
            Timestamp date = new Timestamp(System.currentTimeMillis());
            this.userService.changePwd(user.getId(), Md5Util.getMD5(userNewPwd), date);
            request.getSession().removeAttribute(UPDATE_PASSWORD);
        }
    }

    @RequestMapping(value={"toUserLogin"})
    public String toUserLogin() {
        return "user/login";
    }

    @RequestMapping(value={"login"})
    public ModelAndView userLogin(@RequestParam(value="key") String key, @RequestParam(value="who") String who, HttpServletRequest request, HttpServletResponse response, String returnURL) {
        String userName = key;
        DESUtil desUtil = new DESUtil();
        try {
            byte[] by = DESUtil.parseHexStr2Byte(key);
            userName = DESUtil.deCrypt(by);
        }
        catch (Exception e) {
            log.info((Object)"\u7528\u6237\u89e3\u7801\u9519\u8bef");
            request.setAttribute("fail", (Object)"\u7528\u6237\u89e3\u7801\u9519\u8bef");
            return new ModelAndView("login");
        }
        SysUser user = this.userService.findByUsername(userName);
        if (user == null) {
            log.info((Object)("\u7cfb\u7edf\u4e0d\u5b58\u5728\u3010" + userName + "\u3011\u7528\u6237"));
            request.setAttribute("fail", (Object)("\u7cfb\u7edf\u4e0d\u5b58\u5728\u3010" + userName + "\u3011\u7528\u6237"));
            return new ModelAndView("login");
        }
        Subject currUser = SecurityUtils.getSubject();
        UamsAuthenticationToken token = new UamsAuthenticationToken(user.getUsername(), user.getPassword());
        currUser.login((AuthenticationToken)token);
        if (who.equals("tongyi")) {
            request.getSession().setAttribute("isThird", (Object)true);
        }
        if (who.equals("AAA")) {
            request.getSession().setAttribute("AAA", (Object)"true");
        }
        if (request.getSession().getAttribute("returnURL") != null) {
            returnURL = request.getSession().getAttribute("returnURL").toString();
        }
        return this.indexController.setSession(userName, request, response, this.gatewayProper.getGatewayUrl() + returnURL);
    }

    @RequestMapping(value={"resetPassword"})
    @SysManagerPermission.HasPermission(value="\u91cd\u7f6e\u5bc6\u7801", isAdmin=true)
    public void resetPassword(@RequestParam(value="id") String id, HttpServletRequest request, HttpServletResponse response) {
        try {
            Timestamp date = new Timestamp(System.currentTimeMillis());
            this.userService.changePwd(id, this.systemPassword.getDefaultpwd(), date);
            response.getWriter().print(true);
        }
        catch (Exception e) {
            e.printStackTrace();
        }
    }

    @RequestMapping(value={"checkUserExist"})
    public void checkUserExist(@RequestParam(value="isExist") String isExist, HttpServletRequest request, HttpServletResponse response) throws IOException {
        SysUser user = this.userService.findByUsername(isExist);
        if (user != null) {
            response.getWriter().println("1");
        } else {
            response.getWriter().println("0");
        }
    }

    @GetMapping
    @ResponseBody
    public List<SysUser> users() {
        Map<String, SysUser> users = this.userService.getAll().stream().collect(Collectors.toMap(SysUser::getId, Function.identity()));
        this.groupService.setGroupsForUser(users);
        return new ArrayList<SysUser>(users.values());
    }

    @GetMapping(value={"/active"})
    @ResponseBody
    public List<UserWrapper> activeUsers() {
        return this.sessionUtil.getActiveUsers().stream().map(item -> {
            item.setGroups(null);
            return UserWrapper.getInstance(item);
        }).collect(Collectors.toList());
    }

    @GetMapping(value={"/{userId}/groups"})
    @ResponseBody
    public List<Group> getGroupsByUsername(@PathVariable(value="userId") String userId, HttpServletRequest request) {
        SysUser sysUser = this.userService.getById(userId);
        if (sysUser == null) {
            Locale locale = RequestContextUtils.getLocale((HttpServletRequest)request);
            throw new UamsException(ErrorCode.RESOURCE_NOT_FOUND, this.messageSource.getMessage("user.controller.user.empty", null, locale));
        }
        return this.groupService.getGroupByUserId(userId);
    }

    @PostMapping(value={"/upload"})
    @ResponseBody
    public ExcelUploadLog upload(@RequestParam(value="file") MultipartFile file, HttpServletRequest req) {
        return this.userService.uploadUsers(file, req);
    }
}
