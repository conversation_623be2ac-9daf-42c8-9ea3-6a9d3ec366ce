/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.servlet.http.HttpServletRequest
 *  javax.servlet.http.HttpServletResponse
 *  org.springframework.stereotype.Controller
 *  org.springframework.web.bind.annotation.PostMapping
 *  org.springframework.web.bind.annotation.RequestBody
 *  org.springframework.web.bind.annotation.RequestMapping
 *  org.springframework.web.bind.annotation.ResponseBody
 */
package com.fulongtech.uams.controller;

import com.fulongtech.uams.excel.UamsExcelFactory;
import com.fulongtech.uams.excel.builder.ExcelExportBuilder;
import com.fulongtech.uams.service.UserService;
import java.util.List;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping(value={"/export"})
public class ExcelExportController {
    @Resource
    private UserService userService;

    @PostMapping(value={"/usersInfo"})
    @ResponseBody
    public void exportUsersInfo(@RequestBody List<String> userIdList, HttpServletRequest request, HttpServletResponse response) {
        String title = "\u7528\u6237\u4fe1\u606f\u5bfc\u51fa";
        String[] columnName = new String[]{"\u5e8f\u53f7", "\u7528\u6237\u540d", "\u59d3\u540d", "\u90ae\u7bb1", "\u5ea7\u673a\u53f7", "\u624b\u673a", "\u8eab\u4efd\u8bc1", "\u7528\u6237\u7ec4"};
        List<List<String>> userInfo = this.userService.exportUserInfo(userIdList);
        ExcelExportBuilder ex = UamsExcelFactory.exportBuilder(title, columnName, userInfo, request, response);
        try {
            ex.export();
        }
        catch (Exception e) {
            e.printStackTrace();
        }
    }
}
