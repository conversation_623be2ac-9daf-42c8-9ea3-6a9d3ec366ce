/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.apache.ibatis.mapping.DatabaseIdProvider
 *  org.apache.ibatis.mapping.VendorDatabaseIdProvider
 *  org.apache.log4j.PropertyConfigurator
 *  org.springframework.boot.SpringApplication
 *  org.springframework.boot.autoconfigure.SpringBootApplication
 *  org.springframework.boot.context.properties.EnableConfigurationProperties
 *  org.springframework.boot.web.servlet.ServletComponentScan
 *  org.springframework.context.annotation.Bean
 *  org.springframework.context.annotation.ComponentScan
 *  org.springframework.web.servlet.config.annotation.ContentNegotiationConfigurer
 *  org.springframework.web.servlet.config.annotation.CorsRegistry
 *  org.springframework.web.servlet.config.annotation.WebMvcConfigurerAdapter
 */
package com.fulongtech.uams.application;

import com.fulongtech.uams.excel.builder.ExcelReaderBuilder;
import com.fulongtech.uams.util.Md5Util;
import java.io.File;
import java.security.Security;
import java.util.Properties;
import org.apache.ibatis.mapping.DatabaseIdProvider;
import org.apache.ibatis.mapping.VendorDatabaseIdProvider;
import org.apache.log4j.PropertyConfigurator;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.web.servlet.config.annotation.ContentNegotiationConfigurer;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurerAdapter;

@SpringBootApplication
@EnableConfigurationProperties
@ComponentScan(basePackages={"com.fulongtech"})
@ServletComponentScan
public class Application
extends WebMvcConfigurerAdapter {
    public static void main(String[] args) {
        ExcelReaderBuilder.setDefaultPassword();
        Security.setProperty("crypto.policy", "unlimited");
        PropertyConfigurator.configure((String)(System.getProperty("user.dir") + File.separator + "config/log4j.properties"));
        if (args == null || args.length <= 0) {
            SpringApplication.run(Application.class, (String[])args);
            return;
        }
        switch (args[0]) {
            case "-setdefaultpwd": {
                if (args.length > 1 && args[1] != null) {
                    String unEncryptPwd = args[1];
                    String encryptPwd = Md5Util.getMD5(unEncryptPwd);
                    System.out.println("\u52a0\u5bc6\u540e\u5bc6\u7801\uff1a" + encryptPwd);
                    break;
                }
                System.out.println("\u53c2\u6570\u9519\u8bef\u3002");
                break;
            }
            default: {
                SpringApplication.run(Application.class, (String[])args);
            }
        }
    }

    @Bean
    public DatabaseIdProvider getDatabaseIdProvider() {
        VendorDatabaseIdProvider databaseIdProvider = new VendorDatabaseIdProvider();
        Properties p = new Properties();
        p.setProperty("Oracle", "oracle");
        p.setProperty("SQL Server", "sqlserver");
        p.setProperty("MySQL", "mysql");
        databaseIdProvider.setProperties(p);
        return databaseIdProvider;
    }

    public void configureContentNegotiation(ContentNegotiationConfigurer configurer) {
        configurer.favorPathExtension(false);
    }

    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**").allowedOrigins(new String[]{"*"}).allowedMethods(new String[]{"POST", "GET", "PUT", "OPTIONS", "DELETE"}).maxAge(3600L).allowCredentials(true);
    }
}
