/*
 * Decompiled with CFR 0.152.
 */
package com.fulongtech.uams.util;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

public class Md5Util {
    protected static char[] hexDigits = new char[]{'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f'};
    protected static MessageDigest messagedigest = null;

    public static String getMD5(String code) {
        try {
            MessageDigest messageDigest = MessageDigest.getInstance("MD5");
            byte[] bytes = code.getBytes();
            byte[] results = messageDigest.digest(bytes);
            StringBuilder stringBuilder = new StringBuilder();
            for (byte result : results) {
                stringBuilder.append(String.format("%02x", result));
            }
            return stringBuilder.toString();
        }
        catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
            return null;
        }
    }

    static {
        try {
            messagedigest = MessageDigest.getInstance("MD5");
        }
        catch (NoSuchAlgorithmException nsaex) {
            System.err.println(Md5Util.class.getName() + "\u521d\u59cb\u5316\u5931\u8d25\uff0cMessageDigest\u4e0d\u652f\u6301MD5Util\u3002");
            nsaex.printStackTrace();
        }
    }
}
