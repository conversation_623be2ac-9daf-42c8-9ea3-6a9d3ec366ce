/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  io.jsonwebtoken.Jwts
 *  io.jsonwebtoken.SignatureAlgorithm
 *  org.apache.commons.codec.binary.Base64
 */
package com.fulongtech.uams.util;

import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import java.io.IOException;
import java.security.Key;
import java.security.KeyFactory;
import java.security.NoSuchAlgorithmException;
import java.security.PrivateKey;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.util.Map;
import javax.crypto.Cipher;
import org.apache.commons.codec.binary.Base64;
import sun.misc.BASE64Decoder;

public class JwtUtils {
    private static final String PRIVATE_KEY = "MIIEvwIBADANBgkqhkiG9w0BAQEFAASCBKkwggSlAgEAAoIBAQCiAGW/OXgLfzFp4xzCfJs23lmwcBHhstKRBhcOv5+w32Nt7vymheVh9O1qPG9hgWRPnKPFs5o5gLeYhvEfkPH562pXiCqBBpqARguV2XBKsgmkGzmBJwFBvf1VEhLaC2z1AJU4J5fL83mfjV8rC/ZgxabkAof0/ZsYWBHV2tsyNEIMjjqiHqo7/BMafoE1U5ApfDW042ZqNANSdUAX42yHOTCEgjTx3zZ3lqFLxA6NEbiSbetKvtjxENQbe2PZNE561VlwnZGMA6GkoOfX47oYphqn787+O92S0Ap9EkGPIl5CmnXquJngm86UYOTBQXEec33D8nPCr7eCkRG5UZO3AgMBAAECggEAHQNHNZT8O8retaD38JjST2RI2cFoYmxdK53UmU6WMd3/95s6YjROnVl4/3KtW+AJb/yUk/6TW0MDHmzkWAvkWibgF6cKPatKFjj1MDQN2ULDWUGK7ednYTe+W/Ltr2EmdUZScwkcNWl4Wau+5H2y9SfQuLr/UE/u69Ax+YPFh9rsFi0k0UFMd37yIQng7U7p4K0+0FzXKyzPp2rBgawYsOpGszLt7wyIP03H/74P1C+5BdXOOBK6EW47qjtl2lUupJIfVm9j7X9/6j1tFzKdXxwc1ZCWVJ+kk1gjwezKKvAD1OwF61FJ2NYAaNlp6JJHci/Q4TV947SOm7WqapqNCQKBgQDwtEHo1wtvgZCOCcKXfSPHYHfTje3jczxEtptNMxISbd3r7ZZfoQU8Ct2PDIIXukND/BPicXy9lKriTkKL7aTFbVSykbI+q4RM4WRZqVcmOAaG/HI6ZQlOL6ih/9ulHNmBaflDeB2UWott+UkMSYvymRTxCnwu5VqFWThCFdmS9QKBgQCsS9Emnq+7Zd7B5cmLuvA40TzBTUoE33SeTRp37w5UH1r4kQpNBF7j5aqU+RbUo79QUxxqkPRpZghDPDJoQHi4oPPW8+LJntX6iOI6McicptAK/C7z6NTVEy2I8WmPeYAM63pVKfgb4Kvz1fdDwg9QI80nWD2Z1l1/zd00DV0YewKBgQCdW3prGYqkTvONp9vK5Uhqoyoy52pJc6BG24oTofuCiMi34A6aXc3qPsAxMGAr2tz2qOjm9IIz/ejpIXhnuegqGRtx0cXRKpfHGuJbq1UJ6z55KLgUbND0iDuaDIYvHvkinYETFodf4QGd14Zlvf77jhAetFgS4GWZH4ajzVz7sQKBgQCrC3VuFpk0u/QBoAKOwh2a+jWLXRQ/dCqMwNSm/d7Gqn4gR7TnMpCVRDotPH9qx8i0kF2keZT6UX2gadFM57ww5jBMU7/wEswwtNWJmkSjJ4NfdG2UXK91fF13FtqJwb/ak6bVhx21Zzyti9u2G6AiGfift50n7MTTjOattxqVowKBgQCjYGMpdspQY6ctTGbv/GDRzRqvD2KbAzW9jXSNMDpcdb/XONsvAoT9oeaBL7EJ6faAHRtQ+RFb8jNbw/PZVMQv6F57lAxcJPFviOxHNaiMBWey2G0gVX7ynUv3NxpuNbGcVoRCrWrzxBI8QqG7KFpTPZbJFRo3NEI9NJcxvoCtUg==";

    public static String createToken(Map<String, Object> claims) {
        String s = null;
        try {
            s = Jwts.builder().setIssuer("UAMS").addClaims(claims).signWith((Key)JwtUtils.strToPrivateKey(), SignatureAlgorithm.RS256).compact();
        }
        catch (Exception e) {
            e.printStackTrace();
        }
        return s;
    }

    private static PrivateKey strToPrivateKey() {
        PrivateKey privateKey = null;
        try {
            byte[] keyBytes = new byte[]{};
            keyBytes = new BASE64Decoder().decodeBuffer(PRIVATE_KEY);
            PKCS8EncodedKeySpec pkcs8EncodedKeySpec = new PKCS8EncodedKeySpec(keyBytes);
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            privateKey = keyFactory.generatePrivate(pkcs8EncodedKeySpec);
        }
        catch (IOException | NoSuchAlgorithmException | InvalidKeySpecException e) {
            e.printStackTrace();
        }
        return privateKey;
    }

    public static String decrypt(String data) throws Exception {
        byte[] inputByte = Base64.decodeBase64((byte[])data.getBytes("UTF-8"));
        Cipher cipher = Cipher.getInstance("RSA");
        cipher.init(2, JwtUtils.strToPrivateKey());
        String outStr = new String(cipher.doFinal(inputByte));
        return outStr;
    }
}
