/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  cn.hutool.core.util.ObjectUtil
 *  cn.hutool.core.util.StrUtil
 *  org.springframework.context.MessageSource
 *  org.springframework.util.StringUtils
 */
package com.fulongtech.uams.util;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.fulongtech.uams.exception.ErrorCode;
import com.fulongtech.uams.exception.UamsException;
import com.fulongtech.uams.model.ExcelLogType;
import com.fulongtech.uams.model.ExcelUploadLog;
import com.fulongtech.uams.model.Group;
import com.fulongtech.uams.model.SysUser;
import java.util.List;
import java.util.Locale;
import java.util.regex.Pattern;
import org.springframework.context.MessageSource;
import org.springframework.util.StringUtils;

public class SysUserValidator {
    private static final String CHARACTER_REGEX = "[`~!@$%^&*()+=|{}':;',\\[\\]\\\\.<>?~\uff01@\uffe5%\u2026\u2026&*\uff08\uff09\u2014\u2014+|{}\u3010\u3011\u2018\uff1b\uff1a\u201d\u201c\u2019\u3002\uff0c\u3001\uff1f]";
    private static final String USERNAME_REGEX = "[\u0430-\u044f\u0410-\u042f.0-9a-zA-Z-\u0430]+$";
    private static final String ALPHABET_NUMBERS = "^[A-Za-z0-9]+$";
    private static final String NUMBERS = "^[0-9]*$";
    public static final String SPACE = " ";
    private static final String PHONE_REGEX = "^[0][1-9][0-9]{1,2}-[0-9]{5,8}$";
    private static final String EMAIL_REGEX = "^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\\.[a-zA-Z0-9_-]+)+$";
    private static final String ID_CARD_REGEX = "^\\d{1,17}[0-9Xx]$";
    private static final String ADMIN_ID = "BEFE2FF1A20F4C419C8A94B7213C5219";

    public static void checkUserProperty(SysUser sysUser, boolean isAdd, MessageSource messageSource, Locale locale, boolean isEnableSmsLogin) {
        boolean b;
        Pattern p = Pattern.compile(CHARACTER_REGEX);
        if (isAdd) {
            if (!StringUtils.isEmpty((Object)sysUser.getUsername())) {
                if (sysUser.getUsername().length() < 2 || sysUser.getUsername().length() > 20) {
                    throw new UamsException(ErrorCode.UNPROCESABLE_ENTITY, messageSource.getMessage("user.validator.username.length", null, locale));
                }
                Pattern compile = Pattern.compile(USERNAME_REGEX);
                if (!compile.matcher(sysUser.getUsername()).find()) {
                    throw new UamsException(ErrorCode.UNPROCESABLE_ENTITY, messageSource.getMessage("user.validator.username.character", null, locale));
                }
            } else {
                throw new UamsException(ErrorCode.UNPROCESABLE_ENTITY, messageSource.getMessage("user.validator.username.empty", null, locale));
            }
        }
        if (StrUtil.isBlank((CharSequence)sysUser.getName()) || sysUser.getName().contains(SPACE)) {
            throw new UamsException(ErrorCode.UNPROCESABLE_ENTITY, messageSource.getMessage("user.validator.name.empty", null, locale));
        }
        if (p.matcher(sysUser.getName()).find()) {
            throw new UamsException(ErrorCode.UNPROCESABLE_ENTITY, messageSource.getMessage("user.validator.name.character", null, locale) + StrUtil.strip((CharSequence)CHARACTER_REGEX, (CharSequence)"[", (CharSequence)"]"));
        }
        if (sysUser.getName().length() > 20 || sysUser.getName().length() < 2) {
            throw new UamsException(ErrorCode.UNPROCESABLE_ENTITY, messageSource.getMessage("user.validator.name.length", null, locale));
        }
        if (!StringUtils.isEmpty((Object)sysUser.getIdCard())) {
            if (sysUser.getIdCard().length() != 18) {
                throw new UamsException(ErrorCode.UNPROCESABLE_ENTITY, messageSource.getMessage("user.validator.id.card", null, locale));
            }
            if (!Pattern.compile(ID_CARD_REGEX).matcher(sysUser.getIdCard()).find()) {
                throw new UamsException(ErrorCode.UNPROCESABLE_ENTITY, messageSource.getMessage("user.validator.id.card", null, locale));
            }
        }
        if (!StringUtils.isEmpty((Object)sysUser.getMobilePhone())) {
            if (sysUser.getMobilePhone().length() != 11) {
                throw new UamsException(ErrorCode.UNPROCESABLE_ENTITY, messageSource.getMessage("user.validator.mobile.phone.length", null, locale));
            }
            if (!Pattern.compile(NUMBERS).matcher(sysUser.getMobilePhone()).find()) {
                throw new UamsException(ErrorCode.UNPROCESABLE_ENTITY, messageSource.getMessage("user.validator.mobile.phone.context", null, locale));
            }
        } else if (isEnableSmsLogin) {
            throw new UamsException(ErrorCode.UNPROCESABLE_ENTITY, messageSource.getMessage("user.validator.mobile.phone.empty", null, locale));
        }
        if (!StringUtils.isEmpty((Object)sysUser.getPhone()) && !(b = Pattern.compile(PHONE_REGEX).matcher(sysUser.getPhone()).matches())) {
            throw new UamsException(ErrorCode.UNPROCESABLE_ENTITY, messageSource.getMessage("user.validator.mobile.model", null, locale));
        }
        if (!StringUtils.isEmpty((Object)sysUser.getEmail())) {
            if (sysUser.getEmail().length() > 100) {
                throw new UamsException(ErrorCode.UNPROCESABLE_ENTITY, messageSource.getMessage("user.validator.email.length", null, locale));
            }
            b = Pattern.compile(EMAIL_REGEX).matcher(sysUser.getEmail()).find();
            if (!b) {
                throw new UamsException(ErrorCode.UNPROCESABLE_ENTITY, messageSource.getMessage("user.validator.email.model", null, locale));
            }
        }
    }

    public static boolean checkUserProperty(SysUser sysUser, ExcelUploadLog log, int rowNum) {
        boolean b;
        boolean isError = false;
        String admin = "admin";
        Pattern p = Pattern.compile(CHARACTER_REGEX);
        if (!StringUtils.isEmpty((Object)sysUser.getUsername())) {
            Pattern compile;
            if (admin.equals(sysUser.getUsername())) {
                isError = true;
                log.addError(ExcelLogType.PERMISSION, rowNum, "\u7b2c" + rowNum + "\u884c\uff1a\u65e0\u6cd5\u4fee\u6539admin\u7528\u6237");
            }
            if (sysUser.getUsername().length() < 2 || sysUser.getUsername().length() > 20) {
                isError = true;
                log.addError(ExcelLogType.LINE, rowNum, "\u7528\u6237\u540d\u957f\u5ea6\u9700\u8981\u57282-20\u4e2a\u5b57\u7b26\u4e4b\u95f4");
            }
            if (!(compile = Pattern.compile(USERNAME_REGEX)).matcher(sysUser.getUsername()).find()) {
                isError = true;
                log.addError(ExcelLogType.LINE, rowNum, "\u7528\u6237\u540d\u53ea\u80fd\u662f\u6570\u5b57\u548c\u82f1\u8bed\u548c\u4e0b\u5212\u7ebf\u7ec4\u6210");
            }
            if (StrUtil.isBlank((CharSequence)sysUser.getUsername()) || sysUser.getUsername().contains(SPACE)) {
                isError = true;
                log.addError(ExcelLogType.LINE, rowNum, "\u7528\u6237\u540d\u4e3a\u7a7a\u6216\u8005\u7528\u6237\u540d\u4e2d\u6709\u7a7a\u683c\u3001\u5168\u89d2\u7a7a\u683c\u3001\u5236\u8868\u7b26\u3001\u6362\u884c\u7b26\uff0c\u7b49\u4e0d\u53ef\u89c1\u5b57\u7b26");
            }
        } else {
            isError = true;
            log.addError(ExcelLogType.LINE, rowNum, "\u7528\u6237\u540d\u4e0d\u80fd\u4e3a\u7a7a");
        }
        if (!StringUtils.isEmpty((Object)sysUser.getName())) {
            if (p.matcher(sysUser.getName()).find()) {
                isError = true;
                log.addError(ExcelLogType.LINE, rowNum, "\u7528\u6237\u59d3\u540d\u4e0d\u80fd\u542b\u6709\u7279\u6b8a\u5b57\u7b26" + StrUtil.strip((CharSequence)CHARACTER_REGEX, (CharSequence)"[", (CharSequence)"]"));
            }
            if (StrUtil.isBlank((CharSequence)sysUser.getName()) || sysUser.getName().contains(SPACE)) {
                isError = true;
                log.addError(ExcelLogType.LINE, rowNum, "\u7528\u6237\u59d3\u540d\u4e3a\u7a7a\u6216\u8005\u7528\u6237\u59d3\u540d\u6709\u7a7a\u683c\u3001\u5168\u89d2\u7a7a\u683c\u3001\u5236\u8868\u7b26\u3001\u6362\u884c\u7b26\uff0c\u7b49\u4e0d\u53ef\u89c1\u5b57\u7b26");
            }
            if (sysUser.getName().length() > 20 || sysUser.getName().length() < 2) {
                isError = true;
                log.addError(ExcelLogType.LINE, rowNum, "\u7528\u6237\u59d3\u540d\u957f\u5ea6\u9700\u8981\u57282-20\u4e2a\u5b57\u7b26\u4e4b\u95f4");
            }
        } else {
            isError = true;
            log.addError(ExcelLogType.LINE, rowNum, "\u59d3\u540d\u4e0d\u80fd\u4e3a\u7a7a");
        }
        if (!StringUtils.isEmpty((Object)sysUser.getIdCard())) {
            if (sysUser.getIdCard().contains("e") || sysUser.getIdCard().contains("E")) {
                isError = true;
                log.addError(ExcelLogType.LINE, rowNum, "\u8eab\u4efd\u8bc1\u5355\u5143\u683c\u8bf7\u8bbe\u7f6e\u4e3a\u6587\u672c\u683c\u5f0f");
            } else if (sysUser.getIdCard().length() != 18) {
                isError = true;
                log.addError(ExcelLogType.LINE, rowNum, "\u8eab\u4efd\u8bc1\u683c\u5f0f\u53ea\u80fd\u4e3a18\u4f4d\u7eaf\u6570\u5b57\uff0c\u6216\u6700\u540e\u4e00\u4f4d\u4e3ax");
            } else if (!Pattern.compile(ID_CARD_REGEX).matcher(sysUser.getIdCard()).find()) {
                isError = true;
                log.addError(ExcelLogType.LINE, rowNum, "\u8eab\u4efd\u8bc1\u683c\u5f0f\u53ea\u80fd\u4e3a18\u4f4d\u7eaf\u6570\u5b57\uff0c\u6216\u6700\u540e\u4e00\u4f4d\u4e3ax");
            }
        }
        if (!StringUtils.isEmpty((Object)sysUser.getPassword()) && (StrUtil.isBlank((CharSequence)sysUser.getPassword()) || sysUser.getPassword().contains(SPACE))) {
            isError = true;
            log.addError(ExcelLogType.LINE, rowNum, "\u5bc6\u7801\u4e3a\u7a7a\u6216\u8005\u5bc6\u7801\u4e2d\u6709\u7a7a\u683c\u3001\u5168\u89d2\u7a7a\u683c\u3001\u5236\u8868\u7b26\u3001\u6362\u884c\u7b26\uff0c\u7b49\u4e0d\u53ef\u89c1\u5b57\u7b26");
        }
        if (!StringUtils.isEmpty((Object)sysUser.getMobilePhone())) {
            if (sysUser.getMobilePhone().length() != 11) {
                isError = true;
                log.addError(ExcelLogType.LINE, rowNum, "\u624b\u673a\u53f7\u7801\u957f\u5ea6\u53ea\u80fd\u4e3a11\u4f4d");
            }
            if (!Pattern.compile(NUMBERS).matcher(sysUser.getMobilePhone()).find()) {
                isError = true;
                log.addError(ExcelLogType.LINE, rowNum, "\u624b\u673a\u53f7\u7801\u53ea\u80fd\u662f\u7eaf\u6570\u5b57");
            }
        }
        if (!StringUtils.isEmpty((Object)sysUser.getPhone()) && !(b = Pattern.compile(PHONE_REGEX).matcher(sysUser.getPhone()).find())) {
            isError = true;
            log.addError(ExcelLogType.LINE, rowNum, "\u5ea7\u673a\u53f7\u683c\u5f0f\u53ea\u80fd\u662f\uff1a\u533a\u53f7-\u5ea7\u673a\u53f7\u7801");
        }
        if (!StringUtils.isEmpty((Object)sysUser.getEmail())) {
            if (sysUser.getEmail().length() > 100) {
                isError = true;
                log.addError(ExcelLogType.LINE, rowNum, "\u90ae\u7bb1\u957f\u5ea6\u4e0d\u80fd\u5927\u4e8e100\u4f4d");
            }
            if (!(b = Pattern.compile(EMAIL_REGEX).matcher(sysUser.getEmail()).find())) {
                isError = true;
                log.addError(ExcelLogType.LINE, rowNum, "\u90ae\u7bb1\u683c\u5f0f\u53ea\u80fd\u6709\u6570\u5b57\u548c\u5b57\u6bcd\uff0c\u5e76\u4e14\u6709@\u548c.\u7b26\u53f7");
            }
        }
        return isError;
    }

    public static boolean checkGroupName(String groupName, ExcelUploadLog log, int row) {
        String SystemGroupName = "\u7cfb\u7edf\u7ba1\u7406\u7ec4";
        if (groupName.length() > 20) {
            log.addError(ExcelLogType.LINE, row, "\u7528\u6237\u7ec4\u540d\u5b57\u957f\u5ea6\u4e0d\u80fd\u8d85\u8fc720");
            return false;
        }
        if (SystemGroupName.equals(groupName)) {
            log.addError(ExcelLogType.PERMISSION, row, "\u7b2c" + row + "\u884c\uff1a\u65e0\u6cd5\u901a\u8fc7\u5bfc\u5165\u6539\u53d8\u7cfb\u7edf\u7ba1\u7406\u7ec4\u6210\u5458");
            return false;
        }
        return true;
    }

    public static boolean checkIdentity(SysUser currentUser) {
        if (ADMIN_ID.equals(currentUser.getId())) {
            return true;
        }
        List<Group> groups = currentUser.getGroups();
        return groups != null && groups.size() > 0 && groups.stream().anyMatch(Group::isSysGroup);
    }

    public static boolean checkMobilePhone(String mobilePhone) {
        if (ObjectUtil.isNotNull((Object)mobilePhone) && ObjectUtil.isNotEmpty((Object)mobilePhone)) {
            if (mobilePhone.length() != 11) {
                throw new UamsException(ErrorCode.UNPROCESABLE_ENTITY, "\u624b\u673a\u53f7\u7801\u957f\u5ea6\u53ea\u80fd\u4e3a11\u4f4d");
            }
            if (!Pattern.compile(NUMBERS).matcher(mobilePhone).find()) {
                throw new UamsException(ErrorCode.UNPROCESABLE_ENTITY, "\u624b\u673a\u53f7\u7801\u53ea\u80fd\u662f\u7eaf\u6570\u5b57");
            }
            return true;
        }
        return false;
    }
}
