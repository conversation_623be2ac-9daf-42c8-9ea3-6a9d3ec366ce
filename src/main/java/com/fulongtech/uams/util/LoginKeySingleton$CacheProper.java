/*
 * Decompiled with CFR 0.152.
 */
package com.fulongtech.uams.util;

public class LoginKeySingleton.CacheProper {
    private String key;
    private String value;
    private long time;

    public LoginKeySingleton.CacheProper(String key, String value, long time) {
        this.key = key;
        this.value = value;
        this.time = time;
    }

    public String getKey() {
        return this.key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getValue() {
        return this.value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public long getTime() {
        return this.time;
    }

    public void setTime(long time) {
        this.time = time;
    }
}
