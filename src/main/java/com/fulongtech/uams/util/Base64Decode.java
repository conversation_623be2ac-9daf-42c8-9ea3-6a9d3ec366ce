/*
 * Decompiled with CFR 0.152.
 */
package com.fulongtech.uams.util;

import java.io.ByteArrayOutputStream;

public class Base64Decode {
    private static byte[] base64DecodeChars = new byte[]{-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 62, -1, -1, -1, 63, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, -1, -1, -1, -1, -1, -1, -1, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, -1, -1, -1, -1, -1, -1, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, -1, -1, -1, -1, -1};

    public static byte[] decode(String str) {
        byte[] data = str.getBytes();
        int len = data.length;
        ByteArrayOutputStream buf = new ByteArrayOutputStream(len);
        int i = 0;
        while (i < len) {
            byte b4;
            byte b3;
            byte b2;
            byte b1;
            do {
                b1 = base64DecodeChars[data[i++]];
            } while (i < len && b1 == -1);
            if (b1 == -1) break;
            do {
                b2 = base64DecodeChars[data[i++]];
            } while (i < len && b2 == -1);
            if (b2 == -1) break;
            buf.write(b1 << 2 | (b2 & 0x30) >>> 4);
            do {
                if ((b3 = data[i++]) == 61) {
                    return buf.toByteArray();
                }
                b3 = base64DecodeChars[b3];
            } while (i < len && b3 == -1);
            if (b3 == -1) break;
            buf.write((b2 & 0xF) << 4 | (b3 & 0x3C) >>> 2);
            do {
                if ((b4 = data[i++]) == 61) {
                    return buf.toByteArray();
                }
                b4 = base64DecodeChars[b4];
            } while (i < len && b4 == -1);
            if (b4 == -1) break;
            buf.write((b3 & 3) << 6 | b4);
        }
        return buf.toByteArray();
    }

    public static String unescape(String src) {
        StringBuffer tmp = new StringBuffer();
        tmp.ensureCapacity(src.length());
        int lastPos = 0;
        int pos = 0;
        while (lastPos < src.length()) {
            pos = src.indexOf("%", lastPos);
            if (pos == lastPos) {
                char ch;
                if (src.charAt(pos + 1) == 'u') {
                    ch = (char)Integer.parseInt(src.substring(pos + 2, pos + 6), 16);
                    tmp.append(ch);
                    lastPos = pos + 6;
                    continue;
                }
                ch = (char)Integer.parseInt(src.substring(pos + 1, pos + 3), 16);
                tmp.append(ch);
                lastPos = pos + 3;
                continue;
            }
            if (pos == -1) {
                tmp.append(src.substring(lastPos));
                lastPos = src.length();
                continue;
            }
            tmp.append(src.substring(lastPos, pos));
            lastPos = pos;
        }
        return tmp.toString();
    }
}
