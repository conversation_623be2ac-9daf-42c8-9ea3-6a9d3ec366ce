/*
 * Decompiled with CFR 0.152.
 */
package com.fulongtech.uams.util;

import java.awt.Color;
import java.awt.Font;
import java.awt.Graphics;
import java.awt.image.BufferedImage;
import java.util.Random;

public class CaptchaUtil {
    private BufferedImage image;
    private String str;
    private static char[] code = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ123456789".toCharArray();
    public static final String SESSION_CODE_NAME = "code";

    private CaptchaUtil() {
        this.init();
    }

    public static CaptchaUtil Instance() {
        return new CaptchaUtil();
    }

    public BufferedImage getImage() {
        return this.image;
    }

    public String getString() {
        return this.str;
    }

    private void init() {
        int width = 90;
        int height = 37;
        BufferedImage image = new BufferedImage(width, height, 1);
        Graphics g = image.getGraphics();
        Random random = new Random();
        g.setColor(this.getRandColor(200, 250));
        g.fillRect(0, 0, width, height);
        g.setFont(new Font("Times New Roman", 1, 30));
        g.setColor(this.getRandColor(160, 200));
        for (int i = 0; i < 155; ++i) {
            int x = random.nextInt(width);
            int y = random.nextInt(height);
            int x1 = random.nextInt(12);
            int y1 = random.nextInt(12);
            g.drawLine(x, y, x + x1, y + y1);
        }
        String sRand = "";
        for (int i = 0; i < 4; ++i) {
            String rand = String.valueOf(code[random.nextInt(code.length)]);
            sRand = sRand + rand;
            g.setColor(new Color(20 + random.nextInt(110), 20 + random.nextInt(110), 20 + random.nextInt(110)));
            g.drawString(rand, 17 * i + 10, 30);
        }
        this.str = sRand;
        g.dispose();
        this.image = image;
    }

    public Color getRandColor(int fc, int bc) {
        Random random = new Random();
        if (fc > 255) {
            fc = 255;
        }
        if (bc > 255) {
            bc = 255;
        }
        int r = fc + random.nextInt(bc - fc);
        int g = fc + random.nextInt(bc - fc);
        int b = fc + random.nextInt(bc - fc);
        return new Color(r, g, b);
    }
}
