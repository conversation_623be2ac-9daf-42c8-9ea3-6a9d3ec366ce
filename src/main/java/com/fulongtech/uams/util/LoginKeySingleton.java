/*
 * Decompiled with CFR 0.152.
 */
package com.fulongtech.uams.util;

import java.util.concurrent.ConcurrentHashMap;

public class LoginKeySingleton {
    private static final long DEFAULT_TIMEOUT = 300000L;
    private long cacheTimeout;
    private static LoginKeySingleton loginKeySingleton;
    private ConcurrentHashMap<String, CacheProper> map = new ConcurrentHashMap();

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     * Enabled force condition propagation
     * Lifted jumps to return sites
     */
    public static final LoginKeySingleton getInstance() {
        if (loginKeySingleton != null) return loginKeySingleton;
        Class<LoginKeySingleton> clazz = LoginKeySingleton.class;
        synchronized (LoginKeySingleton.class) {
            if (loginKeySingleton != null) return loginKeySingleton;
            loginKeySingleton = new LoginKeySingleton(300000L);
            // ** MonitorExit[var0] (shouldn't be in output)
            return loginKeySingleton;
        }
    }

    private LoginKeySingleton(long timeOut) {
        this.cacheTimeout = timeOut;
        new ClearThread().start();
    }

    public ConcurrentHashMap<String, CacheProper> getMap() {
        return this.map;
    }

    public void remove(String key) {
        if (this.map.containsKey(key)) {
            this.map.remove(key);
        }
    }

    public String put(String key, String value) {
        long time = System.currentTimeMillis();
        CacheProper entry = new CacheProper(key, value, time);
        this.map.put(key, entry);
        return value;
    }

    public class CacheProper {
        private String key;
        private String value;
        private long time;

        public CacheProper(String key, String value, long time) {
            this.key = key;
            this.value = value;
            this.time = time;
        }

        public String getKey() {
            return this.key;
        }

        public void setKey(String key) {
            this.key = key;
        }

        public String getValue() {
            return this.value;
        }

        public void setValue(String value) {
            this.value = value;
        }

        public long getTime() {
            return this.time;
        }

        public void setTime(long time) {
            this.time = time;
        }
    }

    private class ClearThread
    extends Thread {
        ClearThread() {
            this.setName("clear cache thread");
        }

        @Override
        public void run() {
            while (true) {
                try {
                    while (true) {
                        Object[] keys;
                        long now = System.currentTimeMillis();
                        for (Object key : keys = ((ConcurrentHashMap.CollectionView)((Object)LoginKeySingleton.this.map.keySet())).toArray()) {
                            CacheProper entry = (CacheProper)LoginKeySingleton.this.map.get(key);
                            if (now - entry.getTime() < LoginKeySingleton.this.cacheTimeout || !LoginKeySingleton.this.map.containsKey(key)) continue;
                            LoginKeySingleton.this.map.remove(key);
                        }
                        Thread.sleep(LoginKeySingleton.this.cacheTimeout);
                    }
                }
                catch (Exception e) {
                    e.printStackTrace();
                    continue;
                }
                break;
            }
        }
    }
}
