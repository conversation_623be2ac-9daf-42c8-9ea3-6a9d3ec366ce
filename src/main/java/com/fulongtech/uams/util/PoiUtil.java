/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.apache.poi.hssf.usermodel.HSSFDateUtil
 *  org.apache.poi.ss.usermodel.Cell
 *  org.apache.poi.ss.usermodel.FormulaEvaluator
 *  org.apache.tools.ant.util.DateUtils
 *  org.springframework.util.StringUtils
 */
package com.fulongtech.uams.util;

import java.text.DecimalFormat;
import java.util.Date;
import org.apache.poi.hssf.usermodel.HSSFDateUtil;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.FormulaEvaluator;
import org.apache.tools.ant.util.DateUtils;
import org.springframework.util.StringUtils;

public class PoiUtil {
    private static FormulaEvaluator evaluator;

    public static String getCellValueByCell(Cell cell) {
        if (cell == null || cell.toString().trim().equals("")) {
            return "";
        }
        String cellValue = "";
        int cellType = cell.getCellType();
        if (cellType == 2) {
            cellType = evaluator.evaluate(cell).getCellType();
        }
        switch (cellType) {
            case 1: {
                cellValue = cell.getStringCellValue().trim();
                cellValue = StringUtils.isEmpty((Object)cellValue) ? "" : cellValue;
                break;
            }
            case 4: {
                cellValue = String.valueOf(cell.getBooleanCellValue());
                break;
            }
            case 0: {
                if (HSSFDateUtil.isCellDateFormatted((Cell)cell)) {
                    cellValue = DateUtils.format((Date)cell.getDateCellValue(), (String)"yyyy-MM-dd");
                    break;
                }
                cellValue = new DecimalFormat("#.######").format(cell.getNumericCellValue());
                break;
            }
            default: {
                cellValue = "";
            }
        }
        return cellValue;
    }
}
