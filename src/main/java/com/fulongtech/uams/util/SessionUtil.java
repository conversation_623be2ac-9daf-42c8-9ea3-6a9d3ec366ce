/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.apache.shiro.SecurityUtils
 *  org.apache.shiro.session.Session
 *  org.apache.shiro.subject.Subject
 *  org.springframework.data.redis.core.RedisTemplate
 *  org.springframework.session.ExpiringSession
 *  org.springframework.session.data.redis.RedisOperationsSessionRepository
 *  org.springframework.session.data.redis.RedisOperationsSessionRepository$RedisSession
 *  org.springframework.stereotype.Component
 */
package com.fulongtech.uams.util;

import com.fulongtech.uams.model.SysUser;
import com.fulongtech.uams.properties.SecurityProper;
import com.fulongtech.uams.service.UserService;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Set;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.session.Session;
import org.apache.shiro.subject.Subject;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.session.ExpiringSession;
import org.springframework.session.data.redis.RedisOperationsSessionRepository;
import org.springframework.stereotype.Component;

@Component
public class SessionUtil {
    @Resource
    private SecurityProper securityProper;
    private int maxSession;
    @Resource
    private RedisTemplate<Object, Object> sessionRedisTemplate;
    @Resource
    private RedisOperationsSessionRepository sessionRepository;
    @Resource
    private UserService userService;

    @PostConstruct
    private void init() {
        this.maxSession = this.securityProper.getMaxSession();
    }

    private List<ExpiringSession> getAllSessions() {
        Set sessionsKey = this.sessionRedisTemplate.keys((Object)"spring:session:sessions*");
        return sessionsKey.parallelStream().filter(key -> ((String)key).contains("expires")).map(key -> {
            int beginIndex = ((String)key).lastIndexOf(":") + 1;
            int endIndex = ((String)key).length();
            String sessionId = ((String)key).substring(beginIndex, endIndex);
            RedisOperationsSessionRepository.RedisSession session = this.sessionRepository.getSession(sessionId);
            return session;
        }).filter(session -> session != null).collect(Collectors.toList());
    }

    public List<ExpiringSession> getSessionByUsername(String username) {
        ArrayList<ExpiringSession> sessionList = new ArrayList<ExpiringSession>();
        List<ExpiringSession> sessions = this.getAllSessions();
        for (int i = 0; i < sessions.size(); ++i) {
            ExpiringSession session = sessions.get(i);
            SysUser currentUser = (SysUser)session.getAttribute("currentUser");
            if (currentUser == null || !currentUser.getUsername().equals(username) || session.getAttribute("kickout") != null || session.isExpired() || session.getAttribute("isLogin") != null) continue;
            sessionList.add(session);
        }
        return sessionList;
    }

    private List<ExpiringSession> getAccountSessionsSorted(String username, List<ExpiringSession> sessions) {
        ArrayList<ExpiringSession> sessionList = new ArrayList<ExpiringSession>();
        for (int i = 0; i < sessions.size(); ++i) {
            ExpiringSession session = sessions.get(i);
            SysUser currentUser = (SysUser)session.getAttribute("currentUser");
            if (currentUser == null || !currentUser.getUsername().equals(username) || session.getAttribute("kickout") != null || session.getAttribute("isThird") != null && ((Boolean)session.getAttribute("isThird")).booleanValue()) continue;
            sessionList.add(session);
        }
        sessionList.sort(Comparator.comparing(ExpiringSession::getLastAccessedTime));
        return sessionList;
    }

    private void kickOutSessions(List<ExpiringSession> sessions) {
        if (this.maxSession > 0) {
            for (int i = sessions.size() - 1 - this.maxSession; i >= 0; --i) {
                ExpiringSession expiringSession = sessions.get(i);
                if (expiringSession.isExpired()) continue;
                expiringSession.setAttribute("kickout", (Object)true);
            }
        }
    }

    public void kicOut() {
        Subject subject = SecurityUtils.getSubject();
        Session session = subject.getSession();
        String userName = (String)subject.getPrincipal();
        List<ExpiringSession> allSessions = this.getAllSessions();
        List<ExpiringSession> sessionsSorted = this.getAccountSessionsSorted(userName, allSessions);
        RedisOperationsSessionRepository.RedisSession currentSession = this.sessionRepository.getSession((String)((Object)session.getId()));
        sessionsSorted.add((ExpiringSession)currentSession);
        if (sessionsSorted.size() > this.maxSession) {
            this.kickOutSessions(sessionsSorted);
        }
    }

    public Set<SysUser> getActiveUsers() {
        List<ExpiringSession> allSessions = this.getAllSessions();
        Predicate<ExpiringSession> predicate = session -> !session.isExpired();
        predicate = predicate.and(session -> session.getAttribute("kickout") == null);
        return allSessions.parallelStream().filter(predicate).filter(session -> session.getAttribute("currentUser") != null).map(session -> (SysUser)session.getAttribute("currentUser")).collect(Collectors.toSet());
    }

    public void deleteSessionByUsername(String username) {
        Set<SysUser> activeUsers = this.getActiveUsers();
        boolean isOnline = false;
        for (SysUser activeUser : activeUsers) {
            if (!username.equals(activeUser.getUsername())) continue;
            isOnline = true;
            break;
        }
        if (isOnline) {
            List<ExpiringSession> accountSessionsSorted = this.getAccountSessionsSorted(username, this.getAllSessions());
            for (ExpiringSession expiringSession : accountSessionsSorted) {
                if (expiringSession.isExpired()) continue;
                String key = "spring:session:sessions:" + expiringSession.getId();
                this.sessionRedisTemplate.delete((Object)key);
            }
        }
    }
}
