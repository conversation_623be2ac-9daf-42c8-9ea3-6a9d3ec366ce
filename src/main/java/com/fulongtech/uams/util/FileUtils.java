/*
 * Decompiled with CFR 0.152.
 */
package com.fulongtech.uams.util;

import java.io.File;
import java.io.FileFilter;

public class FileUtils {
    public static File[] scanFile(File folder, final String key) {
        File[] files = folder.listFiles(new FileFilter(){

            @Override
            public boolean accept(File pathname) {
                String name;
                return pathname.isFile() && (name = pathname.getName()).contains(key);
            }
        });
        return files;
    }
}
