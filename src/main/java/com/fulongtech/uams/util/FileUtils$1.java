/*
 * Decompiled with CFR 0.152.
 */
package com.fulongtech.uams.util;

import java.io.File;
import java.io.FileFilter;

static final class FileUtils.1
implements FileFilter {
    final /* synthetic */ String val$key;

    FileUtils.1(String string) {
        this.val$key = string;
    }

    @Override
    public boolean accept(File pathname) {
        String name;
        return pathname.isFile() && (name = pathname.getName()).contains(this.val$key);
    }
}
