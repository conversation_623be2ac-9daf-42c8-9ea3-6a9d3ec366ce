/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.apache.log4j.Logger
 *  org.springframework.stereotype.Component
 */
package com.fulongtech.uams.util;

import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.Base64;
import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Component;

@Component
public class DESUtil {
    private static final Logger log = Logger.getLogger(DESUtil.class);
    private static final String Encrypt_Alg = "AES";
    private static final String ALGORITHMSTR = "AES/ECB/PKCS5Padding";
    private static final String Char_Unicode = "UTF-8";
    private static final Integer Key_Size = 128;
    private static String strKey = "12345678";

    public String getKey() {
        try {
            KeyGenerator kg = KeyGenerator.getInstance(Encrypt_Alg);
            kg.init(Key_Size);
            SecretKey sk = kg.generateKey();
            byte[] b = sk.getEncoded();
            String s = DESUtil.parseByte2HexStr(b);
            return s;
        }
        catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
            System.out.println("\u6ca1\u6709\u6b64\u7b97\u6cd5\u3002");
            return null;
        }
    }

    public static String decryptContent(String encryptStr, String decryptKey) {
        try {
            byte[] raw = decryptKey.getBytes();
            SecretKeySpec skey = new SecretKeySpec(raw, Encrypt_Alg);
            Cipher cipher = Cipher.getInstance(ALGORITHMSTR);
            cipher.init(2, skey);
            byte[] encode_content = Base64.getDecoder().decode(encryptStr);
            byte[] byte_content = cipher.doFinal(encode_content);
            return new String(byte_content, Char_Unicode);
        }
        catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public static String encryptContent(String content, String key) {
        try {
            byte[] raw = key.getBytes();
            SecretKeySpec skey = new SecretKeySpec(raw, Encrypt_Alg);
            Cipher cipher = Cipher.getInstance(ALGORITHMSTR);
            cipher.init(1, skey);
            byte[] byte_content = content.getBytes("utf-8");
            byte[] encode_content = cipher.doFinal(byte_content);
            return Base64.getEncoder().encodeToString(encode_content);
        }
        catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public static String parseByte2HexStr(byte[] buf) {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < buf.length; ++i) {
            String hex = Integer.toHexString(buf[i] & 0xFF);
            if (hex.length() == 1) {
                hex = '0' + hex;
            }
            sb.append(hex.toUpperCase());
        }
        return sb.toString();
    }

    public static byte[] enCrypt(String content) throws Exception {
        String str = content;
        KeyGenerator keygen = KeyGenerator.getInstance(Encrypt_Alg);
        SecureRandom secureRandom = SecureRandom.getInstance("SHA1PRNG");
        secureRandom.setSeed(strKey.getBytes());
        keygen.init(128, secureRandom);
        SecretKey desKey = keygen.generateKey();
        Cipher c = Cipher.getInstance(Encrypt_Alg);
        c.init(1, desKey);
        byte[] cByte = c.doFinal(str.getBytes(Char_Unicode));
        return cByte;
    }

    public static String deCrypt(byte[] src) throws Exception {
        KeyGenerator keygen = KeyGenerator.getInstance(Encrypt_Alg);
        keygen.init(128, new SecureRandom(strKey.getBytes()));
        SecretKey desKey = keygen.generateKey();
        Cipher c = Cipher.getInstance(Encrypt_Alg);
        c.init(2, desKey);
        byte[] cByte = c.doFinal(src);
        return new String(cByte, Char_Unicode);
    }

    public static byte[] parseHexStr2Byte(String hexStr) {
        if (hexStr.length() < 1) {
            return null;
        }
        byte[] result = new byte[hexStr.length() / 2];
        for (int i = 0; i < hexStr.length() / 2; ++i) {
            int high = Integer.parseInt(hexStr.substring(i * 2, i * 2 + 1), 16);
            int low = Integer.parseInt(hexStr.substring(i * 2 + 1, i * 2 + 2), 16);
            result[i] = (byte)(high * 16 + low);
        }
        return result;
    }
}
