/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.apache.commons.codec.binary.Base64
 */
package com.fulongtech.uams.util;

import java.security.KeyFactory;
import java.security.KeyPair;
import java.security.KeyPairGenerator;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.HashMap;
import java.util.Map;
import javax.crypto.Cipher;
import org.apache.commons.codec.binary.Base64;

public class RSAUtil {
    private static Map<Integer, String> keyMap = new HashMap<Integer, String>();
    private static String privateKey = "MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAKGdzZgPRjMk/HpBOH7+ftfONHW6AXOuyY+iUmGkJ9YwqtIZtPuJf8qBybUAj5P3krgnO/cpLjZBN5xnXOInhGv0u7PD71kJKMxmCuYBRtjkh4FoDobNpKTxRYdnifk8v4ZQar7Z/4I4vv4oJLYDESlqoNlWsmsh+D9R6S12A3LPAgMBAAECgYBYHSQhenpomgq5UOPPgUuAt2EsqmvuOWaVVAecSpaZldEcWX2uAmpU62wgrhyZrMomcuWjvKUAWZBLSqN+fdMY9ALHGGOKeE3dC6B7r59ZLsDeqzGimHIfNcwj8AYG/0bthDgLIr5HUbVXG3zJN3knFg3lQ3EAdurPwYKDEJ+DKQJBAO+A7KhuuuJE4LgaW8ZeuGLcnhqx/fo7msuqaFSR3SqqrJz9A/khx66qV82Xd1eZt/gNx4kBIwVP1TLkNYdKYq0CQQCsv4YJczpipB3vmY0+49ngbxa3GjD8lmpyjxRax9sI8nJ+O/nkmmUWM70RHz2t4N/EbkMxs7CPChtZygGpQBbrAkEAxr7FpD++9WUS1bOJ1ONlPyS0TpLFn5E2syuFyCgC0BUxiFStp19QxLC2i/TVI+x7rWxB2H8JAK9uy/x0LwCbQQJAOjNMtWJdCV7BtzfEnqulfDfxLz9jwbXrpy0NbohnA1yrtlv+lUZvSEumJ16fKTXaLhP4D4a6tXZyQI4OgTdWgQJAWapBNb646Xa6LXN8BVwsnpdHE4NFxT+MdYymqVwrO2oO0fgPKQh83R2PMIB5tkCcEmV8Du0PfpW/vqv9xCuC7g==";
    private static String publickey = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQChnc2YD0YzJPx6QTh+/n7XzjR1ugFzrsmPolJhpCfWMKrSGbT7iX/Kgcm1AI+T95K4Jzv3KS42QTecZ1ziJ4Rr9Luzw+9ZCSjMZgrmAUbY5IeBaA6GzaSk8UWHZ4n5PL+GUGq+2f+COL7+KCS2AxEpaqDZVrJrIfg/UektdgNyzwIDAQAB";

    public static void genKeyPair() throws NoSuchAlgorithmException {
        KeyPairGenerator keyPairGen = KeyPairGenerator.getInstance("RSA");
        keyPairGen.initialize(1024, new SecureRandom());
        KeyPair keyPair = keyPairGen.generateKeyPair();
        RSAPrivateKey privateKey = (RSAPrivateKey)keyPair.getPrivate();
        RSAPublicKey publicKey = (RSAPublicKey)keyPair.getPublic();
        String publicKeyString = new String(Base64.encodeBase64((byte[])publicKey.getEncoded()));
        String privateKeyString = new String(Base64.encodeBase64((byte[])privateKey.getEncoded()));
        keyMap.put(0, publicKeyString);
        keyMap.put(1, privateKeyString);
    }

    public static String encrypt(String str, String publicKey) throws Exception {
        byte[] decoded = Base64.decodeBase64((String)publicKey);
        RSAPublicKey pubKey = (RSAPublicKey)KeyFactory.getInstance("RSA").generatePublic(new X509EncodedKeySpec(decoded));
        Cipher cipher = Cipher.getInstance("RSA");
        cipher.init(1, pubKey);
        String outStr = Base64.encodeBase64String((byte[])cipher.doFinal(str.getBytes("UTF-8")));
        return outStr;
    }

    public static String decrypt(String str, String privateKey) throws Exception {
        byte[] inputByte = Base64.decodeBase64((byte[])str.getBytes("UTF-8"));
        byte[] decoded = Base64.decodeBase64((String)privateKey);
        RSAPrivateKey priKey = (RSAPrivateKey)KeyFactory.getInstance("RSA").generatePrivate(new PKCS8EncodedKeySpec(decoded));
        Cipher cipher = Cipher.getInstance("RSA");
        cipher.init(2, priKey);
        String outStr = new String(cipher.doFinal(inputByte));
        return outStr;
    }

    public static String decrypt(String str) throws Exception {
        byte[] inputByte = Base64.decodeBase64((byte[])str.getBytes("UTF-8"));
        byte[] decoded = Base64.decodeBase64((String)privateKey);
        RSAPrivateKey priKey = (RSAPrivateKey)KeyFactory.getInstance("RSA").generatePrivate(new PKCS8EncodedKeySpec(decoded));
        Cipher cipher = Cipher.getInstance("RSA");
        cipher.init(2, priKey);
        String outStr = new String(cipher.doFinal(inputByte));
        return outStr;
    }

    public static void main(String[] args) throws Exception {
        String userName = "admin";
        String messageEn = RSAUtil.encrypt(userName, publickey);
        System.out.println(userName + "\t\u52a0\u5bc6\u540e\u7684\u7528\u6237\u540d\u4e3a:" + messageEn);
        String messageDe = RSAUtil.decrypt(messageEn, privateKey);
        System.out.println("\u8fd8\u539f\u540e\u7684\u7528\u6237\u540d\u4e3a:" + messageDe);
        String password = "admin";
        String passwordPublickey = RSAUtil.encrypt(password, publickey);
        System.out.println(password + "\t\u52a0\u5bc6\u540e\u7684\u5bc6\u7801\u4e3a:" + passwordPublickey);
        String passwordStr = RSAUtil.decrypt(passwordPublickey, privateKey);
        System.out.println("\u8fd8\u539f\u540e\u7684\u5bc6\u7801\u4e3a:" + passwordStr);
    }
}
