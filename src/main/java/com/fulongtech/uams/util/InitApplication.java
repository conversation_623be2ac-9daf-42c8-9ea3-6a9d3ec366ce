/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.springframework.beans.factory.annotation.Autowired
 *  org.springframework.boot.ApplicationArguments
 *  org.springframework.boot.ApplicationRunner
 *  org.springframework.data.redis.core.RedisOperations
 *  org.springframework.session.Session
 *  org.springframework.session.data.redis.RedisOperationsSessionRepository
 *  org.springframework.session.data.redis.RedisOperationsSessionRepository$RedisSession
 *  org.springframework.stereotype.Component
 */
package com.fulongtech.uams.util;

import com.fulongtech.uams.model.SysUser;
import com.fulongtech.uams.util.redis.RedisSessionForUser;
import java.util.Set;
import javax.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.data.redis.core.RedisOperations;
import org.springframework.session.Session;
import org.springframework.session.data.redis.RedisOperationsSessionRepository;
import org.springframework.stereotype.Component;

@Component
public class InitApplication
implements ApplicationRunner {
    @Autowired
    private RedisOperationsSessionRepository redisOperationsSessionRepository;
    @Resource(name="sessionRedisTemplate")
    private RedisOperations<Object, Object> sessionRedisOperations;

    public void run(ApplicationArguments applicationArguments) {
        Set keys = this.sessionRedisOperations.keys((Object)"spring:session:sessions*");
        keys.forEach(key -> {
            SysUser currentUser;
            RedisOperationsSessionRepository.RedisSession session;
            if (key.toString().contains("expires") && (session = this.redisOperationsSessionRepository.getSession(key.toString().substring(key.toString().lastIndexOf(":") + 1))) != null && (currentUser = (SysUser)session.getAttribute("currentUser")) != null) {
                RedisSessionForUser.putSession(currentUser.getUsername(), (Session)session);
            }
        });
    }
}
