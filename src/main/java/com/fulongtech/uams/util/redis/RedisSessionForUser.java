/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.springframework.session.Session
 */
package com.fulongtech.uams.util.redis;

import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import org.springframework.session.Session;

public final class RedisSessionForUser {
    private static Map<String, Session> userSession = new HashMap<String, Session>();

    private RedisSessionForUser() {
        throw new IllegalStateException("Utility class");
    }

    public static Session putSession(String userName, Session session) {
        userSession.put(userName, session);
        return session;
    }

    public static Session getSessionByUserName(String userName) {
        return userSession.get(userName);
    }

    public static void removeSessionByUserName(String userName) {
        userSession.remove(userName);
    }

    public static Collection<Session> getAllSessions() {
        return userSession.values();
    }
}
