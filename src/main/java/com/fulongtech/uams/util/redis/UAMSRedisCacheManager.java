/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.apache.shiro.cache.Cache
 *  org.apache.shiro.cache.CacheException
 *  org.apache.shiro.cache.CacheManager
 *  org.crazycake.shiro.IRedisManager
 *  org.crazycake.shiro.RedisCache
 *  org.crazycake.shiro.RedisManager
 *  org.crazycake.shiro.serializer.ObjectSerializer
 *  org.crazycake.shiro.serializer.RedisSerializer
 *  org.crazycake.shiro.serializer.StringSerializer
 */
package com.fulongtech.uams.util.redis;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import org.apache.shiro.cache.Cache;
import org.apache.shiro.cache.CacheException;
import org.apache.shiro.cache.CacheManager;
import org.crazycake.shiro.IRedisManager;
import org.crazycake.shiro.RedisCache;
import org.crazycake.shiro.RedisManager;
import org.crazycake.shiro.serializer.ObjectSerializer;
import org.crazycake.shiro.serializer.RedisSerializer;
import org.crazycake.shiro.serializer.StringSerializer;

public class UAMSRedisCacheManager
implements CacheManager {
    private final ConcurrentMap<String, Cache> caches = new ConcurrentHashMap<String, Cache>();
    private RedisManager redisManager;
    private String keyPrefix = "shiro_redis_cache:";
    private int expire;

    public int getExpire() {
        return this.expire;
    }

    public void setExpire(int expire) {
        this.expire = expire;
    }

    public RedisManager getRedisManager() {
        return this.redisManager;
    }

    public void setRedisManager(RedisManager redisManager) {
        this.redisManager = redisManager;
    }

    public String getKeyPrefix() {
        return this.keyPrefix;
    }

    public void setKeyPrefix(String keyPrefix) {
        this.keyPrefix = keyPrefix;
    }

    public <K, V> Cache<K, V> getCache(String name) throws CacheException {
        Cache c = (Cache)this.caches.get(name);
        if (c == null) {
            c = new RedisCache((IRedisManager)this.redisManager, (RedisSerializer)new StringSerializer(), (RedisSerializer)new ObjectSerializer(), this.getKeyPrefix(), this.getExpire(), this.keyPrefix + name);
            this.caches.put(name, c);
        }
        return c;
    }
}
