/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  cn.hutool.captcha.generator.RandomGenerator
 */
package com.fulongtech.uams.util;

import cn.hutool.captcha.generator.RandomGenerator;

public class VerificationCodeUtil {
    private static RandomGenerator randomGenerator = new RandomGenerator("0123456789", 6);

    public static String generateCode() {
        return randomGenerator.generate();
    }

    public static boolean verify(String code, String userInputCode) {
        return randomGenerator.verify(code, userInputCode);
    }
}
