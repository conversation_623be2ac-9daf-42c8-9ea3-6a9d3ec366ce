/*
 * Decompiled with CFR 0.152.
 */
package com.fulongtech.uams.util;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

public class PropertiesUtil {
    public static Map<String, String> readProperties(File file) {
        HashMap<String, String> map = new HashMap<String, String>(50);
        Properties properties = new Properties();
        try {
            FileInputStream in = new FileInputStream(file);
            try {
                properties.load(new InputStreamReader((InputStream)in, "UTF-8"));
                for (Object o : properties.keySet()) {
                    String key = String.valueOf(o);
                    map.put(key, properties.getProperty(key));
                }
            }
            catch (IOException e) {
                System.out.println("\u7ffb\u8bd1\u6587\u4ef6\u4e2d\u6709\u9519\u8bef");
            }
        }
        catch (Exception e) {
            System.out.println("\u7ffb\u8bd1\u6587\u4ef6\u4e0d\u5b58\u5728");
        }
        return map;
    }
}
