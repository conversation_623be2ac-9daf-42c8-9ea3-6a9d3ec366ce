/*
 * Decompiled with CFR 0.152.
 */
package com.fulongtech.uams.util;

import com.fulongtech.uams.util.LoginKeySingleton;
import java.util.concurrent.ConcurrentHashMap;

private class LoginKeySingleton.ClearThread
extends Thread {
    LoginKeySingleton.ClearThread() {
        this.setName("clear cache thread");
    }

    @Override
    public void run() {
        while (true) {
            try {
                while (true) {
                    Object[] keys;
                    long now = System.currentTimeMillis();
                    for (Object key : keys = ((ConcurrentHashMap.CollectionView)((Object)LoginKeySingleton.this.map.keySet())).toArray()) {
                        LoginKeySingleton.CacheProper entry = (LoginKeySingleton.CacheProper)LoginKeySingleton.this.map.get(key);
                        if (now - entry.getTime() < LoginKeySingleton.this.cacheTimeout || !LoginKeySingleton.this.map.containsKey(key)) continue;
                        LoginKeySingleton.this.map.remove(key);
                    }
                    Thread.sleep(LoginKeySingleton.this.cacheTimeout);
                }
            }
            catch (Exception e) {
                e.printStackTrace();
                continue;
            }
            break;
        }
    }
}
