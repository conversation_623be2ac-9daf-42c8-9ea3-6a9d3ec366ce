/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper
 *  com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper
 *  com.baomidou.mybatisplus.core.toolkit.ObjectUtils
 *  com.baomidou.mybatisplus.core.toolkit.support.SFunction
 *  com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper
 *  com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper
 */
package com.fulongtech.uams.util;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

public class MybatisplusParameterUtils {
    public static <T, F> void cutInParameter(LambdaQueryWrapper<T> wrapper, SFunction<T, ?> column, List<F> coll) throws Exception {
        List newList = MybatisplusParameterUtils.splitList(coll, 900);
        if (ObjectUtils.isEmpty(newList)) {
            throw new Exception("\u53c2\u6570\u9519\u8bef");
        }
        if (newList.size() == 1) {
            wrapper.in(column, (Collection)newList.get(0));
            return;
        }
        wrapper.and(i -> {
            i.in((Object)column, (Collection)newList.get(0));
            newList.remove(0);
            for (List objects : newList) {
                ((LambdaQueryWrapper)i.or()).in((Object)column, (Collection)objects);
            }
        });
    }

    public static <T, F> void cutNotInParameter(LambdaQueryWrapper<T> wrapper, SFunction<T, ?> column, List<F> coll) throws Exception {
        List newList = MybatisplusParameterUtils.splitList(coll, 900);
        if (ObjectUtils.isEmpty(newList)) {
            throw new Exception("\u53c2\u6570\u9519\u8bef");
        }
        if (newList.size() == 1) {
            wrapper.notIn(column, (Collection)newList.get(0));
            return;
        }
        wrapper.and(i -> {
            i.in((Object)column, (Collection)newList.get(0));
            newList.remove(0);
            for (List objects : newList) {
                ((LambdaQueryWrapper)i.or()).notIn((Object)column, (Collection)objects);
            }
        });
    }

    public static <T, F> void cutInParameter(LambdaQueryChainWrapper<T> wrapper, SFunction<T, ?> column, List<F> coll) throws Exception {
        List newList = MybatisplusParameterUtils.splitList(coll, 900);
        if (ObjectUtils.isEmpty(newList)) {
            throw new Exception("\u53c2\u6570\u9519\u8bef");
        }
        if (newList.size() == 1) {
            wrapper.in(column, (Collection)newList.get(0));
            return;
        }
        wrapper.and(i -> {
            i.in((Object)column, (Collection)newList.get(0));
            newList.remove(0);
            for (List objects : newList) {
                ((LambdaQueryWrapper)i.or()).in((Object)column, (Collection)objects);
            }
        });
    }

    public static <T, F> void cutNotInParameter(LambdaQueryChainWrapper<T> wrapper, SFunction<T, ?> column, List<F> coll) throws Exception {
        List newList = MybatisplusParameterUtils.splitList(coll, 900);
        if (ObjectUtils.isEmpty(newList)) {
            throw new Exception("\u53c2\u6570\u9519\u8bef");
        }
        if (newList.size() == 1) {
            wrapper.notIn(column, (Collection)newList.get(0));
            return;
        }
        wrapper.and(i -> {
            i.in((Object)column, (Collection)newList.get(0));
            newList.remove(0);
            for (List objects : newList) {
                ((LambdaQueryWrapper)i.or()).notIn((Object)column, (Collection)objects);
            }
        });
    }

    public static <T, F> void cutInParameter(LambdaUpdateWrapper<T> wrapper, SFunction<T, ?> column, List<F> coll) throws Exception {
        List newList = MybatisplusParameterUtils.splitList(coll, 900);
        if (ObjectUtils.isEmpty(newList)) {
            throw new Exception("\u53c2\u6570\u9519\u8bef");
        }
        if (newList.size() == 1) {
            wrapper.in(column, (Collection)newList.get(0));
            return;
        }
        wrapper.and(i -> {
            i.in((Object)column, (Collection)newList.get(0));
            newList.remove(0);
            for (List objects : newList) {
                ((LambdaUpdateWrapper)i.or()).in((Object)column, (Collection)objects);
            }
        });
    }

    public static <T, F> void cutNotInParameter(LambdaUpdateWrapper<T> wrapper, SFunction<T, ?> column, List<F> coll) throws Exception {
        List newList = MybatisplusParameterUtils.splitList(coll, 900);
        if (ObjectUtils.isEmpty(newList)) {
            throw new Exception("\u53c2\u6570\u9519\u8bef");
        }
        if (newList.size() == 1) {
            wrapper.notIn(column, (Collection)newList.get(0));
            return;
        }
        wrapper.and(i -> {
            i.in((Object)column, (Collection)newList.get(0));
            newList.remove(0);
            for (List objects : newList) {
                ((LambdaUpdateWrapper)i.or()).notIn((Object)column, (Collection)objects);
            }
        });
    }

    public static <T, F> void cutInParameter(LambdaUpdateChainWrapper<T> wrapper, SFunction<T, ?> column, List<F> coll) throws Exception {
        List newList = MybatisplusParameterUtils.splitList(coll, 900);
        if (ObjectUtils.isEmpty(newList)) {
            throw new Exception("\u53c2\u6570\u9519\u8bef");
        }
        if (newList.size() == 1) {
            wrapper.in(column, (Collection)newList.get(0));
            return;
        }
        wrapper.and(i -> {
            i.in((Object)column, (Collection)newList.get(0));
            newList.remove(0);
            for (List objects : newList) {
                ((LambdaUpdateWrapper)i.or()).in((Object)column, (Collection)objects);
            }
        });
    }

    public static <T, F> void cutNotInParameter(LambdaUpdateChainWrapper<T> wrapper, SFunction<T, ?> column, List<F> coll) throws Exception {
        List newList = MybatisplusParameterUtils.splitList(coll, 900);
        if (ObjectUtils.isEmpty(newList)) {
            throw new Exception("\u53c2\u6570\u9519\u8bef");
        }
        if (newList.size() == 1) {
            wrapper.notIn(column, (Collection)newList.get(0));
            return;
        }
        wrapper.and(i -> {
            i.in((Object)column, (Collection)newList.get(0));
            newList.remove(0);
            for (List objects : newList) {
                ((LambdaUpdateWrapper)i.or()).notIn((Object)column, (Collection)objects);
            }
        });
    }

    public static <F> List<List<F>> splitList(List<F> list, int groupSize) {
        int length = list.size();
        int num = (length + groupSize - 1) / groupSize;
        ArrayList<List<F>> newList = new ArrayList<List<F>>(num);
        for (int i = 0; i < num; ++i) {
            int fromIndex = i * groupSize;
            int toIndex = Math.min((i + 1) * groupSize, length);
            newList.add(list.subList(fromIndex, toIndex));
        }
        return newList;
    }
}
