/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  com.baomidou.mybatisplus.core.mapper.BaseMapper
 *  org.apache.ibatis.annotations.Options
 *  org.apache.ibatis.annotations.Param
 *  org.apache.ibatis.annotations.Select
 *  org.apache.ibatis.annotations.Update
 *  org.apache.ibatis.mapping.ResultSetType
 */
package com.fulongtech.uams.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fulongtech.uams.model.SysUser;
import java.util.List;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.apache.ibatis.mapping.ResultSetType;

public interface SysUserMapper
extends BaseMapper<SysUser> {
    @Update(value={"update SYS_USER set password = #{password} where ID =#{userId}"})
    public void changePassword(@Param(value="userId") String var1, @Param(value="password") String var2);

    @Select(value={"SELECT * FROM SYS_USER"})
    @Options(resultSetType=ResultSetType.FORWARD_ONLY, fetchSize=5000)
    public List<SysUser> getAll();
}
