/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  com.baomidou.mybatisplus.core.mapper.BaseMapper
 *  org.apache.ibatis.annotations.Options
 *  org.apache.ibatis.annotations.Select
 *  org.apache.ibatis.mapping.ResultSetType
 */
package com.fulongtech.uams.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fulongtech.uams.model.UserGroupRelation;
import java.util.List;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.mapping.ResultSetType;

public interface RelationMapper
extends BaseMapper<UserGroupRelation> {
    @Select(value={"SELECT * FROM RELATION"})
    @Options(resultSetType=ResultSetType.FORWARD_ONLY, fetchSize=5000)
    public List<UserGroupRelation> getAll();
}
