/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  com.fulongtech.log4j.Log4jFilter
 *  javax.servlet.Filter
 *  org.apache.shiro.authc.credential.CredentialsMatcher
 *  org.apache.shiro.cache.CacheManager
 *  org.apache.shiro.codec.Base64
 *  org.apache.shiro.mgt.RememberMeManager
 *  org.apache.shiro.mgt.SecurityManager
 *  org.apache.shiro.session.mgt.SessionManager
 *  org.apache.shiro.session.mgt.eis.AbstractSessionDAO
 *  org.apache.shiro.session.mgt.eis.JavaUuidSessionIdGenerator
 *  org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor
 *  org.apache.shiro.spring.web.ShiroFilterFactoryBean
 *  org.apache.shiro.web.filter.authz.SslFilter
 *  org.apache.shiro.web.filter.mgt.DefaultFilterChainManager
 *  org.apache.shiro.web.mgt.CookieRememberMeManager
 *  org.apache.shiro.web.mgt.DefaultWebSecurityManager
 *  org.apache.shiro.web.servlet.Cookie
 *  org.apache.shiro.web.servlet.SimpleCookie
 *  org.apache.shiro.web.session.mgt.ServletContainerSessionManager
 *  org.crazycake.shiro.IRedisManager
 *  org.crazycake.shiro.RedisCache
 *  org.crazycake.shiro.RedisManager
 *  org.crazycake.shiro.RedisSessionDAO
 *  org.crazycake.shiro.serializer.ObjectSerializer
 *  org.crazycake.shiro.serializer.RedisSerializer
 *  org.crazycake.shiro.serializer.StringSerializer
 *  org.springframework.aop.framework.autoproxy.DefaultAdvisorAutoProxyCreator
 *  org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean
 *  org.springframework.boot.web.servlet.FilterRegistrationBean
 *  org.springframework.context.annotation.Bean
 *  org.springframework.context.annotation.Configuration
 *  org.springframework.util.StringUtils
 *  org.springframework.web.filter.DelegatingFilterProxy
 */
package com.fulongtech.uams.config;

import com.fulongtech.log4j.Log4jFilter;
import com.fulongtech.uams.filter.UserFilter;
import com.fulongtech.uams.properties.ServerProper;
import com.fulongtech.uams.properties.ShiroProper;
import com.fulongtech.uams.properties.SpringSessionProper;
import com.fulongtech.uams.shiro.credentials.RetryLimitHashedCredentialsMatcher;
import com.fulongtech.uams.shiro.realm.UserRealm;
import com.fulongtech.uams.shiro.spring.CustomDefaultFilterChainManager;
import com.fulongtech.uams.util.redis.UAMSRedisCacheManager;
import java.util.ArrayList;
import java.util.HashMap;
import javax.annotation.Resource;
import javax.servlet.Filter;
import org.apache.shiro.authc.credential.CredentialsMatcher;
import org.apache.shiro.cache.CacheManager;
import org.apache.shiro.codec.Base64;
import org.apache.shiro.mgt.RememberMeManager;
import org.apache.shiro.mgt.SecurityManager;
import org.apache.shiro.session.mgt.SessionManager;
import org.apache.shiro.session.mgt.eis.AbstractSessionDAO;
import org.apache.shiro.session.mgt.eis.JavaUuidSessionIdGenerator;
import org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor;
import org.apache.shiro.spring.web.ShiroFilterFactoryBean;
import org.apache.shiro.web.filter.authz.SslFilter;
import org.apache.shiro.web.filter.mgt.DefaultFilterChainManager;
import org.apache.shiro.web.mgt.CookieRememberMeManager;
import org.apache.shiro.web.mgt.DefaultWebSecurityManager;
import org.apache.shiro.web.servlet.Cookie;
import org.apache.shiro.web.servlet.SimpleCookie;
import org.apache.shiro.web.session.mgt.ServletContainerSessionManager;
import org.crazycake.shiro.IRedisManager;
import org.crazycake.shiro.RedisCache;
import org.crazycake.shiro.RedisManager;
import org.crazycake.shiro.RedisSessionDAO;
import org.crazycake.shiro.serializer.ObjectSerializer;
import org.crazycake.shiro.serializer.RedisSerializer;
import org.crazycake.shiro.serializer.StringSerializer;
import org.springframework.aop.framework.autoproxy.DefaultAdvisorAutoProxyCreator;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.DelegatingFilterProxy;

@Configuration
public class ShiroConfig {
    private ShiroProper shiroProper = ShiroProper.getInstance();
    @Resource
    private SpringSessionProper redisProper;
    @Resource
    private ServerProper serverProper;

    @Bean
    @ConditionalOnMissingBean
    public DefaultAdvisorAutoProxyCreator defaultAdvisorAutoProxyCreator() {
        DefaultAdvisorAutoProxyCreator daap = new DefaultAdvisorAutoProxyCreator();
        daap.setProxyTargetClass(true);
        return daap;
    }

    @Bean
    public AuthorizationAttributeSourceAdvisor authorizationAttributeSourceAdvisor() {
        AuthorizationAttributeSourceAdvisor advisor = new AuthorizationAttributeSourceAdvisor();
        advisor.setSecurityManager(this.securityManager());
        return advisor;
    }

    @Bean
    public RedisCache retryCache() {
        RedisCache redisCache = new RedisCache((IRedisManager)this.redisManager(), (RedisSerializer)new StringSerializer(), (RedisSerializer)new ObjectSerializer(), "shiro:cache", 900, "passwordRetryCache");
        return redisCache;
    }

    @Bean
    public CacheManager shiroCacheManager() {
        UAMSRedisCacheManager cacheManager = new UAMSRedisCacheManager();
        cacheManager.setRedisManager(this.redisManager());
        return cacheManager;
    }

    @Bean
    public RetryLimitHashedCredentialsMatcher credentialsMatcher() {
        RetryLimitHashedCredentialsMatcher retryLimitHashedCredentialsMatcher = new RetryLimitHashedCredentialsMatcher(this.retryCache(), 900);
        retryLimitHashedCredentialsMatcher.setStoredCredentialsHexEncoded(false);
        return retryLimitHashedCredentialsMatcher;
    }

    @Bean
    public UserRealm userRealm() {
        UserRealm userRealm = new UserRealm();
        userRealm.setCredentialsMatcher((CredentialsMatcher)this.credentialsMatcher());
        userRealm.setCachingEnabled(false);
        return userRealm;
    }

    @Bean
    public JavaUuidSessionIdGenerator sessionIdGenerator() {
        return new JavaUuidSessionIdGenerator();
    }

    @Bean
    public SimpleCookie sessionIdCookie() {
        SimpleCookie simpleCookie = new SimpleCookie("sid");
        simpleCookie.setHttpOnly(true);
        simpleCookie.setMaxAge(this.shiroProper.getSessionIdCookieMaxAge());
        return simpleCookie;
    }

    @Bean
    public SimpleCookie rememberMeCookie() {
        SimpleCookie simpleCookie = new SimpleCookie("rememberMe");
        simpleCookie.setHttpOnly(true);
        simpleCookie.setMaxAge(this.shiroProper.getRememberMeCookieMaxAge());
        return simpleCookie;
    }

    @Bean
    public CookieRememberMeManager rememberMeManager() {
        CookieRememberMeManager cookieRememberMeManager = new CookieRememberMeManager();
        cookieRememberMeManager.setCipherKey(Base64.decode((String)"RnVsb25ndGVjaFVBTVNERA=="));
        cookieRememberMeManager.setCookie((Cookie)this.rememberMeCookie());
        return cookieRememberMeManager;
    }

    @Bean
    public AbstractSessionDAO sessionDAO() {
        RedisSessionDAO sessionDAO = new RedisSessionDAO();
        sessionDAO.setRedisManager((IRedisManager)this.redisManager());
        return sessionDAO;
    }

    @Bean
    public RedisManager redisManager() {
        RedisManager redisManager = new RedisManager();
        redisManager.setHost(this.redisProper.getHost() + ":" + this.redisProper.getPort());
        if (!StringUtils.isEmpty((Object)this.redisProper.getPassword())) {
            redisManager.setPassword(this.redisProper.getPassword());
        }
        return redisManager;
    }

    @Bean
    public ServletContainerSessionManager sessionManager() {
        return new ServletContainerSessionManager();
    }

    @Bean
    public SecurityManager securityManager() {
        DefaultWebSecurityManager securityManager = new DefaultWebSecurityManager();
        ArrayList<UserRealm> realms = new ArrayList<UserRealm>();
        if (this.userRealm() != null) {
            realms.add(this.userRealm());
        }
        securityManager.setRealms(realms);
        securityManager.setSessionManager((SessionManager)this.sessionManager());
        securityManager.setRememberMeManager((RememberMeManager)this.rememberMeManager());
        securityManager.setCacheManager(this.shiroCacheManager());
        return securityManager;
    }

    @Bean(name={"oneWaySslFilter"})
    public SslFilter oneWaySslFilter() {
        SslFilter oneWaySslFilter = new SslFilter();
        oneWaySslFilter.setPort(this.serverProper.getBasicConnector().getPort());
        return oneWaySslFilter;
    }

    @Bean
    public Log4jFilter log4jFilter() {
        return new Log4jFilter();
    }

    @Bean
    public FilterRegistrationBean delegatingFilterProxy() {
        FilterRegistrationBean filterRegistrationBean = new FilterRegistrationBean();
        DelegatingFilterProxy proxy = new DelegatingFilterProxy();
        proxy.setTargetFilterLifecycle(true);
        proxy.setTargetBeanName("shiroFilter");
        filterRegistrationBean.setFilter((Filter)proxy);
        return filterRegistrationBean;
    }

    @Bean(value={"shiroFilter"})
    public ShiroFilterFactoryBean shiroFilter(SecurityManager securityManager) {
        ShiroFilterFactoryBean shiroFilterFactoryBean = new ShiroFilterFactoryBean();
        shiroFilterFactoryBean.setLoginUrl(this.shiroProper.getLoginUrl());
        shiroFilterFactoryBean.setSuccessUrl(this.shiroProper.getSuccessUrl());
        shiroFilterFactoryBean.setUnauthorizedUrl(this.shiroProper.getUnauthorizedUrl());
        HashMap<String, Object> customFilters = new HashMap<String, Object>(4);
        customFilters.put("owssl", this.oneWaySslFilter());
        customFilters.put("log", this.log4jFilter());
        customFilters.put("userInfo", (Object)this.userFilter());
        shiroFilterFactoryBean.setFilters(customFilters);
        shiroFilterFactoryBean.setSecurityManager(securityManager);
        shiroFilterFactoryBean.setFilterChainDefinitions(this.shiroProper.getFilter());
        return shiroFilterFactoryBean;
    }

    @Bean
    public DefaultFilterChainManager filterChainManager() {
        return new CustomDefaultFilterChainManager();
    }

    @Bean
    public UserFilter userFilter() {
        return new UserFilter();
    }
}
