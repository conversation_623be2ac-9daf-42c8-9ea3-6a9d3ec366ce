/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  com.baomidou.mybatisplus.core.parser.ISqlParser
 *  com.baomidou.mybatisplus.extension.plugins.PaginationInterceptor
 *  com.baomidou.mybatisplus.extension.plugins.pagination.optimize.JsqlParserCountOptimize
 *  org.mybatis.spring.annotation.MapperScan
 *  org.springframework.context.annotation.Bean
 *  org.springframework.context.annotation.Configuration
 */
package com.fulongtech.uams.config;

import com.baomidou.mybatisplus.core.parser.ISqlParser;
import com.baomidou.mybatisplus.extension.plugins.PaginationInterceptor;
import com.baomidou.mybatisplus.extension.plugins.pagination.optimize.JsqlParserCountOptimize;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@MapperScan(value={"com.fulongtech.uams.mapper"})
public class MybatisConfig {
    @Bean
    public PaginationInterceptor paginationInterceptor() {
        PaginationInterceptor paginationInterceptor = new PaginationInterceptor();
        paginationInterceptor.setOverflow(true);
        paginationInterceptor.setLimit(-1L);
        paginationInterceptor.setCountSqlParser((ISqlParser)new JsqlParserCountOptimize(true));
        return paginationInterceptor;
    }
}
