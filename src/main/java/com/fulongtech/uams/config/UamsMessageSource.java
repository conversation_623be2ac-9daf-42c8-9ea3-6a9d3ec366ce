/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.apache.commons.collections.MapUtils
 *  org.springframework.context.ResourceLoaderAware
 *  org.springframework.context.support.AbstractMessageSource
 *  org.springframework.core.io.DefaultResourceLoader
 *  org.springframework.core.io.ResourceLoader
 *  org.springframework.stereotype.Component
 */
package com.fulongtech.uams.config;

import com.fulongtech.uams.util.FileUtils;
import com.fulongtech.uams.util.PropertiesUtil;
import java.io.File;
import java.text.MessageFormat;
import java.util.Locale;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import javax.annotation.PostConstruct;
import org.apache.commons.collections.MapUtils;
import org.springframework.context.ResourceLoaderAware;
import org.springframework.context.support.AbstractMessageSource;
import org.springframework.core.io.DefaultResourceLoader;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Component;

@Component(value="messageSource")
public class UamsMessageSource
extends AbstractMessageSource
implements ResourceLoaderAware {
    ResourceLoader resourceLoader;
    private static final Map<String, Map<String, String>> LOCAL_CACHE = new ConcurrentHashMap<String, Map<String, String>>(256);

    @PostConstruct
    public void init() {
        this.reload();
    }

    public void reload() {
        LOCAL_CACHE.clear();
        LOCAL_CACHE.putAll(this.loadAllMessageResources());
    }

    public Map<String, Map<String, String>> loadAllMessageResources() {
        File[] messages;
        File folder = new File("./config/translation");
        for (File message : messages = FileUtils.scanFile(folder, "message")) {
            Map<String, String> lang = PropertiesUtil.readProperties(message);
            String name = message.getName();
            String langName = name.substring(name.indexOf("_") + 1, name.lastIndexOf("_"));
            LOCAL_CACHE.put(langName, lang);
        }
        return MapUtils.EMPTY_MAP;
    }

    public String getSourceFromCache(String code, Locale locale) {
        String language = locale.getLanguage();
        Map<String, String> props = LOCAL_CACHE.get(language);
        if (null != props && props.containsKey(code)) {
            return props.get(code);
        }
        try {
            if (null != this.getParentMessageSource()) {
                return this.getParentMessageSource().getMessage(code, null, locale);
            }
        }
        catch (Exception ex) {
            this.logger.error((Object)ex.getMessage(), (Throwable)ex);
        }
        return code;
    }

    public void setResourceLoader(ResourceLoader resourceLoader) {
        this.resourceLoader = resourceLoader == null ? new DefaultResourceLoader() : resourceLoader;
    }

    protected MessageFormat resolveCode(String code, Locale locale) {
        String msg = this.getSourceFromCache(code, locale);
        MessageFormat messageFormat = new MessageFormat(msg, locale);
        return messageFormat;
    }

    protected String resolveCodeWithoutArguments(String code, Locale locale) {
        return this.getSourceFromCache(code, locale);
    }
}
