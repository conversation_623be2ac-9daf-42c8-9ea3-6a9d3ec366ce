/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  com.google.code.kaptcha.impl.DefaultKaptcha
 *  com.google.code.kaptcha.util.Config
 *  org.springframework.boot.context.embedded.EmbeddedServletContainerCustomizer
 *  org.springframework.boot.context.embedded.EmbeddedServletContainerFactory
 *  org.springframework.boot.context.embedded.tomcat.TomcatEmbeddedServletContainerFactory
 *  org.springframework.boot.web.servlet.ErrorPage
 *  org.springframework.boot.web.servlet.ServletContextInitializer
 *  org.springframework.context.annotation.Bean
 *  org.springframework.context.annotation.Configuration
 *  org.springframework.http.HttpStatus
 */
package com.fulongtech.uams.config;

import com.fulongtech.uams.properties.ServerProper;
import com.google.code.kaptcha.impl.DefaultKaptcha;
import com.google.code.kaptcha.util.Config;
import java.util.Properties;
import javax.annotation.Resource;
import org.springframework.boot.context.embedded.EmbeddedServletContainerCustomizer;
import org.springframework.boot.context.embedded.EmbeddedServletContainerFactory;
import org.springframework.boot.context.embedded.tomcat.TomcatEmbeddedServletContainerFactory;
import org.springframework.boot.web.servlet.ErrorPage;
import org.springframework.boot.web.servlet.ServletContextInitializer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpStatus;

@Configuration
public class WebConfig {
    @Resource
    private ServerProper serverProper;

    @Bean
    public EmbeddedServletContainerFactory servletContainer() {
        TomcatEmbeddedServletContainerFactory tomcat = new TomcatEmbeddedServletContainerFactory();
        tomcat.setContextPath(this.serverProper.getContextPath());
        tomcat.setPort(this.serverProper.getBasicConnector().getPort());
        return tomcat;
    }

    @Bean
    public EmbeddedServletContainerCustomizer containerCustomizer() {
        return container -> {
            ErrorPage error404Page = new ErrorPage(HttpStatus.NOT_FOUND, "/static/errorpage/404.html");
            ErrorPage error500Page = new ErrorPage(HttpStatus.INTERNAL_SERVER_ERROR, "/static/errorpage/500.html");
            container.addErrorPages(new ErrorPage[]{error404Page, error500Page});
        };
    }

    @Bean
    public ServletContextInitializer initializer() {
        return servletContext -> {};
    }

    @Bean
    public DefaultKaptcha getDefaultKaptcha() {
        DefaultKaptcha defaultKaptcha = new DefaultKaptcha();
        Properties properties = new Properties();
        properties.setProperty("kaptcha.border", "yes");
        properties.setProperty("kaptcha.border.color", "105,179,90");
        properties.setProperty("kaptcha.textproducer.font.color", "blue");
        properties.setProperty("kaptcha.noise.color", "black");
        properties.setProperty("kaptcha.image.width", "90");
        properties.setProperty("kaptcha.image.height", "37");
        properties.setProperty("kaptcha.textproducer.font.size", "30");
        properties.setProperty("kaptcha.session.key", "code");
        properties.setProperty("kaptcha.textproducer.char.length", "4");
        properties.setProperty("kaptcha.textproducer.font.names", "\u5f69\u4e91,\u5b8b\u4f53,\u6977\u4f53,\u5fae\u8f6f\u96c5\u9ed1");
        properties.setProperty("kaptcha.obscurificator.impl", "com.google.code.kaptcha.impl.ShadowGimpy");
        Config config = new Config(properties);
        defaultKaptcha.setConfig(config);
        return defaultKaptcha;
    }
}
