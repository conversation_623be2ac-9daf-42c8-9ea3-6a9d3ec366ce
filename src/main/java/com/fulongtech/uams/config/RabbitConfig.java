/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.springframework.amqp.core.Binding
 *  org.springframework.amqp.core.BindingBuilder
 *  org.springframework.amqp.core.DirectExchange
 *  org.springframework.amqp.core.Queue
 *  org.springframework.context.annotation.Bean
 *  org.springframework.context.annotation.Configuration
 *  org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor
 */
package com.fulongtech.uams.config;

import java.util.concurrent.Executor;
import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.DirectExchange;
import org.springframework.amqp.core.Queue;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

@Configuration
public class RabbitConfig {
    @Bean
    DirectExchange directExchange() {
        return new DirectExchange("UAMS_Exchange", true, false);
    }

    @Bean
    public Queue TestTopicQueue() {
        return new Queue("UAMS");
    }

    @Bean
    Binding bindingTopic() {
        return BindingBuilder.bind((Queue)this.TestTopicQueue()).to(this.directExchange()).with("UAMS");
    }

    @Bean(value={"rabbitMqExecutor"})
    public Executor rabbitMqExecutor() {
        ThreadPoolTaskExecutor taskExecutor = new ThreadPoolTaskExecutor();
        taskExecutor.setCorePoolSize(10);
        taskExecutor.setMaxPoolSize(50);
        taskExecutor.setQueueCapacity(200);
        taskExecutor.setKeepAliveSeconds(60);
        taskExecutor.setThreadNamePrefix("rabbitMqExecutor--");
        taskExecutor.setWaitForTasksToCompleteOnShutdown(true);
        taskExecutor.setAwaitTerminationSeconds(60);
        return taskExecutor;
    }
}
