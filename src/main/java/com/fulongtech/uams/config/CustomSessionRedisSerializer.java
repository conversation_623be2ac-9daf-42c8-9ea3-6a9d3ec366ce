/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.slf4j.Logger
 *  org.slf4j.LoggerFactory
 *  org.springframework.data.redis.serializer.JdkSerializationRedisSerializer
 */
package com.fulongtech.uams.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.serializer.JdkSerializationRedisSerializer;

public class CustomSessionRedisSerializer
extends JdkSerializationRedisSerializer {
    private static final Logger LOG = LoggerFactory.getLogger(CustomSessionRedisSerializer.class);

    public Object deserialize(byte[] bytes) {
        Object deserializeObj = null;
        try {
            deserializeObj = super.deserialize(bytes);
        }
        catch (Exception e) {
            LOG.warn("deserialize session Object error!", (Throwable)e);
        }
        return deserializeObj;
    }
}
