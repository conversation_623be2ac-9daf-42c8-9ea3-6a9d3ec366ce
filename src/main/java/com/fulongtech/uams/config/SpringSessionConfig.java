/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.servlet.ServletContext
 *  org.springframework.beans.factory.annotation.Qualifier
 *  org.springframework.context.ApplicationEventPublisher
 *  org.springframework.context.annotation.Bean
 *  org.springframework.context.annotation.Configuration
 *  org.springframework.context.annotation.Primary
 *  org.springframework.data.redis.connection.RedisConnectionFactory
 *  org.springframework.data.redis.core.RedisOperations
 *  org.springframework.data.redis.core.RedisTemplate
 *  org.springframework.data.redis.serializer.RedisSerializer
 *  org.springframework.data.redis.serializer.StringRedisSerializer
 *  org.springframework.session.ExpiringSession
 *  org.springframework.session.SessionRepository
 *  org.springframework.session.data.redis.RedisFlushMode
 *  org.springframework.session.data.redis.RedisOperationsSessionRepository
 *  org.springframework.session.data.redis.config.annotation.web.http.EnableRedisHttpSession
 *  org.springframework.session.web.http.CookieHttpSessionStrategy
 *  org.springframework.session.web.http.CookieSerializer
 *  org.springframework.session.web.http.DefaultCookieSerializer
 *  org.springframework.session.web.http.MultiHttpSessionStrategy
 *  org.springframework.session.web.http.SessionRepositoryFilter
 */
package com.fulongtech.uams.config;

import com.fulongtech.uams.config.CustomSessionRedisSerializer;
import com.fulongtech.uams.config.UAMSCookieSerializer;
import com.fulongtech.uams.properties.SpringSessionProper;
import javax.annotation.Resource;
import javax.servlet.ServletContext;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import org.springframework.session.ExpiringSession;
import org.springframework.session.SessionRepository;
import org.springframework.session.data.redis.RedisFlushMode;
import org.springframework.session.data.redis.RedisOperationsSessionRepository;
import org.springframework.session.data.redis.config.annotation.web.http.EnableRedisHttpSession;
import org.springframework.session.web.http.CookieHttpSessionStrategy;
import org.springframework.session.web.http.CookieSerializer;
import org.springframework.session.web.http.DefaultCookieSerializer;
import org.springframework.session.web.http.MultiHttpSessionStrategy;
import org.springframework.session.web.http.SessionRepositoryFilter;

@EnableRedisHttpSession
@Configuration
public class SpringSessionConfig {
    @Resource
    private SpringSessionProper springSessionProper;

    @Primary
    @Bean
    public RedisOperationsSessionRepository sessionRepository(@Qualifier(value="sessionRedisTemplate") RedisOperations<Object, Object> sessionRedisTemplate, ApplicationEventPublisher applicationEventPublisher) {
        RedisOperationsSessionRepository sessionRepository = new RedisOperationsSessionRepository(sessionRedisTemplate);
        sessionRepository.setApplicationEventPublisher(applicationEventPublisher);
        sessionRepository.setDefaultMaxInactiveInterval(Integer.parseInt(this.springSessionProper.getMaxInactiveIntervalInSeconds()));
        sessionRepository.setRedisFlushMode(RedisFlushMode.IMMEDIATE);
        return sessionRepository;
    }

    @Bean
    public RedisTemplate<Object, Object> sessionRedisTemplate(RedisConnectionFactory factory) {
        RedisTemplate redisTemplate = new RedisTemplate();
        redisTemplate.setConnectionFactory(factory);
        CustomSessionRedisSerializer customSessionRedisSerializer = new CustomSessionRedisSerializer();
        redisTemplate.setValueSerializer((RedisSerializer)customSessionRedisSerializer);
        redisTemplate.setHashValueSerializer((RedisSerializer)customSessionRedisSerializer);
        redisTemplate.setKeySerializer((RedisSerializer)new StringRedisSerializer());
        redisTemplate.setHashKeySerializer((RedisSerializer)new StringRedisSerializer());
        redisTemplate.setDefaultSerializer((RedisSerializer)customSessionRedisSerializer);
        redisTemplate.afterPropertiesSet();
        return redisTemplate;
    }

    @Bean
    public <S extends ExpiringSession> SessionRepositoryFilter<? extends ExpiringSession> springSessionRepositoryFilter(SessionRepository<S> sessionRepository, ServletContext servletContext) {
        SessionRepositoryFilter sessionRepositoryFilter = new SessionRepositoryFilter(sessionRepository);
        sessionRepositoryFilter.setServletContext(servletContext);
        CookieHttpSessionStrategy httpSessionStrategy = new CookieHttpSessionStrategy();
        httpSessionStrategy.setCookieSerializer((CookieSerializer)this.defaultCookieSerializer());
        sessionRepositoryFilter.setHttpSessionStrategy((MultiHttpSessionStrategy)httpSessionStrategy);
        return sessionRepositoryFilter;
    }

    @Bean
    public DefaultCookieSerializer defaultCookieSerializer() {
        UAMSCookieSerializer cookieSerializer = new UAMSCookieSerializer();
        cookieSerializer.setCookiePath("/");
        cookieSerializer.setCookieName("FULONGTECH_SESSION");
        cookieSerializer.setUseBase64Encoding(false);
        return cookieSerializer;
    }
}
