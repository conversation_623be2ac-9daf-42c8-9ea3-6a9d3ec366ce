/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.apache.commons.dbcp2.BasicDataSource
 *  org.springframework.beans.factory.annotation.Autowired
 *  org.springframework.beans.factory.annotation.Value
 *  org.springframework.context.annotation.Bean
 *  org.springframework.context.annotation.Configuration
 */
package com.fulongtech.uams.config;

import com.fulongtech.uams.properties.DataSourceProper;
import org.apache.commons.dbcp2.BasicDataSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class DataSourceConfig {
    @Autowired
    private DataSourceProper dataSourceProper;
    @Value(value="${uams.datasource.type}")
    private String dialect;

    @Bean
    public BasicDataSource dataSource() {
        BasicDataSource ds = new BasicDataSource();
        try {
            String userName = this.dataSourceProper.getUsername();
            String password = this.dataSourceProper.getPassword();
            String url = null;
            String driverClassName = null;
            String type = this.dataSourceProper.getType();
            if (this.dialect.equals("sqlserver")) {
                url = this.dataSourceProper.getSqlserverUrl();
                driverClassName = this.dataSourceProper.getSqlserverDriverClassName();
                ds.setValidationQuery(this.dataSourceProper.getSqlServerValidationQuery());
            } else if (this.dialect.equals("oracle")) {
                url = this.dataSourceProper.getOracleUrl();
                driverClassName = this.dataSourceProper.getOracleDriverClassName();
                ds.setValidationQuery(this.dataSourceProper.getOracleValidationQuery());
            } else {
                url = this.dataSourceProper.getMysqlUrl();
                driverClassName = this.dataSourceProper.getMysqlDriverClassName();
                ds.setValidationQuery(this.dataSourceProper.getMysqlValidationQuery());
            }
            ds.setUrl(url);
            ds.setUsername(userName);
            ds.setPassword(password);
            ds.setDriverClassName(driverClassName);
            ds.setTimeBetweenEvictionRunsMillis(this.dataSourceProper.getTimeBetweenEvictionRunsMillis().longValue());
            ds.setMinEvictableIdleTimeMillis(this.dataSourceProper.getMinEvictableIdleTimeMillis().longValue());
            ds.setTestOnBorrow(this.dataSourceProper.getTestOnBorrow().booleanValue());
            ds.setTestWhileIdle(this.dataSourceProper.getTestWhileIdle().booleanValue());
            ds.setNumTestsPerEvictionRun(this.dataSourceProper.getNumTestsPerEvictionRun().intValue());
        }
        catch (Exception e) {
            e.printStackTrace();
        }
        return ds;
    }
}
