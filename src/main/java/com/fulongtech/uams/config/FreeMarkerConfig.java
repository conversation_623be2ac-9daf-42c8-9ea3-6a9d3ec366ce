/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  com.jagregory.shiro.freemarker.ShiroTags
 *  freemarker.template.Configuration
 *  freemarker.template.TemplateModel
 *  org.springframework.beans.factory.InitializingBean
 *  org.springframework.stereotype.Component
 *  org.springframework.web.servlet.resource.ResourceUrlProvider
 */
package com.fulongtech.uams.config;

import com.jagregory.shiro.freemarker.ShiroTags;
import freemarker.template.Configuration;
import freemarker.template.TemplateModel;
import javax.annotation.Resource;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.resource.ResourceUrlProvider;

@Component
public class FreeMarkerConfig
implements InitializingBean {
    @Resource
    private Configuration configuration;
    @Resource
    ResourceUrlProvider resourceUrlProvider;

    public void afterPropertiesSet() throws Exception {
        this.configuration.setSharedVariable("shiro", (<PERSON>mplateModel)new ShiroTags());
        this.configuration.addAutoImport("spring", "spring.ftl");
    }
}
