/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  com.fulongtech.uams.token.filter.UAMSTokenFilter
 *  javax.servlet.Filter
 *  org.springframework.boot.web.servlet.FilterRegistrationBean
 *  org.springframework.context.annotation.Bean
 *  org.springframework.context.annotation.Configuration
 *  org.springframework.stereotype.Component
 */
package com.fulongtech.uams.config;

import com.fulongtech.uams.token.filter.UAMSTokenFilter;
import javax.servlet.Filter;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

@Configuration
@Component
public class TokenConfig {
    @Bean
    public FilterRegistrationBean testFilterRegistration() {
        FilterRegistrationBean registration = new FilterRegistrationBean();
        registration.setFilter((Filter)new UAMSTokenFilter());
        registration.addUrlPatterns(new String[]{"/*"});
        registration.setName("UAMSTokenFilter");
        registration.setOrder(2);
        return registration;
    }
}
