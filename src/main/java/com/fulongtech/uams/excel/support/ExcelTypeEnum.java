/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.springframework.web.multipart.MultipartFile
 */
package com.fulongtech.uams.excel.support;

import com.fulongtech.uams.excel.metadata.ReadBookwork;
import com.fulongtech.uams.exception.ErrorCode;
import com.fulongtech.uams.exception.UamsException;
import java.io.InputStream;
import org.springframework.web.multipart.MultipartFile;

public enum ExcelTypeEnum {
    XLS(".xls"),
    XLSX(".xlsx");

    private String value;

    private ExcelTypeEnum(String value) {
        this.setValue(value);
    }

    public static ExcelTypeEnum valueOf(ReadBookwork readBookwork) {
        ExcelTypeEnum excelType = readBookwork.getExcelType();
        if (excelType != null) {
            return excelType;
        }
        MultipartFile file = readBookwork.getFile();
        InputStream inputStream = readBookwork.getInputStream();
        if (file == null && inputStream == null) {
            throw new UamsException(ErrorCode.RESOURCE_NOT_FOUND, "\u6587\u4ef6\u4e0d\u5b58\u5728");
        }
        try {
            if (file != null) {
                if (file.isEmpty()) {
                    throw new UamsException(ErrorCode.RESOURCE_NOT_FOUND, "\u6587\u4ef6\u4e3a\u7a7a");
                }
                String fileName = file.getOriginalFilename().toLowerCase();
                if (fileName.endsWith(XLSX.getValue())) {
                    return XLSX;
                }
                if (fileName.endsWith(XLS.getValue())) {
                    return XLS;
                }
            }
            throw new UamsException(ErrorCode.SYSTEM_ERROR, "\u73b0\u5728\u6682\u4e0d\u652f\u6301\u4f7f\u7528InputStream\u5f62\u5f0f");
        }
        catch (Exception e) {
            throw new UamsException(ErrorCode.SYSTEM_ERROR, "\u6587\u4ef6\u683c\u5f0f\u4e0d\u652f\u6301");
        }
    }

    public String getValue() {
        return this.value;
    }

    public void setValue(String value) {
        this.value = value;
    }
}
