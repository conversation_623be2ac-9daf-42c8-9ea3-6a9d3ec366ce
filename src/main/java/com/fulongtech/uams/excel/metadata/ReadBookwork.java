/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.springframework.web.multipart.MultipartFile
 */
package com.fulongtech.uams.excel.metadata;

import com.fulongtech.uams.excel.support.ExcelTypeEnum;
import java.io.InputStream;
import org.springframework.web.multipart.MultipartFile;

public class ReadBookwork {
    private ExcelTypeEnum excelType;
    private InputStream inputStream;
    private String fileName;
    private MultipartFile file;

    public ExcelTypeEnum getExcelType() {
        return this.excelType;
    }

    public InputStream getInputStream() {
        return this.inputStream;
    }

    public String getFileName() {
        return this.fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public void setInputStream(InputStream inputStream) {
        this.inputStream = inputStream;
    }

    public void setExcelType(ExcelTypeEnum excelType) {
        this.excelType = excelType;
    }

    @Deprecated
    public ReadBookwork(InputStream inputStream, String fileName) {
        this.inputStream = inputStream;
    }

    public MultipartFile getFile() {
        return this.file;
    }

    public void setFile(MultipartFile file) {
        this.file = file;
    }

    public ReadBookwork(MultipartFile file) {
        this.file = file;
        this.excelType = ExcelTypeEnum.valueOf(this);
    }
}
