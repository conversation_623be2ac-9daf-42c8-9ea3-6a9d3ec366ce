/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.servlet.http.HttpServletRequest
 *  javax.servlet.http.HttpServletResponse
 *  org.springframework.web.multipart.MultipartFile
 */
package com.fulongtech.uams.excel;

import com.fulongtech.uams.excel.builder.ExcelExportBuilder;
import com.fulongtech.uams.excel.builder.ExcelReaderBuilder;
import com.fulongtech.uams.model.ExcelUploadLog;
import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;

public class UamsExcelFactory<T> {
    public static ExcelReaderBuilder readBuilder(MultipartFile file, ExcelUploadLog uploadLog) {
        return new ExcelReaderBuilder(file, uploadLog);
    }

    public static ExcelExportBuilder exportBuilder(String title, String[] columnName, List<List<String>> dataList, HttpServletRequest request, HttpServletResponse response) {
        return new ExcelExportBuilder(title, columnName, dataList, request, response);
    }
}
