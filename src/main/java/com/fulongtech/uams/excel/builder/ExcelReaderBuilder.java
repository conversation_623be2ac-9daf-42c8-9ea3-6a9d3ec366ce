/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  com.google.common.collect.Lists
 *  javafx.util.Pair
 *  org.apache.poi.hssf.usermodel.HSSFWorkbook
 *  org.apache.poi.ss.usermodel.Cell
 *  org.apache.poi.ss.usermodel.Row
 *  org.apache.poi.ss.usermodel.Sheet
 *  org.apache.poi.ss.usermodel.Workbook
 *  org.apache.poi.xssf.usermodel.XSSFWorkbook
 *  org.springframework.util.StringUtils
 *  org.springframework.web.multipart.MultipartFile
 *  org.yaml.snakeyaml.Yaml
 */
package com.fulongtech.uams.excel.builder;

import com.fulongtech.uams.annotation.ExcelProperty;
import com.fulongtech.uams.excel.metadata.ReadBookwork;
import com.fulongtech.uams.excel.support.ExcelTypeEnum;
import com.fulongtech.uams.exception.ErrorCode;
import com.fulongtech.uams.exception.UamsException;
import com.fulongtech.uams.model.ExcelLogType;
import com.fulongtech.uams.model.ExcelUploadLog;
import com.fulongtech.uams.model.Group;
import com.fulongtech.uams.model.SysUser;
import com.fulongtech.uams.util.Md5Util;
import com.fulongtech.uams.util.SysUserValidator;
import com.google.common.collect.Lists;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.sql.Timestamp;
import java.time.Instant;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import javafx.util.Pair;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;
import org.yaml.snakeyaml.Yaml;

public class ExcelReaderBuilder {
    private static final String GROUP = "groups";
    private Map<String, String> fieldHeaderNameMapping;
    private Workbook workbook;
    private ExcelUploadLog uploadLog;
    private static String defaultPassword;

    public static void setDefaultPassword() {
        File ymlFile = new File("./config/application.yml");
        Yaml yaml = new Yaml();
        try {
            Map map = (Map)yaml.load((InputStream)new FileInputStream(ymlFile));
            Map uams = (Map)map.get("uams");
            Map policy = (Map)uams.get("security-policy");
            Map password = (Map)policy.get("password");
            defaultPassword = (String)password.get("defaultpwd");
        }
        catch (FileNotFoundException e) {
            e.printStackTrace();
        }
    }

    public ExcelReaderBuilder(MultipartFile file, ExcelUploadLog uploadLog) {
        ReadBookwork readBookwork = new ReadBookwork(file);
        this.uploadLog = uploadLog != null ? uploadLog : new ExcelUploadLog();
        this.workbook = this.choiceExcelExecutor(readBookwork);
    }

    public Map<Integer, SysUser> read(Class object) {
        HashMap<String, Integer> processName = new HashMap<String, Integer>(20);
        Sheet sheet = this.workbook.getSheetAt(0);
        Map<String, List<Integer>> mapping = this.createMapping(object, sheet.getRow(1));
        HashMap<Integer, SysUser> userMap = new HashMap<Integer, SysUser>(50);
        int lastRowNum = sheet.getLastRowNum();
        for (int i = 2; i <= lastRowNum; ++i) {
            boolean isRepeat = false;
            Row row = sheet.getRow(i);
            if (row == null) {
                this.uploadLog.addError(ExcelLogType.LINE, i + 1, "\u6570\u636e\u4e3a\u7a7a");
                this.uploadLog.addFailNum();
                continue;
            }
            if (row.getCell(mapping.get("username").get(0).intValue()) != null) {
                row.getCell(mapping.get("username").get(0).intValue()).setCellType(1);
                if (!StringUtils.isEmpty((Object)row.getCell(mapping.get("username").get(0).intValue()).getStringCellValue())) {
                    if (processName.containsKey(row.getCell(mapping.get("username").get(0).intValue()).getStringCellValue())) {
                        isRepeat = true;
                    } else {
                        processName.put(row.getCell(mapping.get("username").get(0).intValue()).getStringCellValue(), i + 1);
                    }
                }
            }
            ExcelUploadLog log = new ExcelUploadLog();
            SysUser sysUser = this.buildObject(row, mapping, log);
            if (isRepeat) {
                this.uploadLog.merge(log);
                Integer index = (Integer)processName.get(row.getCell(mapping.get("username").get(0).intValue()).getStringCellValue());
                this.uploadLog.addError(ExcelLogType.LINE, i + 1, "\u7528\u6237\u540d " + row.getCell(mapping.get("username").get(0).intValue()).getStringCellValue() + " \u548c\u7b2c" + index + "\u884c\u91cd\u590d");
                this.uploadLog.addFailNum();
                continue;
            }
            if (sysUser == null) {
                this.uploadLog.merge(log);
                this.uploadLog.addFailNum();
                continue;
            }
            this.uploadLog.merge(log);
            userMap.put(row.getRowNum() + 1, sysUser);
        }
        return userMap;
    }

    public ExcelUploadLog getUploadLog() {
        return this.uploadLog;
    }

    private Map<String, List<Integer>> createMapping(Class sysUser, Row headerRow) {
        Field[] fields;
        HashMap<String, List<Integer>> fieldLocationMapping = new HashMap<String, List<Integer>>(10);
        this.fieldHeaderNameMapping = new LinkedHashMap<String, String>(20);
        HashMap<String, Pair<String, Boolean>> headerRequire = new HashMap<String, Pair<String, Boolean>>(10);
        HashMap<String, Pair<String, Boolean>> headerIsMulti = new HashMap<String, Pair<String, Boolean>>(10);
        for (Field field : fields = sysUser.getDeclaredFields()) {
            if (field.getAnnotation(ExcelProperty.class) == null) continue;
            String value = field.getAnnotation(ExcelProperty.class).value();
            boolean require = field.getAnnotation(ExcelProperty.class).require();
            boolean multi = field.getAnnotation(ExcelProperty.class).multi();
            this.fieldHeaderNameMapping.put(value, field.getName());
            headerRequire.put(field.getName(), (Pair<String, Boolean>)new Pair((Object)value, (Object)require));
            headerIsMulti.put(field.getName(), (Pair<String, Boolean>)new Pair((Object)value, (Object)multi));
        }
        int length = headerRow.getLastCellNum();
        for (int i = 0; i < length; ++i) {
            Cell cell = headerRow.getCell(i);
            if (cell == null) continue;
            cell.setCellType(1);
            String cellValue = cell.getStringCellValue();
            if (StringUtils.isEmpty((Object)cellValue)) continue;
            if (this.fieldHeaderNameMapping.containsKey(cellValue)) {
                if (fieldLocationMapping.containsKey(this.fieldHeaderNameMapping.get(cellValue))) {
                    ((List)fieldLocationMapping.get(this.fieldHeaderNameMapping.get(cellValue))).add(i);
                    continue;
                }
                fieldLocationMapping.put(this.fieldHeaderNameMapping.get(cellValue), Lists.newArrayList((Object[])new Integer[]{i}));
                continue;
            }
            this.uploadLog.addWarn(ExcelLogType.HEADER, 0, "\u5217\u540d\u4e3a\uff1a" + cellValue + "\uff0c\u662f\u591a\u4f59\u7684\uff0c\u8bf7\u68c0\u67e5");
        }
        this.validateHeader(headerRequire, fieldLocationMapping, headerIsMulti);
        return fieldLocationMapping;
    }

    private boolean validateHeader(Map<String, Pair<String, Boolean>> header, Map<String, List<Integer>> fieldLocationMapping, Map<String, Pair<String, Boolean>> headerIsMulti) {
        boolean isError = false;
        for (Map.Entry<String, Pair<String, Boolean>> map : header.entrySet()) {
            if (fieldLocationMapping.containsKey(map.getKey())) continue;
            if (((Boolean)map.getValue().getValue()).booleanValue()) {
                isError = true;
                this.uploadLog.addError(ExcelLogType.HEADER, 0, "\u6a21\u677f\u4e2d\u6ca1\u6709" + (String)map.getValue().getKey() + "\u4e00\u5217\uff0c\u8bf7\u68c0\u67e5\uff01");
                continue;
            }
            this.uploadLog.addWarn(ExcelLogType.HEADER, 0, "\u6a21\u677f\u4e2d\u6ca1\u6709" + (String)map.getValue().getKey() + "\u4e00\u5217\uff0c\u8bf7\u68c0\u67e5\uff01");
        }
        for (Map.Entry<String, Pair<String, Boolean>> map : headerIsMulti.entrySet()) {
            if (!fieldLocationMapping.containsKey(map.getKey()) || ((Boolean)map.getValue().getValue()).booleanValue() || fieldLocationMapping.get(map.getKey()).size() <= 1) continue;
            this.uploadLog.addError(ExcelLogType.HEADER, 0, "\u6a21\u677f\u4e2d\u5217\u540d\u4e3a\uff1a" + (String)map.getValue().getKey() + "\uff0c\u91cd\u590d");
            isError = true;
        }
        if (isError) {
            throw new UamsException(ErrorCode.UNPROCESABLE_ENTITY, "\u6a21\u677f\u4e2d\u53c2\u6570\u6709\u8bef");
        }
        return true;
    }

    private SysUser buildObject(Row row, Map<String, List<Integer>> fieldLocationMapping, ExcelUploadLog uploadLog) {
        if (uploadLog == null) {
            uploadLog = new ExcelUploadLog();
        }
        SysUser sysUser = new SysUser();
        ArrayList<Group> groups = new ArrayList<Group>(10);
        for (Map.Entry<String, List<Integer>> map : fieldLocationMapping.entrySet()) {
            String name = map.getKey();
            try {
                Field declaredField = sysUser.getClass().getDeclaredField(name);
                declaredField.setAccessible(true);
                if (!GROUP.equals(map.getKey())) {
                    if (row.getCell(map.getValue().get(0).intValue()) == null) continue;
                    row.getCell(map.getValue().get(0).intValue()).setCellType(1);
                    String value = row.getCell(map.getValue().get(0).intValue()).getStringCellValue();
                    declaredField.set(sysUser, value);
                    continue;
                }
                for (int i = 0; i < map.getValue().size(); ++i) {
                    if (row.getCell(map.getValue().get(i).intValue()) == null) continue;
                    row.getCell(map.getValue().get(i).intValue()).setCellType(1);
                    String groupName = row.getCell(map.getValue().get(i).intValue()).getStringCellValue();
                    if (StringUtils.isEmpty((Object)groupName)) continue;
                    Group group = new Group();
                    if (!SysUserValidator.checkGroupName(groupName, uploadLog, row.getRowNum() + 1)) continue;
                    group.setName(groupName);
                    groups.add(group);
                }
            }
            catch (Exception e) {
                e.printStackTrace();
            }
        }
        if (sysUser.isEmpty()) {
            uploadLog.addError(ExcelLogType.LINE, row.getRowNum() + 1, "\u6570\u636e\u4e3a\u7a7a");
            return null;
        }
        if (SysUserValidator.checkUserProperty(sysUser, uploadLog, row.getRowNum() + 1)) {
            return null;
        }
        sysUser.setGroups(groups);
        sysUser.setChangePwdTime(Timestamp.from(Instant.now()));
        if (StringUtils.isEmpty((Object)sysUser.getPassword())) {
            sysUser.setPassword(defaultPassword);
        } else {
            String code = sysUser.getPassword();
            String password = Md5Util.getMD5(code);
            sysUser.setPassword(password);
        }
        return sysUser;
    }

    private String getKey(Map<String, String> map, String value) {
        for (Map.Entry<String, String> item : map.entrySet()) {
            if (!value.equals(item.getValue())) continue;
            return item.getKey();
        }
        return null;
    }

    private Workbook choiceExcelExecutor(ReadBookwork readBookwork) {
        ExcelTypeEnum excelType = ExcelTypeEnum.valueOf(readBookwork);
        try {
            switch (excelType) {
                case XLS: {
                    return new HSSFWorkbook(readBookwork.getFile().getInputStream());
                }
                case XLSX: {
                    return new XSSFWorkbook(readBookwork.getFile().getInputStream());
                }
            }
            return new HSSFWorkbook(readBookwork.getFile().getInputStream());
        }
        catch (IOException e) {
            e.printStackTrace();
            return null;
        }
    }
}
