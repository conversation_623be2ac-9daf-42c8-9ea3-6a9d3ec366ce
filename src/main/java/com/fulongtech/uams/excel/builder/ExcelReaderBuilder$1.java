/*
 * Decompiled with CFR 0.152.
 */
package com.fulongtech.uams.excel.builder;

import com.fulongtech.uams.excel.support.ExcelTypeEnum;

static class ExcelReaderBuilder.1 {
    static final /* synthetic */ int[] $SwitchMap$com$fulongtech$uams$excel$support$ExcelTypeEnum;

    static {
        $SwitchMap$com$fulongtech$uams$excel$support$ExcelTypeEnum = new int[ExcelTypeEnum.values().length];
        try {
            ExcelReaderBuilder.1.$SwitchMap$com$fulongtech$uams$excel$support$ExcelTypeEnum[ExcelTypeEnum.XLS.ordinal()] = 1;
        }
        catch (NoSuchFieldError noSuchFieldError) {
            // empty catch block
        }
        try {
            ExcelReaderBuilder.1.$SwitchMap$com$fulongtech$uams$excel$support$ExcelTypeEnum[ExcelTypeEnum.XLSX.ordinal()] = 2;
        }
        catch (NoSuchFieldError noSuchFieldError) {
            // empty catch block
        }
    }
}
