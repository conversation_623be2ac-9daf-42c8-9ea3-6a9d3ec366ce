/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  cn.hutool.core.util.StrUtil
 *  javax.servlet.ServletOutputStream
 *  javax.servlet.http.HttpServletRequest
 *  javax.servlet.http.HttpServletResponse
 *  org.apache.poi.hssf.usermodel.HSSFCell
 *  org.apache.poi.hssf.usermodel.HSSFCellStyle
 *  org.apache.poi.hssf.usermodel.HSSFFont
 *  org.apache.poi.hssf.usermodel.HSSFRichTextString
 *  org.apache.poi.hssf.usermodel.HSSFRow
 *  org.apache.poi.hssf.usermodel.HSSFSheet
 *  org.apache.poi.hssf.usermodel.HSSFWorkbook
 *  org.apache.poi.ss.usermodel.IndexedColors
 *  org.apache.poi.ss.usermodel.RichTextString
 *  org.apache.poi.ss.util.CellRangeAddress
 */
package com.fulongtech.uams.excel.builder;

import cn.hutool.core.util.StrUtil;
import java.io.IOException;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.List;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFFont;
import org.apache.poi.hssf.usermodel.HSSFRichTextString;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.RichTextString;
import org.apache.poi.ss.util.CellRangeAddress;

public class ExcelExportBuilder {
    private String title;
    private String[] columnName;
    private List<List<String>> dataList = new ArrayList<List<String>>();
    private HttpServletRequest request;
    private HttpServletResponse response;

    public ExcelExportBuilder(String title, String[] columnName, List<List<String>> dataList, HttpServletRequest request, HttpServletResponse response) {
        this.dataList = dataList;
        this.columnName = columnName;
        this.title = title;
        this.request = request;
        this.response = response;
    }

    public void export() throws Exception {
        try {
            HSSFWorkbook workbook = new HSSFWorkbook();
            HSSFSheet sheet = workbook.createSheet(this.title);
            HSSFRow rowm = sheet.createRow(0);
            HSSFCell cellTiltle = rowm.createCell(0);
            HSSFCellStyle columnTopStyle = this.getColumnTopStyle(workbook);
            HSSFCellStyle style = this.getStyle(workbook);
            sheet.addMergedRegion(new CellRangeAddress(0, 1, 0, this.columnName.length - 1));
            cellTiltle.setCellStyle(columnTopStyle);
            cellTiltle.setCellValue(this.title);
            int columnNum = this.columnName.length;
            HSSFRow rowRowName = sheet.createRow(2);
            for (int n = 0; n < columnNum; ++n) {
                HSSFCell cellRowName = rowRowName.createCell(n);
                cellRowName.setCellType(1);
                HSSFRichTextString text = new HSSFRichTextString(this.columnName[n]);
                cellRowName.setCellValue((RichTextString)text);
                cellRowName.setCellStyle(columnTopStyle);
            }
            for (int i = 0; i < this.dataList.size(); ++i) {
                List<String> obj = this.dataList.get(i);
                HSSFRow row = sheet.createRow(i + 3);
                for (int j = 0; j < obj.size(); ++j) {
                    HSSFCell cell = null;
                    if (j == 0) {
                        cell = row.createCell(j, 0);
                        cell.setCellValue((double)(i + 1));
                    } else {
                        cell = row.createCell(j, 1);
                        if (StrUtil.isNotBlank((CharSequence)obj.get(j))) {
                            // empty if block
                        }
                        if (!"".equals(obj.get(j)) && obj.get(j) != null) {
                            cell.setCellValue(obj.get(j).toString());
                        } else if ("".equals(obj.get(j))) {
                            cell.setCellValue(" ");
                        }
                    }
                    cell.setCellStyle(style);
                }
            }
            for (int colNum = 0; colNum < columnNum; ++colNum) {
                int columnWidth = sheet.getColumnWidth(colNum) / 256;
                for (int rowNum = 0; rowNum < sheet.getLastRowNum(); ++rowNum) {
                    int length;
                    HSSFCell currentCell;
                    HSSFRow currentRow = sheet.getRow(rowNum) == null ? sheet.createRow(rowNum) : sheet.getRow(rowNum);
                    if (currentRow.getCell(colNum) == null || (currentCell = currentRow.getCell(colNum)).getCellType() != 1 || columnWidth >= (length = currentCell.getStringCellValue().getBytes().length)) continue;
                    columnWidth = length;
                }
                if (colNum == 0) {
                    sheet.setColumnWidth(colNum, (columnWidth - 2) * 256);
                    continue;
                }
                sheet.setColumnWidth(colNum, (columnWidth + 4) * 256);
            }
            if (workbook != null) {
                try {
                    String fileName = String.valueOf(System.currentTimeMillis()).substring(4, 13) + ".xls";
                    String headStr = "attachment; filename=\"" + fileName + "\"";
                    this.response.setContentType("APPLICATION/OCTET-STREAM");
                    this.response.setHeader("Content-Disposition", headStr);
                    ServletOutputStream outputStreamt = this.response.getOutputStream();
                    workbook.write((OutputStream)outputStreamt);
                }
                catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        catch (Exception e) {
            e.printStackTrace();
        }
    }

    public HSSFCellStyle getColumnTopStyle(HSSFWorkbook workbook) {
        HSSFFont font = workbook.createFont();
        font.setFontHeightInPoints((short)16);
        font.setBoldweight((short)700);
        font.setFontName("\u5b8b\u4f53");
        HSSFCellStyle style = workbook.createCellStyle();
        style.setFillForegroundColor(IndexedColors.BLUE.getIndex());
        style.setBorderBottom((short)1);
        style.setBottomBorderColor((short)8);
        style.setBorderLeft((short)1);
        style.setLeftBorderColor((short)8);
        style.setBorderRight((short)1);
        style.setRightBorderColor((short)8);
        style.setBorderTop((short)1);
        style.setTopBorderColor((short)8);
        style.setFont(font);
        style.setWrapText(false);
        style.setAlignment((short)2);
        style.setVerticalAlignment((short)1);
        return style;
    }

    public HSSFCellStyle getStyle(HSSFWorkbook workbook) {
        HSSFFont font = workbook.createFont();
        font.setFontHeightInPoints((short)11);
        font.setFontName("\u5b8b\u4f53");
        HSSFCellStyle style = workbook.createCellStyle();
        style.setBorderBottom((short)1);
        style.setBottomBorderColor((short)8);
        style.setBorderLeft((short)1);
        style.setLeftBorderColor((short)8);
        style.setBorderRight((short)1);
        style.setRightBorderColor((short)8);
        style.setBorderTop((short)1);
        style.setTopBorderColor((short)8);
        style.setFont(font);
        style.setWrapText(false);
        style.setAlignment((short)2);
        style.setVerticalAlignment((short)1);
        return style;
    }
}
