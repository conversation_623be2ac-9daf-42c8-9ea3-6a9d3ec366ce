/*
 * Decompiled with CFR 0.152.
 */
package com.fulongtech.uams.exception;

public enum ErrorCode {
    RESOURCE_NOT_FOUND(410, "RESOURCE_NOT_FOUND"),
    UNPROCESABLE_ENTITY(422, "UNPROCESABLE_ENTITY"),
    RESOURCE_ALREADY_EXISTS(409, "RESOURCE_ALREADY_EXISTS"),
    UNAUTHORIZED(403, "UNAUTHORIZED"),
    BAD_INPUT_PARAMETER(400, "RESOURCE_NOT_FOUND"),
    FORBIDDEN_OPERATE(555, "FORBIDDEN_OPERATE"),
    SYSTEM_ERROR(500, "SYSTEM_ERROR"),
    VERIFICATION_CODE_EXPIRED(431, "VERIFICATION_CODE_EXPIRED"),
    VERIFICATION_CODE_ERROR(430, "VERIFICATION_CODE_ERROR"),
    <PERSON><PERSON><PERSON><PERSON>_ALREADY_EXISTS(432, "MO<PERSON><PERSON>_ALREADY_EXISTS"),
    SMS_SEND_ERROR(433, "SMS_SEND_ERROR");

    private final int value;
    private final String reasonPhrase;

    private ErrorCode(int value, String reasonPhrase) {
        this.value = value;
        this.reasonPhrase = reasonPhrase;
    }

    public int value() {
        return this.value;
    }

    public String getReasonPhrase() {
        return this.reasonPhrase;
    }

    public static ErrorCode valueOf(int statusCode) {
        for (ErrorCode status : ErrorCode.values()) {
            if (status.value != statusCode) continue;
            return status;
        }
        throw new IllegalArgumentException("No matching constant for [" + statusCode + "]");
    }
}
