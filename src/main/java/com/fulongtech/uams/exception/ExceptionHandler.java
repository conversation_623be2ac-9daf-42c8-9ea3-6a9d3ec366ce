/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.apache.log4j.LogManager
 *  org.apache.log4j.Logger
 *  org.springframework.dao.DataAccessException
 *  org.springframework.dao.DataAccessResourceFailureException
 *  org.springframework.dao.RecoverableDataAccessException
 */
package com.fulongtech.uams.exception;

import java.sql.SQLException;
import java.sql.SQLRecoverableException;
import java.sql.SQLSyntaxErrorException;
import org.apache.log4j.LogManager;
import org.apache.log4j.Logger;
import org.springframework.dao.DataAccessException;
import org.springframework.dao.DataAccessResourceFailureException;
import org.springframework.dao.RecoverableDataAccessException;

public class ExceptionHandler {
    private static final Logger log = LogManager.getLogger(ExceptionHandler.class);

    public static String DataBaseException(Throwable throwable) {
        if (throwable != null) {
            if (throwable instanceof RecoverableDataAccessException) {
                log.error((Object)"\u6570\u636e\u5e93\u5b95\u673a\u4e86");
                return "\u6570\u636e\u5e93\u670d\u52a1\u51fa\u73b0\u4e86\u5f02\u5e38";
            }
            if (throwable instanceof SQLSyntaxErrorException) {
                log.error((Object)"\u6570\u636e\u5e93\u8868\u627e\u4e0d\u5230\u4e86");
                return "\u6570\u636e\u5e93\u670d\u52a1\u51fa\u73b0\u4e86\u5f02\u5e38";
            }
            if (throwable instanceof SQLRecoverableException) {
                log.error((Object)"\u6570\u636e\u5e93\u8fde\u63a5\u9519\u8bef");
                return "\u6570\u636e\u5e93\u670d\u52a1\u51fa\u73b0\u4e86\u5f02\u5e38";
            }
            if (throwable instanceof DataAccessResourceFailureException) {
                log.error((Object)"\u6570\u636e\u5e93\u8fde\u63a5\u9519\u8bef");
                return "\u6570\u636e\u5e93\u670d\u52a1\u51fa\u73b0\u4e86\u5f02\u5e38";
            }
            if (throwable instanceof SQLException) {
                log.error((Object)"\u6570\u636e\u5e93\u51fa\u73b0\u4e86\u672a\u77e5\u5f02\u5e38");
                return "\u6570\u636e\u5e93\u670d\u52a1\u51fa\u73b0\u4e86\u5f02\u5e38";
            }
            if (throwable instanceof DataAccessException) {
                log.error((Object)"\u6570\u636e\u5e93\u8fde\u63a5\u51fa\u73b0\u4e86\u5f02\u5e38");
                return "\u6570\u636e\u5e93\u670d\u52a1\u51fa\u73b0\u4e86\u5f02\u5e38";
            }
        }
        return null;
    }
}
