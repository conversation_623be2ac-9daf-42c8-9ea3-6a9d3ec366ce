/*
 * Decompiled with CFR 0.152.
 */
package com.fulongtech.uams.exception;

import com.fulongtech.uams.exception.ErrorCode;

public class UamsError {
    private ErrorCode code;
    private String message;

    public UamsError(ErrorCode code, String message) {
        this.code = code;
        this.message = message;
    }

    public ErrorCode getCode() {
        return this.code;
    }

    public void setCode(ErrorCode code) {
        this.code = code;
    }

    public String getMessage() {
        return this.message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
}
