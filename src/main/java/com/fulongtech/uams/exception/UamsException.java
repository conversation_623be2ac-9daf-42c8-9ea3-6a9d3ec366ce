/*
 * Decompiled with CFR 0.152.
 */
package com.fulongtech.uams.exception;

import com.fulongtech.uams.exception.ErrorCode;

public class UamsException
extends RuntimeException {
    private ErrorCode code;
    private String message;
    private static final long serialVersionUID = 6208612658967690051L;

    public UamsException(ErrorCode code, String message) {
        this.code = code;
        this.message = message;
    }

    public ErrorCode getCode() {
        return this.code;
    }

    public void setCode(ErrorCode code) {
        this.code = code;
    }

    @Override
    public String getMessage() {
        return this.message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
}
