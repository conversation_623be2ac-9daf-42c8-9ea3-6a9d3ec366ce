/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.servlet.http.HttpServletRequest
 *  javax.servlet.http.HttpServletResponse
 *  org.apache.shiro.authz.AuthorizationException
 *  org.apache.shiro.authz.UnauthorizedException
 *  org.springframework.beans.factory.annotation.Autowired
 *  org.springframework.context.MessageSource
 *  org.springframework.dao.DataAccessException
 *  org.springframework.dao.DataAccessResourceFailureException
 *  org.springframework.dao.RecoverableDataAccessException
 *  org.springframework.http.HttpStatus
 *  org.springframework.web.bind.annotation.ControllerAdvice
 *  org.springframework.web.bind.annotation.ExceptionHandler
 *  org.springframework.web.bind.annotation.ResponseBody
 *  org.springframework.web.bind.annotation.ResponseStatus
 *  org.springframework.web.context.request.NativeWebRequest
 *  org.springframework.web.servlet.ModelAndView
 *  org.springframework.web.servlet.support.RequestContextUtils
 */
package com.fulongtech.uams.exception;

import com.fulongtech.uams.exception.ErrorCode;
import com.fulongtech.uams.exception.UamsError;
import com.fulongtech.uams.exception.UamsException;
import java.sql.SQLException;
import java.sql.SQLRecoverableException;
import java.sql.SQLSyntaxErrorException;
import java.util.Locale;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.shiro.authz.AuthorizationException;
import org.apache.shiro.authz.UnauthorizedException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.dao.DataAccessException;
import org.springframework.dao.DataAccessResourceFailureException;
import org.springframework.dao.RecoverableDataAccessException;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.support.RequestContextUtils;

@ControllerAdvice
public class ExceptionController {
    @Autowired
    private MessageSource messageSource;

    @ExceptionHandler(value={SQLException.class, SQLSyntaxErrorException.class})
    @ResponseBody
    public UamsError processDatabaseSQLException(HttpServletResponse response, HttpServletRequest request) {
        Locale locale = RequestContextUtils.getLocale((HttpServletRequest)request);
        UamsException exception = new UamsException(ErrorCode.SYSTEM_ERROR, this.messageSource.getMessage("database-error", null, locale));
        response.setStatus(exception.getCode().value());
        return new UamsError(exception.getCode(), exception.getMessage());
    }

    @ExceptionHandler(value={DataAccessException.class, DataAccessResourceFailureException.class, RecoverableDataAccessException.class, SQLRecoverableException.class})
    @ResponseBody
    public UamsError processDatabaseAccessException(HttpServletResponse response, HttpServletRequest request) {
        Locale locale = RequestContextUtils.getLocale((HttpServletRequest)request);
        UamsException exception = new UamsException(ErrorCode.SYSTEM_ERROR, this.messageSource.getMessage("database-error", null, locale));
        response.setStatus(exception.getCode().value());
        return new UamsError(exception.getCode(), exception.getMessage());
    }

    @ExceptionHandler(value={UnauthorizedException.class})
    @ResponseStatus(value=HttpStatus.UNAUTHORIZED)
    public ModelAndView processUnauthenticatedException(NativeWebRequest request, UnauthorizedException e) {
        return new ModelAndView("/errorpage/unauthorized").addObject("exception", (Object)e);
    }

    @ExceptionHandler(value={AuthorizationException.class})
    @ResponseStatus(value=HttpStatus.UNAUTHORIZED)
    public ModelAndView processAuthorizationException(NativeWebRequest request, AuthorizationException e) {
        return new ModelAndView("login");
    }

    @ExceptionHandler(value={UamsException.class})
    @ResponseBody
    public UamsError processUamsException(NativeWebRequest request, HttpServletResponse response, UamsException e) {
        response.setStatus(e.getCode().value());
        return new UamsError(e.getCode(), e.getMessage());
    }
}
