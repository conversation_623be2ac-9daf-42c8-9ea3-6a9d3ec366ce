/*
 * Decompiled with CFR 0.152.
 */
package com.fulongtech.uams.vo;

import java.util.Objects;

public class LoginRat {
    private String groupId;
    private String groupName;
    private Double loginRat;
    private Integer accountNum;
    private Integer loginAccountNum;
    private Integer loginNum;

    public String getGroupId() {
        return this.groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }

    public String getGroupName() {
        return this.groupName;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }

    public Double getLoginRat() {
        return this.loginRat;
    }

    public void setLoginRat(Double loginRat) {
        this.loginRat = loginRat;
    }

    public Integer getAccountNum() {
        return this.accountNum;
    }

    public void setAccountNum(Integer accountNum) {
        this.accountNum = accountNum;
    }

    public Integer getLoginAccountNum() {
        return this.loginAccountNum;
    }

    public void setLoginAccountNum(Integer loginAccountNum) {
        this.loginAccountNum = loginAccountNum;
    }

    public Integer getLoginNum() {
        return this.loginNum;
    }

    public void setLoginNum(Integer loginNum) {
        this.loginNum = loginNum;
    }

    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || this.getClass() != o.getClass()) {
            return false;
        }
        LoginRat loginRat1 = (LoginRat)o;
        return Objects.equals(this.groupId, loginRat1.groupId) && Objects.equals(this.groupName, loginRat1.groupName) && Objects.equals(this.loginRat, loginRat1.loginRat) && Objects.equals(this.accountNum, loginRat1.accountNum) && Objects.equals(this.loginAccountNum, loginRat1.loginAccountNum) && Objects.equals(this.loginNum, loginRat1.loginNum);
    }

    public int hashCode() {
        return Objects.hash(this.groupId, this.groupName, this.loginRat, this.accountNum, this.loginAccountNum, this.loginNum);
    }

    public String toString() {
        return "LoginRat{groupId='" + this.groupId + '\'' + ", groupName='" + this.groupName + '\'' + ", loginRat=" + this.loginRat + ", accountNum=" + this.accountNum + ", loginAccountNum=" + this.loginAccountNum + ", loginNum=" + this.loginNum + '}';
    }
}
