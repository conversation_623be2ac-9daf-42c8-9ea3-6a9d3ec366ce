/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.springframework.beans.BeanUtils
 */
package com.fulongtech.uams.vo;

import org.springframework.beans.BeanUtils;

public class UserWrapper {
    private String id;
    private String username;
    private String name;
    private String email;
    private String phone;
    private String mobilePhone;
    private String idCard;

    private UserWrapper() {
    }

    public static UserWrapper getInstance(Object sysUser) {
        UserWrapper userWrapper = new UserWrapper();
        BeanUtils.copyProperties((Object)sysUser, (Object)userWrapper);
        return userWrapper;
    }

    public String getId() {
        return this.id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getUsername() {
        return this.username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getName() {
        return this.name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getEmail() {
        return this.email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getPhone() {
        return this.phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getMobilePhone() {
        return this.mobilePhone;
    }

    public void setMobilePhone(String mobilePhone) {
        this.mobilePhone = mobilePhone;
    }

    public String getIdCard() {
        return this.idCard;
    }

    public void setIdCard(String idCard) {
        this.idCard = idCard;
    }
}
