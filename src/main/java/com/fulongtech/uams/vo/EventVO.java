/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  com.fasterxml.jackson.annotation.JsonFormat
 */
package com.fulongtech.uams.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fulongtech.uams.model.Group;
import java.sql.Timestamp;
import java.util.List;
import java.util.Objects;

public class EventVO {
    private String id;
    private String userId;
    private String userName;
    private String name;
    private List<Group> groups;
    private String ip;
    @JsonFormat(pattern="YYYY-MM-dd HH:mm:ss", timezone="GMT+8")
    private Timestamp eventTime;
    private String eventType;

    public String getId() {
        return this.id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getUserId() {
        return this.userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return this.userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public List<Group> getGroups() {
        return this.groups;
    }

    public void setGroups(List<Group> groups) {
        this.groups = groups;
    }

    public String getIp() {
        return this.ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public Timestamp getEventTime() {
        return this.eventTime;
    }

    public void setEventTime(Timestamp eventTime) {
        this.eventTime = eventTime;
    }

    public String getEventType() {
        return this.eventType;
    }

    public void setEventType(String eventType) {
        this.eventType = eventType;
    }

    public String getName() {
        return this.name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String toString() {
        return "EventVO{id='" + this.id + '\'' + ", userId='" + this.userId + '\'' + ", userName='" + this.userName + '\'' + ", name='" + this.name + '\'' + ", groups=" + this.groups + ", ip='" + this.ip + '\'' + ", eventTime=" + this.eventTime + ", eventType='" + this.eventType + '\'' + '}';
    }

    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || this.getClass() != o.getClass()) {
            return false;
        }
        EventVO eventVO = (EventVO)o;
        return Objects.equals(this.id, eventVO.id) && Objects.equals(this.userId, eventVO.userId) && Objects.equals(this.userName, eventVO.userName) && Objects.equals(this.name, eventVO.name) && Objects.equals(this.groups, eventVO.groups) && Objects.equals(this.ip, eventVO.ip) && Objects.equals(this.eventTime, eventVO.eventTime) && Objects.equals(this.eventType, eventVO.eventType);
    }

    public int hashCode() {
        return Objects.hash(this.id, this.userId, this.userName, this.name, this.groups, this.ip, this.eventTime, this.eventType);
    }
}
