/*
 * Decompiled with CFR 0.152.
 */
package com.fulongtech.uams.service;

import com.fulongtech.uams.sms.VerificationCodeInfo;

public interface RedisService {
    public Boolean setVerificationCodeInfo(String var1, VerificationCodeInfo var2, Integer var3);

    public VerificationCodeInfo getVerificationCodeInfo(String var1);

    public Boolean setIntegerValue(String var1, Integer var2, Long var3);

    public void deleteValue(String var1);

    public Boolean hasKey(String var1);

    public Integer getIntegerValue(String var1);
}
