/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  com.baomidou.mybatisplus.extension.service.IService
 */
package com.fulongtech.uams.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fulongtech.uams.model.Group;
import com.fulongtech.uams.model.SysUser;
import java.util.List;
import java.util.Map;

public interface GroupService
extends IService<Group> {
    public List<Group> getGroupByUserId(String var1);

    public Group add(Group var1);

    public int deleteById(String var1);

    public List<SysUser> getUsersByGroupName(String var1);

    public List<Group> getGroups();

    public boolean deleteGroups(List<String> var1);

    public List<SysUser> getGroupUser(String var1);

    public Group updateGroupInfo(Group var1);

    public Group getGroupById(String var1);

    public List<Group> getGroupChildren(String var1);

    public Group getGroupByName(String var1);

    public void setGroupsForUser(Map<String, SysUser> var1);

    public List<SysUser> getUsersByGroupIds(String[] var1);
}
