/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  com.baomidou.mybatisplus.extension.service.IService
 */
package com.fulongtech.uams.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fulongtech.uams.model.UserGroupRelation;
import java.util.List;

public interface RelationService
extends IService<UserGroupRelation> {
    public void delRelationByUserId(String var1);

    public void delRelationByUserIdExceptSys(String var1);

    public void addRelationBatch(String var1, List<String> var2);

    public void deleteRelationByGroupIdAndUserId(String var1, String var2);

    public void addRelations(String var1, List<String> var2);

    public List<String> getManagedGroupsBYUserId(String var1);

    public boolean checkGroupPermissions(String var1, List<UserGroupRelation> var2);

    public boolean upDateGroupRelation(String var1, List<UserGroupRelation> var2);
}
