/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  com.baomidou.mybatisplus.core.conditions.Wrapper
 *  com.baomidou.mybatisplus.core.metadata.IPage
 *  com.baomidou.mybatisplus.extension.plugins.pagination.Page
 *  com.baomidou.mybatisplus.extension.service.IService
 */
package com.fulongtech.uams.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.fulongtech.uams.model.Event;
import com.fulongtech.uams.model.SysUser;
import com.fulongtech.uams.vo.EventPagesVo;

public interface EventService
extends IService<Event> {
    public void addEvent(String var1, SysUser var2, String var3);

    public EventPagesVo searchByParams(String var1, String var2, String var3, String var4, String var5, Integer var6, Integer var7);

    public IPage<Event> selectEventPage(Page<Event> var1, Wrapper<Event> var2);
}
