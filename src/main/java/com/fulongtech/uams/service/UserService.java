/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  com.baomidou.mybatisplus.core.metadata.IPage
 *  com.baomidou.mybatisplus.extension.plugins.pagination.Page
 *  com.baomidou.mybatisplus.extension.service.IService
 *  javax.servlet.http.HttpServletRequest
 *  org.springframework.web.multipart.MultipartFile
 */
package com.fulongtech.uams.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.fulongtech.uams.model.ExcelUploadLog;
import com.fulongtech.uams.model.SysUser;
import java.sql.Timestamp;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import org.springframework.web.multipart.MultipartFile;

public interface UserService
extends IService<SysUser> {
    public SysUser addUser(SysUser var1);

    public SysUser updateUser(SysUser var1);

    public int delete(SysUser var1);

    public SysUser getById(String var1);

    public void changePwd(String var1, String var2, Timestamp var3);

    public SysUser findByUsername(String var1);

    public IPage<SysUser> search(Page<SysUser> var1, String var2);

    public List<SysUser> getUserByGroup(String var1);

    public List<SysUser> getAll();

    public List<SysUser> getSysUsersWithGroups(List<SysUser> var1, SysUser var2);

    public ExcelUploadLog uploadUsers(MultipartFile var1, HttpServletRequest var2);

    public ExcelUploadLog uploadUsers(Map<Integer, SysUser> var1, ExcelUploadLog var2);

    public List<SysUser> getSysUsersWithoutGroups();

    public List<List<String>> exportUserInfo(List<String> var1);

    public boolean mobilePhoneNotBindedByUser(String var1);

    public Integer mobilePhoneNotBindedByUserNumber(String var1);

    public SysUser findByMobilePhone(String var1);

    public List<SysUser> findUserListByMobilePhone(String var1);
}
