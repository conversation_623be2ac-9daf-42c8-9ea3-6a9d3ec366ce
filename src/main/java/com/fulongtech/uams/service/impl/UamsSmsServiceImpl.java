/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  cn.hutool.captcha.generator.RandomGenerator
 *  org.springframework.stereotype.Service
 */
package com.fulongtech.uams.service.impl;

import cn.hutool.captcha.generator.RandomGenerator;
import com.fulongtech.uams.service.UamsSmsService;
import java.io.File;
import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.net.MalformedURLException;
import java.net.URL;
import java.net.URLClassLoader;
import org.springframework.stereotype.Service;

@Service
public class UamsSmsServiceImpl
implements UamsSmsService {
    private RandomGenerator randomGenerator = new RandomGenerator("0123456789", 6);

    @Override
    public boolean sendVerificationCode(String mobilePhone, String code, String smsJarPath, Integer validityPeriod) {
        boolean result = false;
        try {
            File file = new File(smsJarPath);
            URL url1 = new URL(file.toURI().toURL().toString());
            URLClassLoader myClassLoader = new URLClassLoader(new URL[]{url1}, Thread.currentThread().getContextClassLoader());
            Class<?> smsServiceImpl = myClassLoader.loadClass("com.rzon.service.impl.SmsServiceImpl");
            Method sendVerifyVerificationCode = smsServiceImpl.getDeclaredMethod("sendVerificationCode", String.class, String.class, Integer.class);
            result = (Boolean)sendVerifyVerificationCode.invoke(smsServiceImpl.newInstance(), mobilePhone, code, validityPeriod);
        }
        catch (MalformedURLException e) {
            e.printStackTrace();
        }
        catch (IOException e) {
            e.printStackTrace();
        }
        catch (ClassNotFoundException e) {
            e.printStackTrace();
        }
        catch (NoSuchMethodException e) {
            e.printStackTrace();
        }
        catch (InvocationTargetException e) {
            e.printStackTrace();
        }
        catch (IllegalAccessException e) {
            e.printStackTrace();
        }
        catch (InstantiationException e) {
            e.printStackTrace();
        }
        return result;
    }
}
