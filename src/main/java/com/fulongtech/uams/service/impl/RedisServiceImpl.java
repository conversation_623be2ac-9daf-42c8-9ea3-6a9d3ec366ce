/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  com.alibaba.fastjson.JSONObject
 *  org.springframework.beans.factory.annotation.Autowired
 *  org.springframework.data.redis.core.RedisTemplate
 *  org.springframework.data.redis.core.ValueOperations
 *  org.springframework.stereotype.Service
 */
package com.fulongtech.uams.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.fulongtech.uams.service.RedisService;
import com.fulongtech.uams.sms.VerificationCodeInfo;
import java.util.concurrent.TimeUnit;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Service;

@Service
public class RedisServiceImpl
implements RedisService {
    @Autowired
    private RedisTemplate redisTemplate;

    @Override
    public Boolean setVerificationCodeInfo(String key, VerificationCodeInfo verificationCodeInfo, Integer validityPeriod) {
        ValueOperations vo = this.redisTemplate.opsForValue();
        String toJSONString = JSONObject.toJSONString((Object)verificationCodeInfo);
        vo.set((Object)key, (Object)toJSONString);
        return this.redisTemplate.expire((Object)key, (long)validityPeriod.intValue(), TimeUnit.MINUTES);
    }

    @Override
    public VerificationCodeInfo getVerificationCodeInfo(String key) {
        ValueOperations vo = this.redisTemplate.opsForValue();
        String s = (String)vo.get((Object)key);
        VerificationCodeInfo verificationCodeInfo = (VerificationCodeInfo)JSONObject.parseObject((String)s, VerificationCodeInfo.class);
        return verificationCodeInfo;
    }

    @Override
    public Boolean setIntegerValue(String key, Integer value, Long minutes) {
        ValueOperations vo = this.redisTemplate.opsForValue();
        vo.set((Object)key, (Object)value);
        return this.redisTemplate.expire((Object)key, minutes.longValue(), TimeUnit.MINUTES);
    }

    @Override
    public void deleteValue(String key) {
        this.redisTemplate.delete((Object)key);
    }

    @Override
    public Boolean hasKey(String key) {
        return this.redisTemplate.hasKey((Object)key);
    }

    @Override
    public Integer getIntegerValue(String key) {
        ValueOperations vo = this.redisTemplate.opsForValue();
        return (Integer)vo.get((Object)key);
    }
}
