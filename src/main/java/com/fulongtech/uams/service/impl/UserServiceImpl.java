/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  cn.hutool.core.util.ObjectUtil
 *  cn.hutool.core.util.StrUtil
 *  com.baomidou.mybatisplus.core.conditions.Wrapper
 *  com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper
 *  com.baomidou.mybatisplus.core.conditions.query.QueryWrapper
 *  com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper
 *  com.baomidou.mybatisplus.core.metadata.IPage
 *  com.baomidou.mybatisplus.core.toolkit.support.SFunction
 *  com.baomidou.mybatisplus.extension.plugins.pagination.Page
 *  com.baomidou.mybatisplus.extension.service.impl.ServiceImpl
 *  javax.servlet.http.HttpServletRequest
 *  org.apache.log4j.LogManager
 *  org.apache.log4j.Logger
 *  org.springframework.stereotype.Service
 *  org.springframework.transaction.annotation.Transactional
 *  org.springframework.util.StringUtils
 *  org.springframework.web.multipart.MultipartFile
 */
package com.fulongtech.uams.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fulongtech.uams.excel.UamsExcelFactory;
import com.fulongtech.uams.excel.builder.ExcelReaderBuilder;
import com.fulongtech.uams.exception.UamsException;
import com.fulongtech.uams.mapper.RelationMapper;
import com.fulongtech.uams.mapper.SysUserMapper;
import com.fulongtech.uams.model.ExcelLogType;
import com.fulongtech.uams.model.ExcelUploadLog;
import com.fulongtech.uams.model.Group;
import com.fulongtech.uams.model.SysUser;
import com.fulongtech.uams.model.UserGroupRelation;
import com.fulongtech.uams.service.GroupService;
import com.fulongtech.uams.service.RabbitMqService;
import com.fulongtech.uams.service.RelationService;
import com.fulongtech.uams.service.UserService;
import com.fulongtech.uams.util.SessionUtil;
import java.io.Serializable;
import java.sql.Timestamp;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Comparator;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import org.apache.log4j.LogManager;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

@Service
public class UserServiceImpl
extends ServiceImpl<SysUserMapper, SysUser>
implements UserService {
    @Resource
    private RelationMapper relationMapper;
    @Resource
    private GroupService groupService;
    @Resource
    private RelationService relationService;
    @Resource
    private SessionUtil sessionUtil;
    @Resource
    private RabbitMqService rabbitMqService;
    private static final Logger log = LogManager.getLogger(UserServiceImpl.class);

    @Override
    @Transactional(rollbackFor={Exception.class})
    public synchronized SysUser addUser(SysUser user) {
        ((SysUserMapper)this.getBaseMapper()).insert(user);
        user.setPassword(null);
        return user;
    }

    @Override
    public SysUser updateUser(SysUser user) {
        UpdateWrapper updateWrapper = new UpdateWrapper();
        updateWrapper.lambda().eq(SysUser::getId, (Object)user.getId());
        ((SysUserMapper)this.getBaseMapper()).update(user, (Wrapper)updateWrapper);
        SysUser sysUser = (SysUser)((SysUserMapper)this.getBaseMapper()).selectById((Serializable)((Object)user.getId()));
        HashMap<String, Object> map = new HashMap<String, Object>();
        String createTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        map.put("createTime", createTime);
        map.put("msg", "updateUser");
        map.put("user", sysUser);
        this.rabbitMqService.sendMapMessage(map);
        return (SysUser)((SysUserMapper)this.getBaseMapper()).selectById((Serializable)((Object)user.getId()));
    }

    @Override
    @Transactional(rollbackFor={Exception.class})
    public int delete(final SysUser user) {
        LambdaQueryWrapper relationWrapper = new LambdaQueryWrapper();
        relationWrapper.eq(UserGroupRelation::getUserId, (Object)user.getId());
        this.relationMapper.delete((Wrapper)relationWrapper);
        int count = ((SysUserMapper)this.getBaseMapper()).deleteById((Serializable)((Object)user.getId()));
        if (count > 0) {
            this.sessionUtil.deleteSessionByUsername(user.getUsername());
        }
        this.setMapMessage((Map<String, Object>)new HashMap<String, Object>(8){
            {
                super(x0);
                this.put("msg", "deleteUser");
                this.put("userId", user.getId());
            }
        });
        return count;
    }

    public void setMapMessage(Map<String, Object> map) {
        map.put("createTime", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        this.rabbitMqService.sendMapMessage(map);
    }

    @Override
    public SysUser getById(String id) {
        SysUser sysUser = (SysUser)((SysUserMapper)this.getBaseMapper()).selectById((Serializable)((Object)id));
        return sysUser;
    }

    @Override
    @Transactional(rollbackFor={Exception.class})
    public void changePwd(String id, String userPassword, Timestamp userChangePwdTime) {
        SysUser user = new SysUser();
        user.setId(id);
        user.setPassword(userPassword);
        user.setChangePwdTime(userChangePwdTime);
        ((SysUserMapper)this.getBaseMapper()).updateById(user);
        ((SysUserMapper)this.getBaseMapper()).changePassword(id, userPassword);
    }

    @Override
    public SysUser findByUsername(String userName) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.lambda().eq(SysUser::getUsername, (Object)userName);
        return (SysUser)((SysUserMapper)this.getBaseMapper()).selectOne((Wrapper)queryWrapper);
    }

    @Override
    public IPage<SysUser> search(Page<SysUser> page, String searchText) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.select(new String[]{"id", "USERNAME", "NAME", "EMAIL", "PHONE", "MOBILE_PHONE", "ID_CARD", "CREATE_BY"});
        ((LambdaQueryWrapper)((LambdaQueryWrapper)((LambdaQueryWrapper)queryWrapper.lambda().like(!StringUtils.isEmpty((Object)searchText), SysUser::getUsername, (Object)searchText)).or()).like(!StringUtils.isEmpty((Object)searchText), SysUser::getName, (Object)searchText)).orderByDesc(SysUser::getUsername);
        return ((SysUserMapper)this.getBaseMapper()).selectPage((IPage)page, (Wrapper)queryWrapper);
    }

    @Override
    public List<SysUser> getUserByGroup(String groupId) {
        QueryWrapper relationWrapper = new QueryWrapper();
        relationWrapper.lambda().select(new SFunction[]{UserGroupRelation::getUserId}).eq(UserGroupRelation::getGroupId, (Object)groupId);
        List userIds = this.relationMapper.selectObjs((Wrapper)relationWrapper);
        QueryWrapper userWrapper = new QueryWrapper();
        userWrapper.lambda().in(SysUser::getId, (Collection)userIds);
        return ((SysUserMapper)this.getBaseMapper()).selectList((Wrapper)userWrapper);
    }

    @Override
    public List<SysUser> getAll() {
        return ((SysUserMapper)this.getBaseMapper()).selectList(null);
    }

    @Override
    public List<SysUser> getSysUsersWithGroups(List<SysUser> users, SysUser currentUser) {
        if (!"admin".equals(currentUser.getId())) {
            for (SysUser user : users) {
                if (!"admin".equals(user.getUsername())) continue;
                users.remove(user);
                break;
            }
        }
        return users.stream().sorted(Comparator.comparing(SysUser::getUsername)).sorted(Comparator.comparing(item -> item.getUsername().equals(currentUser.getUsername()) ? 0 : 1)).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor={Exception.class})
    public ExcelUploadLog uploadUsers(MultipartFile file, HttpServletRequest req) {
        Map<Integer, SysUser> users;
        if (!this.validateFileType(file)) {
            ExcelUploadLog uploadLog = new ExcelUploadLog();
            uploadLog.addError(ExcelLogType.TYPE, 0, "\u53ea\u80fd\u4e0a\u4f20xls/xlsx\u6587\u4ef6");
            return uploadLog;
        }
        Instant start = Instant.now();
        ExcelReaderBuilder builder = UamsExcelFactory.readBuilder(file, new ExcelUploadLog());
        try {
            users = builder.read(SysUser.class);
        }
        catch (UamsException e) {
            return builder.getUploadLog();
        }
        System.out.println("\u8bfb\u53d6Excel\u82b1\u8d39\u65f6\u95f4\uff1a" + Duration.between(start, Instant.now()).toMillis());
        Instant start1 = Instant.now();
        ExcelUploadLog excelUploadLog = this.uploadUsers(users, builder.getUploadLog());
        System.out.println("\u4fdd\u5b58\u7528\u6237\u6570\u636e\u82b1\u8d39\u65f6\u95f4\uff1a" + Duration.between(start1, Instant.now()).toMillis());
        return excelUploadLog;
    }

    @Override
    @Transactional(rollbackFor={Exception.class})
    public synchronized ExcelUploadLog uploadUsers(Map<Integer, SysUser> users, ExcelUploadLog uploadLog) {
        List existUsers = ((SysUserMapper)this.getBaseMapper()).selectList(null);
        List<Group> existGroups = this.groupService.getGroups();
        List existRelations = this.relationMapper.selectList(null);
        Set<Integer> keySet = users.keySet();
        Iterator<Integer> keyIterator = keySet.iterator();
        ArrayList<SysUser> insertUsers = new ArrayList<SysUser>();
        ArrayList<SysUser> updateUsers = new ArrayList<SysUser>();
        ArrayList uploadRelations = new ArrayList();
        while (keyIterator.hasNext()) {
            Integer rowIndex = keyIterator.next();
            SysUser uploadUser = users.get(rowIndex);
            boolean isAdd = true;
            for (SysUser oldUser : existUsers) {
                if (!uploadUser.getUsername().equals(oldUser.getUsername())) continue;
                isAdd = false;
                uploadUser.setId(oldUser.getId());
                break;
            }
            List<Group> uploadGroups = uploadUser.getGroups();
            if (isAdd) {
                insertUsers.add(uploadUser);
                uploadUser.setId(UUID.randomUUID().toString().replace("-", ""));
                uploadLog.getInfo().add("\u65b0\u589e\u7528\u6237\uff0c[\u7528\u6237\u540d\uff1a" + uploadUser.getUsername() + " \uff0c \u59d3\u540d\uff1a " + uploadUser.getName() + "] \u6210\u529f");
                uploadGroups = this.filterGroups(uploadLog, existGroups, rowIndex, uploadGroups);
            } else {
                updateUsers.add(uploadUser);
                uploadLog.getInfo().add("\u66f4\u65b0\u7528\u6237\uff0c[\u7528\u6237\u540d\uff1a" + uploadUser.getUsername() + " \uff0c \u59d3\u540d\uff1a " + uploadUser.getName() + "] \u6210\u529f");
                uploadGroups = this.filterGroups(uploadLog, existGroups, rowIndex, uploadGroups);
                List<Group> oldGroups = existRelations.parallelStream().filter(relation -> relation.getUserId().equals(uploadUser.getId())).flatMap(relation -> existGroups.stream().filter(group -> group.getId().equals(relation.getGroupId()))).collect(Collectors.toList());
                this.validateGroupsChange(uploadLog, oldGroups, rowIndex, uploadGroups);
                this.relationService.delRelationByUserIdExceptSys(uploadUser.getId());
            }
            uploadGroups.forEach(group -> {
                UserGroupRelation relation = new UserGroupRelation();
                relation.setUserId(uploadUser.getId());
                relation.setGroupId(group.getId());
                uploadRelations.add(relation);
            });
        }
        if (!insertUsers.isEmpty()) {
            uploadLog.setAddNum(insertUsers.size());
            this.saveBatch(insertUsers);
        }
        if (!updateUsers.isEmpty()) {
            uploadLog.setUpdateNum(updateUsers.size());
            this.updateBatchById(updateUsers);
        }
        if (!uploadRelations.isEmpty()) {
            this.relationService.saveBatch(uploadRelations);
        }
        return uploadLog;
    }

    private void validateGroupsChange(ExcelUploadLog uploadLog, List<Group> oldGroups, Integer rowIndex, List<Group> uploadGroups) {
        block9: {
            block8: {
                if (oldGroups != null) break block8;
                if (uploadGroups == null) break block9;
                uploadLog.addWarn(ExcelLogType.LINE, rowIndex, " \u7528\u6237\u7684\u7528\u6237\u7ec4\u6709\u53d8\u52a8\uff0c\u8bf7\u6ce8\u610f");
                break block9;
            }
            oldGroups = oldGroups.stream().filter(group -> !"\u7cfb\u7edf\u7ba1\u7406\u7ec4".equals(group.getName())).collect(Collectors.toList());
            if (uploadGroups == null && oldGroups.size() > 0) {
                uploadLog.addWarn(ExcelLogType.LINE, rowIndex, "\u7528\u6237\u7684\u7528\u6237\u7ec4\u6709\u53d8\u52a8\uff0c\u8bf7\u6ce8\u610f, \u539f\u6709\u7528\u6237-\u7528\u6237\u7ec4\u5173\u7cfb\u5c06\u88ab\u6e05\u7a7a\uff01");
            } else if (uploadGroups != null && uploadGroups.size() > 0 && oldGroups.size() == 0) {
                uploadLog.addWarn(ExcelLogType.LINE, rowIndex, " \u7528\u6237\u7684\u7528\u6237\u7ec4\u6709\u53d8\u52a8\uff0c\u8bf7\u6ce8\u610f");
            } else if (uploadGroups != null && oldGroups.size() > 0) {
                if (uploadGroups.size() != oldGroups.size()) {
                    uploadLog.addWarn(ExcelLogType.LINE, rowIndex, "\u7528\u6237\u7684\u7528\u6237\u7ec4\u6709\u53d8\u52a8\uff0c\u8bf7\u6ce8\u610f");
                } else {
                    for (int i = 0; i < uploadGroups.size(); ++i) {
                        Group uploadGroup = uploadGroups.get(i);
                        String uploadGroupName = uploadGroup.getName();
                        if (oldGroups.stream().filter(group -> group.getName().equals(uploadGroupName)).count() != 0L) continue;
                        uploadLog.addWarn(ExcelLogType.LINE, rowIndex, "\u7528\u6237\u7684\u7528\u6237\u7ec4\u6709\u53d8\u52a8\uff0c\u8bf7\u6ce8\u610f");
                        break;
                    }
                }
            }
        }
    }

    private List<Group> filterGroups(ExcelUploadLog uploadLog, List<Group> existGroups, Integer rowIndex, List<Group> uploadGroups) {
        ArrayList<Group> res = new ArrayList<Group>();
        for (int i = 0; i < uploadGroups.size(); ++i) {
            String groupName = uploadGroups.get(i).getName();
            Group uploadGroup = null;
            for (int j = 0; j < existGroups.size(); ++j) {
                Group group = existGroups.get(j);
                if (!group.getName().equals(groupName)) continue;
                uploadGroup = group;
                break;
            }
            if (uploadGroup == null) {
                uploadLog.addError(ExcelLogType.LINE, rowIndex, "\u7528\u6237\u7ec4: " + groupName + "\u4e0d\u5b58\u5728\uff0c\u8bf7\u6ce8\u610f");
                continue;
            }
            if ("\u7cfb\u7edf\u7ba1\u7406\u7ec4".equals(uploadGroup.getName())) continue;
            res.add(uploadGroup);
        }
        return res;
    }

    @Override
    public List<SysUser> getSysUsersWithoutGroups() {
        List userIds = this.relationMapper.getAll().stream().map(UserGroupRelation::getUserId).distinct().collect(Collectors.toList());
        List<SysUser> sysUsers = ((SysUserMapper)this.getBaseMapper()).getAll().stream().filter(item -> !userIds.contains(item.getId())).collect(Collectors.toList());
        return sysUsers;
    }

    private boolean validateFileType(MultipartFile file) {
        String fileName = file.getOriginalFilename().toLowerCase();
        return fileName.endsWith("xls") || fileName.endsWith("xlsx");
    }

    @Override
    public List<List<String>> exportUserInfo(List<String> useIdList) {
        LambdaQueryWrapper queryWrapper = (LambdaQueryWrapper)new QueryWrapper().lambda().in(SysUser::getId, useIdList);
        List sysUsers = ((SysUserMapper)this.baseMapper).selectList((Wrapper)queryWrapper);
        ArrayList<List<String>> userInfoList = new ArrayList<List<String>>();
        for (int i = 0; i < sysUsers.size(); ++i) {
            SysUser user = (SysUser)sysUsers.get(i);
            ArrayList<String> object = new ArrayList<String>();
            Integer no = i + 1;
            object.add(no.toString());
            object.add(StrUtil.isNotBlank((CharSequence)user.getUsername()) ? user.getUsername() : " ");
            object.add(StrUtil.isNotBlank((CharSequence)user.getName()) ? user.getName() : " ");
            object.add(StrUtil.isNotBlank((CharSequence)user.getEmail()) ? user.getEmail() : " ");
            object.add(StrUtil.isNotBlank((CharSequence)user.getPhone()) ? user.getPhone() : " ");
            object.add(StrUtil.isNotBlank((CharSequence)user.getMobilePhone()) ? user.getMobilePhone() : " ");
            object.add(StrUtil.isNotBlank((CharSequence)user.getIdCard()) ? user.getIdCard() : " ");
            List<Group> groupByUserId = this.groupService.getGroupByUserId(user.getId());
            ArrayList<String> groupName = new ArrayList<String>();
            if (ObjectUtil.isNotNull(groupByUserId)) {
                for (Group group : groupByUserId) {
                    groupName.add(group.getName());
                }
            } else {
                groupName.add(" ");
            }
            object.add(StrUtil.strip((CharSequence)((Object)groupName).toString(), (CharSequence)"[", (CharSequence)"]"));
            userInfoList.add(object);
        }
        return userInfoList;
    }

    @Override
    public boolean mobilePhoneNotBindedByUser(String mobilePhone) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.lambda().eq(SysUser::getMobilePhone, (Object)mobilePhone);
        Integer selectCount = ((SysUserMapper)this.getBaseMapper()).selectCount((Wrapper)queryWrapper);
        return selectCount.equals(0);
    }

    @Override
    public Integer mobilePhoneNotBindedByUserNumber(String mobilePhone) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.lambda().eq(SysUser::getMobilePhone, (Object)mobilePhone);
        Integer selectCount = ((SysUserMapper)this.getBaseMapper()).selectCount((Wrapper)queryWrapper);
        return selectCount;
    }

    @Override
    public SysUser findByMobilePhone(String mobilePhone) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.lambda().eq(SysUser::getMobilePhone, (Object)mobilePhone);
        return (SysUser)((SysUserMapper)this.getBaseMapper()).selectOne((Wrapper)queryWrapper);
    }

    @Override
    public List<SysUser> findUserListByMobilePhone(String mobilePhone) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.lambda().eq(SysUser::getMobilePhone, (Object)mobilePhone);
        return ((SysUserMapper)this.getBaseMapper()).selectList((Wrapper)queryWrapper);
    }
}
