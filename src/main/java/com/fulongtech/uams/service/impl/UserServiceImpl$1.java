/*
 * Decompiled with CFR 0.152.
 */
package com.fulongtech.uams.service.impl;

import com.fulongtech.uams.model.SysUser;
import java.util.HashMap;

class UserServiceImpl.1
extends HashMap<String, Object> {
    final /* synthetic */ SysUser val$user;

    UserServiceImpl.1(int x0, SysUser sysUser) {
        this.val$user = sysUser;
        super(x0);
        this.put("msg", "deleteUser");
        this.put("userId", this.val$user.getId());
    }
}
