/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  cn.hutool.core.bean.BeanUtil
 *  cn.hutool.core.collection.CollectionUtil
 *  cn.hutool.core.date.DateTime
 *  cn.hutool.core.date.DateUtil
 *  cn.hutool.core.util.ObjectUtil
 *  cn.hutool.core.util.StrUtil
 *  com.baomidou.mybatisplus.core.conditions.Wrapper
 *  com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper
 *  com.baomidou.mybatisplus.core.metadata.IPage
 *  com.baomidou.mybatisplus.extension.plugins.pagination.Page
 *  com.baomidou.mybatisplus.extension.service.impl.ServiceImpl
 *  org.springframework.stereotype.Service
 *  org.springframework.transaction.annotation.Transactional
 */
package com.fulongtech.uams.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fulongtech.uams.exception.ErrorCode;
import com.fulongtech.uams.exception.UamsException;
import com.fulongtech.uams.mapper.EventMapper;
import com.fulongtech.uams.mapper.GroupMapper;
import com.fulongtech.uams.mapper.SysUserMapper;
import com.fulongtech.uams.model.Event;
import com.fulongtech.uams.model.Group;
import com.fulongtech.uams.model.SysUser;
import com.fulongtech.uams.service.EventService;
import com.fulongtech.uams.service.GroupService;
import com.fulongtech.uams.vo.EventPagesVo;
import com.fulongtech.uams.vo.EventVO;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class EventServiceImpl
extends ServiceImpl<EventMapper, Event>
implements EventService {
    @Resource
    private SysUserMapper sysUserMapper;
    @Resource
    private GroupService groupService;
    @Resource
    private GroupMapper groupMapper;
    @Resource
    private EventMapper eventMapper;

    @Override
    @Transactional(rollbackFor={Exception.class})
    public void addEvent(String eventType, SysUser user, String ip) {
        String userId = user.getId();
        List<Group> groups = user.getGroups();
        String s = null;
        if (groups != null || !CollectionUtil.isEmpty(groups)) {
            List groupId = groups.stream().map(Group::getId).collect(Collectors.toList());
            s = String.join((CharSequence)",", groupId);
        }
        if (StrUtil.isEmpty((CharSequence)eventType)) {
            throw new UamsException(ErrorCode.UNPROCESABLE_ENTITY, "\u4e8b\u4ef6\u7c7b\u578b\u4e0d\u80fd\u4e3a\u7a7a");
        }
        if (StrUtil.isEmpty((CharSequence)userId)) {
            throw new UamsException(ErrorCode.UNPROCESABLE_ENTITY, "\u7528\u6237ID\u4e0d\u80fd\u4e3a\u7a7a");
        }
        if (StrUtil.isEmpty((CharSequence)ip)) {
            throw new UamsException(ErrorCode.UNPROCESABLE_ENTITY, "\u767b\u5f55IP\u4e0d\u80fd\u4e3a\u7a7a");
        }
        Event event = new Event();
        event.setEventType(eventType);
        event.setUserId(userId);
        event.setGroupId(s);
        event.setIp(ip);
        event.setEventTime(DateUtil.date().toTimestamp());
        ((EventMapper)this.baseMapper).insert(event);
    }

    @Override
    public EventPagesVo searchByParams(String type, String userId, String groupId, String startTime, String endTime, Integer pageNO, Integer pageSize) {
        LambdaQueryWrapper lambdaQueryWrapper = new LambdaQueryWrapper();
        lambdaQueryWrapper.eq(Event::getEventType, (Object)type);
        if (!StrUtil.isEmpty((CharSequence)userId)) {
            lambdaQueryWrapper.eq(Event::getUserId, (Object)userId);
        }
        if (!StrUtil.isEmpty((CharSequence)groupId)) {
            List<SysUser> groupUser = this.groupService.getGroupUser(groupId);
            List<Group> groupChildren = this.groupService.getGroupChildren(groupId);
            if (!StrUtil.isEmpty((CharSequence)groupId)) {
                for (Group group : groupChildren) {
                    List<SysUser> groupUserBYGroupChildren = this.groupService.getGroupUser(group.getId());
                    for (SysUser user : groupUserBYGroupChildren) {
                        if (groupUser.indexOf(user) != -1) continue;
                        groupUser.add(user);
                    }
                }
                ArrayList<String> userIdList = new ArrayList<String>();
                for (SysUser sysUser : groupUser) {
                    userIdList.add(sysUser.getId());
                }
                lambdaQueryWrapper.in(Event::getUserId, userIdList);
            }
        }
        DateTime st = DateUtil.lastMonth();
        DateTime et = DateUtil.date();
        if (!StrUtil.isEmpty((CharSequence)startTime)) {
            st = DateUtil.parse((CharSequence)startTime);
        }
        if (!StrUtil.isEmpty((CharSequence)endTime)) {
            et = DateUtil.parse((CharSequence)endTime);
        }
        lambdaQueryWrapper.between(Event::getEventTime, (Object)st, (Object)et);
        lambdaQueryWrapper.orderByDesc(Event::getEventTime);
        Page page = new Page((long)pageNO.intValue(), (long)pageSize.intValue());
        IPage<Event> iPage = this.selectEventPage((Page<Event>)page, (Wrapper<Event>)lambdaQueryWrapper);
        List events = iPage.getRecords();
        if (events.size() == 0) {
            throw new UamsException(ErrorCode.RESOURCE_NOT_FOUND, "\u6839\u636e\u67e5\u8be2\u6761\u4ef6\u67e5\u5230\u7684\u767b\u5f55\u65e5\u5fd7\u4e3a\u7a7a");
        }
        List<EventVO> eventVOList = events.stream().map(event -> (EventVO)BeanUtil.copyProperties((Object)event, EventVO.class, (String[])new String[0])).collect(Collectors.toList());
        Set<String> userIds = events.stream().map(Event::getUserId).collect(Collectors.toSet());
        List<SysUser> userList = this.getUserByIds(userIds);
        List<Group> groupList = this.groupService.getGroups();
        HashMap<String, Group> hashMap = new HashMap<String, Group>();
        for (Group group : groupList) {
            hashMap.put(group.getId(), group);
        }
        for (SysUser user : userList) {
            eventVOList.stream().filter(eventVO -> eventVO.getUserId().equals(user.getId())).forEach(eventVO -> {
                Event event = (Event)this.eventMapper.selectById((Serializable)((Object)eventVO.getId()));
                String eventGroupIds = event.getGroupId();
                if (StrUtil.isEmpty((CharSequence)eventGroupIds)) {
                    eventVO.setGroups(null);
                } else {
                    String[] split = eventGroupIds.split(",");
                    ArrayList<Group> groups = new ArrayList<Group>();
                    for (int i = 0; i < split.length; ++i) {
                        Group group = (Group)hashMap.get(split[i]);
                        if (!ObjectUtil.isNotEmpty((Object)group)) continue;
                        groups.add(group);
                    }
                    eventVO.setGroups(groups);
                }
                eventVO.setUserName(user.getUsername());
                eventVO.setName(user.getName());
            });
        }
        EventPagesVo eventPagesVo = new EventPagesVo();
        eventPagesVo.seteventVOList(eventVOList);
        eventPagesVo.setSize(iPage.getSize());
        eventPagesVo.setTotal(iPage.getTotal());
        eventPagesVo.setCurrent(iPage.getCurrent());
        return eventPagesVo;
    }

    private List<SysUser> getUserByIds(Set<String> userIds) {
        LambdaQueryWrapper sysUserLambdaQueryWrapper = new LambdaQueryWrapper();
        sysUserLambdaQueryWrapper.in(SysUser::getId, userIds);
        List userList = this.sysUserMapper.selectList((Wrapper)sysUserLambdaQueryWrapper);
        return userList;
    }

    @Override
    public IPage<Event> selectEventPage(Page<Event> page, Wrapper<Event> queryWrapper) {
        return this.eventMapper.selectPage((IPage)page, (Wrapper)queryWrapper);
    }
}
