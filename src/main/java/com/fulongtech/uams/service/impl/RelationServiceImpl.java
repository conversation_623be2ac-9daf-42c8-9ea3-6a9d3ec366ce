/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  cn.hutool.core.util.ObjectUtil
 *  com.baomidou.mybatisplus.core.conditions.Wrapper
 *  com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper
 *  com.baomidou.mybatisplus.core.conditions.query.QueryWrapper
 *  com.baomidou.mybatisplus.core.toolkit.Wrappers
 *  com.baomidou.mybatisplus.core.toolkit.support.SFunction
 *  com.baomidou.mybatisplus.extension.service.impl.ServiceImpl
 *  org.springframework.stereotype.Service
 *  org.springframework.transaction.annotation.Transactional
 */
package com.fulongtech.uams.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fulongtech.uams.mapper.GroupMapper;
import com.fulongtech.uams.mapper.RelationMapper;
import com.fulongtech.uams.mapper.SysUserMapper;
import com.fulongtech.uams.model.Group;
import com.fulongtech.uams.model.UserGroupRelation;
import com.fulongtech.uams.service.RelationService;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.ListIterator;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class RelationServiceImpl
extends ServiceImpl<RelationMapper, UserGroupRelation>
implements RelationService {
    @Resource
    private SysUserMapper userMapper;
    @Resource
    private RelationService relationService;
    @Resource
    private GroupMapper groupMapper;
    private static final String SYSTEM_GROUP_ID = "BEFE2FF1A20F4C419C8A94B7213C5218";
    private static final String ADMIN_ID = "BEFE2FF1A20F4C419C8A94B7213C5219";
    private static final String TRUE = "true";

    @Override
    public void delRelationByUserId(String userId) {
        QueryWrapper wrapper = new QueryWrapper();
        wrapper.lambda().eq(UserGroupRelation::getUserId, (Object)userId);
        ((RelationMapper)this.getBaseMapper()).delete((Wrapper)wrapper);
    }

    @Override
    public void delRelationByUserIdExceptSys(String userId) {
        QueryWrapper wrapper = new QueryWrapper();
        ((LambdaQueryWrapper)wrapper.lambda().eq(UserGroupRelation::getUserId, (Object)userId)).ne(UserGroupRelation::getGroupId, (Object)SYSTEM_GROUP_ID);
        ((RelationMapper)this.getBaseMapper()).delete((Wrapper)wrapper);
    }

    @Override
    @Transactional(rollbackFor={Exception.class})
    public void addRelationBatch(String userId, List<String> groupIds) {
        for (int i = 0; i < groupIds.size(); ++i) {
            UserGroupRelation relation = new UserGroupRelation();
            relation.setGroupId(groupIds.get(i));
            relation.setUserId(userId);
            ((RelationMapper)this.getBaseMapper()).insert(relation);
        }
    }

    @Override
    @Transactional(rollbackFor={Exception.class})
    public void deleteRelationByGroupIdAndUserId(String groupId, String userId) {
        HashMap<Object, String> params = new HashMap<Object, String>(5);
        params.put(UserGroupRelation::getUserId, userId);
        params.put(UserGroupRelation::getGroupId, groupId);
        ((RelationMapper)this.getBaseMapper()).delete((Wrapper)Wrappers.lambdaQuery().allEq(true, params, true));
    }

    @Override
    @Transactional(rollbackFor={Exception.class})
    public void addRelations(String group, List<String> userIds) {
        for (int i = 0; i < userIds.size(); ++i) {
            UserGroupRelation relation = new UserGroupRelation();
            relation.setGroupId(group);
            relation.setUserId(userIds.get(i));
            ((RelationMapper)this.getBaseMapper()).insert(relation);
        }
    }

    @Override
    public List<String> getManagedGroupsBYUserId(String userId) {
        QueryWrapper wrapper = new QueryWrapper();
        ((LambdaQueryWrapper)wrapper.lambda().select(new SFunction[]{UserGroupRelation::getGroupId}).eq(UserGroupRelation::getUserId, (Object)userId)).eq(UserGroupRelation::getIsGroupManage, (Object)TRUE);
        List objects = ((RelationMapper)this.getBaseMapper()).selectObjs((Wrapper)wrapper);
        ListIterator objectListIterator = objects.listIterator();
        ArrayList<String> managedGroups = new ArrayList<String>();
        while (objectListIterator.hasNext()) {
            managedGroups.add(objectListIterator.next().toString());
        }
        return managedGroups;
    }

    @Override
    public boolean checkGroupPermissions(String userId, List<UserGroupRelation> userGroupRelationList) {
        List<String> managedGroupsBYUserId = this.getManagedGroupsBYUserId(userId);
        if (managedGroupsBYUserId.size() == 0) {
            return false;
        }
        for (UserGroupRelation userGroupRelation : userGroupRelationList) {
            boolean contains = managedGroupsBYUserId.contains(userGroupRelation.getGroupId());
            if (contains) continue;
            return false;
        }
        return true;
    }

    @Override
    public boolean upDateGroupRelation(String userId, List<UserGroupRelation> userGroupRelationList) {
        QueryWrapper wrapper = new QueryWrapper();
        wrapper.lambda().eq(UserGroupRelation::getUserId, (Object)userId);
        ArrayList userGroupRelationsByDb = (ArrayList)((RelationMapper)this.getBaseMapper()).selectList((Wrapper)wrapper);
        ListIterator<UserGroupRelation> userGroupRelationListIterator = userGroupRelationList.listIterator();
        HashMap<String, String> groupParamModelHashMap = new HashMap<String, String>();
        ArrayList<String> groupIdList = new ArrayList<String>();
        while (userGroupRelationListIterator.hasNext()) {
            UserGroupRelation userGroupRelation = userGroupRelationListIterator.next();
            if (userGroupRelation.getIsGroupManage().equals(TRUE)) {
                groupIdList.add(userGroupRelation.getGroupId());
            }
            groupParamModelHashMap.put(userGroupRelation.getGroupId(), userGroupRelation.getIsGroupManage());
        }
        List groupList = this.groupMapper.selectList(null);
        for (Group group : groupList) {
            String aBoolean = (String)groupParamModelHashMap.get(group.getParentId());
            if (!ObjectUtil.isNotNull((Object)aBoolean) || !aBoolean.equals(TRUE)) continue;
            groupParamModelHashMap.put(group.getId(), TRUE);
        }
        ArrayList<UserGroupRelation> userGroupRelationArrayList = new ArrayList<UserGroupRelation>();
        for (UserGroupRelation userGroupRelation : userGroupRelationsByDb) {
            String aBoolean = (String)groupParamModelHashMap.get(userGroupRelation.getGroupId());
            if (!ObjectUtil.isNotNull((Object)aBoolean) || !aBoolean.equals(TRUE) || userGroupRelation.getIsGroupManage() != null && userGroupRelation.getIsGroupManage().equals(aBoolean)) continue;
            userGroupRelation.setIsGroupManage(aBoolean);
            userGroupRelationArrayList.add(userGroupRelation);
        }
        if (userGroupRelationArrayList.size() > 0) {
            return this.relationService.updateBatchById(userGroupRelationsByDb);
        }
        return true;
    }
}
