/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  com.alibaba.fastjson.JSONObject
 *  org.slf4j.Logger
 *  org.slf4j.LoggerFactory
 *  org.springframework.amqp.AmqpException
 *  org.springframework.amqp.rabbit.core.RabbitTemplate
 *  org.springframework.scheduling.annotation.Async
 *  org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor
 *  org.springframework.stereotype.Component
 */
package com.fulongtech.uams.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.fulongtech.uams.service.RabbitMqService;
import java.util.Map;
import javax.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.AmqpException;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

@Component
public class RabbitMqServiceImpl
implements RabbitMqService {
    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    @Resource
    public RabbitTemplate rabbitTemplate;
    @Resource
    private ThreadPoolTaskExecutor rabbitMqExecutor;

    @Override
    @Async
    public void sendMapMessage(final Map<String, Object> msg) {
        this.rabbitMqExecutor.execute(new Runnable(){

            @Override
            public void run() {
                try {
                    JSONObject jsonObject = new JSONObject(msg);
                    RabbitMqServiceImpl.this.rabbitTemplate.convertAndSend("UAMS_Exchange", "UAMS", (Object)jsonObject.toJSONString());
                }
                catch (AmqpException e) {
                    RabbitMqServiceImpl.this.logger.warn(e.getMessage());
                }
            }
        });
    }
}
