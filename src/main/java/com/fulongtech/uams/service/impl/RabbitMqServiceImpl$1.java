/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  com.alibaba.fastjson.JSONObject
 *  org.springframework.amqp.AmqpException
 */
package com.fulongtech.uams.service.impl;

import com.alibaba.fastjson.JSONObject;
import java.util.Map;
import org.springframework.amqp.AmqpException;

class RabbitMqServiceImpl.1
implements Runnable {
    final /* synthetic */ Map val$msg;

    RabbitMqServiceImpl.1(Map map) {
        this.val$msg = map;
    }

    @Override
    public void run() {
        try {
            JSONObject jsonObject = new JSONObject(this.val$msg);
            RabbitMqServiceImpl.this.rabbitTemplate.convertAndSend("UAMS_Exchange", "UAMS", (Object)jsonObject.toJSONString());
        }
        catch (AmqpException e) {
            RabbitMqServiceImpl.this.logger.warn(e.getMessage());
        }
    }
}
