/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  com.baomidou.mybatisplus.core.conditions.Wrapper
 *  com.baomidou.mybatisplus.core.conditions.query.QueryWrapper
 *  com.baomidou.mybatisplus.core.toolkit.support.SFunction
 *  com.baomidou.mybatisplus.extension.service.impl.ServiceImpl
 *  com.google.common.collect.Lists
 *  org.springframework.stereotype.Service
 *  org.springframework.transaction.annotation.Transactional
 */
package com.fulongtech.uams.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fulongtech.uams.mapper.GroupMapper;
import com.fulongtech.uams.mapper.RelationMapper;
import com.fulongtech.uams.mapper.SysUserMapper;
import com.fulongtech.uams.model.Group;
import com.fulongtech.uams.model.SysUser;
import com.fulongtech.uams.model.UserGroupRelation;
import com.fulongtech.uams.service.GroupService;
import com.fulongtech.uams.service.RabbitMqService;
import com.fulongtech.uams.service.RelationService;
import com.google.common.collect.Lists;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayDeque;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.ListIterator;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class GroupServiceImpl
extends ServiceImpl<GroupMapper, Group>
implements GroupService {
    @Resource
    private GroupMapper groupMapper;
    @Resource
    private RelationMapper relationMapper;
    @Resource
    private SysUserMapper sysUserMapper;
    @Resource
    private RelationService relationService;
    @Resource
    private GroupService groupService;
    @Resource
    private RabbitMqService rabbitMqService;
    private static final String SYSTEM_GROUP_ID = "BEFE2FF1A20F4C419C8A94B7213C5218";

    @Override
    public List<Group> getGroupByUserId(String userId) {
        QueryWrapper relationQueryWrapper = new QueryWrapper();
        relationQueryWrapper.lambda().eq(UserGroupRelation::getUserId, (Object)userId);
        List userGroupRelationList = this.relationMapper.selectList((Wrapper)relationQueryWrapper);
        ListIterator userGroupRelationListIterator = userGroupRelationList.listIterator();
        ArrayList<String> groupIds = new ArrayList<String>();
        while (userGroupRelationListIterator.hasNext()) {
            UserGroupRelation userGroupRelation = (UserGroupRelation)userGroupRelationListIterator.next();
            groupIds.add(userGroupRelation.getGroupId());
        }
        if (!groupIds.isEmpty()) {
            QueryWrapper groupQueryWrapper = new QueryWrapper();
            groupQueryWrapper.lambda().in(Group::getId, groupIds);
            List groups = this.groupMapper.selectList((Wrapper)groupQueryWrapper);
            return groups;
        }
        return null;
    }

    @Override
    public Group add(Group group) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.lambda().eq(Group::getName, (Object)group.getName());
        this.groupMapper.insert(group);
        return (Group)this.groupMapper.selectOne((Wrapper)queryWrapper);
    }

    @Override
    public int deleteById(String id) {
        return 0;
    }

    @Override
    public List<SysUser> getUsersByGroupName(String groupName) {
        QueryWrapper groupQueryWrapper = new QueryWrapper();
        groupQueryWrapper.lambda().select(new SFunction[]{Group::getId}).like(Group::getName, (Object)groupName);
        List groupIds = this.groupMapper.selectObjs((Wrapper)groupQueryWrapper);
        if (!groupIds.isEmpty()) {
            QueryWrapper relationQueryWrapper = new QueryWrapper();
            relationQueryWrapper.lambda().select(new SFunction[]{UserGroupRelation::getUserId}).in(UserGroupRelation::getGroupId, (Collection)groupIds);
            List userIds = this.relationMapper.selectObjs((Wrapper)relationQueryWrapper);
            if (!userIds.isEmpty()) {
                QueryWrapper userWrapper = new QueryWrapper();
                userWrapper.select(new String[]{"id", "USERNAME", "NAME", "EMAIL", "PHONE", "MOBILE_PHONE", "ID_CARD", "CREATE_BY"});
                userWrapper.lambda().in(SysUser::getId, (Collection)userIds);
                return this.sysUserMapper.selectList((Wrapper)userWrapper);
            }
        }
        return null;
    }

    @Override
    public List<Group> getGroups() {
        return this.groupMapper.selectList(null);
    }

    @Override
    @Transactional(rollbackFor={Exception.class})
    public boolean deleteGroups(List<String> groupId) {
        HashSet<String> groupIds = new HashSet<String>(groupId);
        ArrayDeque<String> queue = new ArrayDeque<String>();
        for (String id : groupId) {
            queue.offer(id);
        }
        QueryWrapper queryWrapper = new QueryWrapper();
        while (queue.size() > 0) {
            String id;
            id = (String)queue.poll();
            queryWrapper.lambda().clear();
            queryWrapper.lambda().eq(Group::getParentId, (Object)id);
            List collectId = this.groupMapper.selectList((Wrapper)queryWrapper).stream().map(item -> item.getId()).collect(Collectors.toList());
            for (String tempId : collectId) {
                groupIds.add(tempId);
                queue.offer(tempId);
            }
        }
        int i = this.groupMapper.deleteBatchIds(groupIds);
        this.deleteGroupFromRelation(groupIds);
        HashMap<String, Object> map = new HashMap<String, Object>();
        String createTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        map.put("createTime", createTime);
        map.put("msg", "deleteGroup");
        map.put("groupId", groupId);
        this.rabbitMqService.sendMapMessage(map);
        return i > 0;
    }

    @Override
    public void setGroupsForUser(Map<String, SysUser> users) {
        List allRelations = this.relationService.getBaseMapper().selectList(null).stream().sorted(Comparator.comparing(UserGroupRelation::getUserId)).collect(Collectors.toList());
        Map allGroups = this.groupService.getGroups().stream().collect(Collectors.toMap(Group::getId, Function.identity()));
        for (UserGroupRelation userGroupRelation : allRelations) {
            String groupId;
            SysUser sysUser = users.get(userGroupRelation.getUserId());
            if (sysUser == null) continue;
            if (userGroupRelation.getIsGroupManage() != null && userGroupRelation.getIsGroupManage().equals("true")) {
                sysUser.addGroupIdToManagedGroups(userGroupRelation.getGroupId());
            }
            if (!sysUser.getId().equals(userGroupRelation.getUserId()) || !allGroups.containsKey(groupId = userGroupRelation.getGroupId())) continue;
            if (sysUser.getGroups() == null) {
                ArrayList<Group> groups = new ArrayList<Group>();
                groups.add((Group)allGroups.get(groupId));
                sysUser.setGroups(groups);
                continue;
            }
            sysUser.getGroups().add((Group)allGroups.get(groupId));
        }
    }

    @Override
    public List<SysUser> getUsersByGroupIds(String[] ids) {
        QueryWrapper relationQueryWrapper = new QueryWrapper();
        relationQueryWrapper.lambda().in(UserGroupRelation::getGroupId, Arrays.stream(ids).toArray());
        Set userIds = this.relationMapper.selectList((Wrapper)relationQueryWrapper).stream().map(UserGroupRelation::getUserId).collect(Collectors.toSet());
        if (userIds != null && userIds.size() > 0) {
            ArrayList<SysUser> res = new ArrayList<SysUser>();
            ArrayList userIdList = new ArrayList(userIds);
            List parts = Lists.partition(userIdList, (int)1000);
            parts.parallelStream().forEach(list -> res.addAll(this.sysUserMapper.selectBatchIds((Collection)list)));
            this.setGroupsForUser(res.stream().collect(Collectors.toMap(SysUser::getId, Function.identity())));
            return res;
        }
        return new ArrayList<SysUser>(0);
    }

    private int deleteGroupFromRelation(Set<String> groupIds) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.lambda().in(UserGroupRelation::getGroupId, groupIds);
        return this.relationMapper.delete((Wrapper)queryWrapper);
    }

    @Override
    public List<SysUser> getGroupUser(String groupId) {
        QueryWrapper relationQueryWrapper = new QueryWrapper();
        relationQueryWrapper.lambda().eq(UserGroupRelation::getGroupId, (Object)groupId);
        List userIds = this.relationMapper.selectList((Wrapper)relationQueryWrapper).stream().map(UserGroupRelation::getUserId).collect(Collectors.toList());
        if (userIds != null && userIds.size() > 0) {
            ArrayList<SysUser> res = new ArrayList<SysUser>();
            List parts = Lists.partition(userIds, (int)1000);
            parts.stream().forEach(list -> res.addAll(this.sysUserMapper.selectBatchIds((Collection)list)));
            this.setGroupsForUser(res.stream().collect(Collectors.toMap(SysUser::getId, Function.identity())));
            return res;
        }
        return new ArrayList<SysUser>(0);
    }

    @Override
    public Group updateGroupInfo(Group group) {
        this.groupMapper.updateById(group);
        HashMap<String, Object> map = new HashMap<String, Object>();
        String createTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        map.put("createTime", createTime);
        map.put("msg", "updateGroup");
        map.put("groupId", group.getId());
        this.rabbitMqService.sendMapMessage(map);
        return (Group)this.groupMapper.selectById((Serializable)((Object)group.getId()));
    }

    @Override
    public Group getGroupById(String groupId) {
        return (Group)this.groupMapper.selectById((Serializable)((Object)groupId));
    }

    @Override
    public List<Group> getGroupChildren(String groupId) {
        ArrayList<Group> groups = new ArrayList<Group>(50);
        ArrayDeque<String> groupIdQueue = new ArrayDeque<String>();
        groupIdQueue.offer(groupId);
        QueryWrapper groupQueryWrapper = new QueryWrapper();
        while (!groupIdQueue.isEmpty()) {
            String id = (String)groupIdQueue.poll();
            groupQueryWrapper.lambda().clear();
            groupQueryWrapper.lambda().eq(Group::getParentId, (Object)id);
            List groupList = this.groupMapper.selectList((Wrapper)groupQueryWrapper);
            if (groupList == null || groupList.size() <= 0) continue;
            for (int i = 0; i < groupList.size(); ++i) {
                groups.add((Group)groupList.get(i));
                groupIdQueue.offer(((Group)groupList.get(i)).getId());
            }
        }
        return groups;
    }

    @Override
    public Group getGroupByName(String groupName) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.lambda().eq(Group::getName, (Object)groupName);
        return (Group)this.groupMapper.selectOne((Wrapper)queryWrapper);
    }
}
