/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  cn.hutool.core.date.DateTime
 *  cn.hutool.core.date.DateUtil
 *  cn.hutool.core.util.StrUtil
 *  com.baomidou.mybatisplus.core.conditions.Wrapper
 *  com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper
 *  com.baomidou.mybatisplus.core.toolkit.Wrappers
 *  com.baomidou.mybatisplus.extension.service.impl.ServiceImpl
 *  org.springframework.stereotype.Service
 */
package com.fulongtech.uams.service.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fulongtech.uams.mapper.EventMapper;
import com.fulongtech.uams.mapper.GroupMapper;
import com.fulongtech.uams.mapper.RelationMapper;
import com.fulongtech.uams.model.Event;
import com.fulongtech.uams.model.Group;
import com.fulongtech.uams.model.UserGroupRelation;
import com.fulongtech.uams.service.GroupService;
import com.fulongtech.uams.service.StatisticalService;
import com.fulongtech.uams.util.MybatisplusParameterUtils;
import com.fulongtech.uams.vo.LoginRat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

@Service
public class StatisticalServiceImpl
extends ServiceImpl<EventMapper, Event>
implements StatisticalService {
    @Resource
    private EventMapper eventMapper;
    @Resource
    private GroupService groupService;
    @Resource
    private GroupMapper groupMapper;
    @Resource
    private RelationMapper relationMapper;

    @Override
    public List<LoginRat> getLoginRat(String groupIds, String startTime, String endTime) {
        Object[] groupIdsArr = groupIds.split(",");
        ArrayList<LoginRat> loginRats = new ArrayList<LoginRat>();
        DateTime st = DateUtil.lastMonth();
        DateTime et = DateUtil.date();
        if (!StrUtil.isEmpty((CharSequence)startTime)) {
            st = DateUtil.parse((CharSequence)startTime);
        }
        if (!StrUtil.isEmpty((CharSequence)endTime)) {
            et = DateUtil.parse((CharSequence)endTime);
        }
        try {
            List groups = this.groupMapper.selectList((Wrapper)Wrappers.lambdaQuery().in(Group::getId, groupIdsArr));
            for (Group group : groups) {
                String groupId = group.getId();
                if (group == null) continue;
                LambdaQueryWrapper lambdaQueryWrapper = new LambdaQueryWrapper();
                lambdaQueryWrapper.between(Event::getEventTime, (Object)st, (Object)et);
                LoginRat loginRat = new LoginRat();
                loginRat.setGroupId(groupId);
                loginRat.setGroupName(group.getName());
                List<Group> groupChildren = this.groupService.getGroupChildren(groupId);
                Set groupIdSet = groupChildren.stream().map(Group::getId).collect(Collectors.toSet());
                groupIdSet.add(groupId);
                LambdaQueryWrapper queryWrapper = Wrappers.lambdaQuery();
                MybatisplusParameterUtils.cutInParameter(queryWrapper, UserGroupRelation::getGroupId, Arrays.asList(groupIdSet.toArray()));
                List relationList = this.relationMapper.selectList((Wrapper)queryWrapper);
                Set userIds = relationList.stream().map(UserGroupRelation::getUserId).collect(Collectors.toSet());
                int accountNum = userIds.size();
                loginRat.setAccountNum(accountNum);
                if (userIds.isEmpty() || userIds.size() <= 0) continue;
                lambdaQueryWrapper.eq(Event::getEventType, (Object)"LOGIN");
                MybatisplusParameterUtils.cutInParameter(lambdaQueryWrapper, Event::getUserId, Arrays.asList(userIds.toArray()));
                List eventList = this.eventMapper.selectList((Wrapper)lambdaQueryWrapper);
                int loginNum = eventList.size();
                loginRat.setLoginNum(loginNum);
                Set eventIds = eventList.stream().map(Event::getUserId).collect(Collectors.toSet());
                int loginAccountNum = eventIds.size();
                loginRat.setLoginAccountNum(loginAccountNum);
                double rat = (double)loginAccountNum / (double)accountNum;
                loginRat.setLoginRat(rat);
                loginRats.add(loginRat);
            }
        }
        catch (Exception e) {
            e.printStackTrace();
        }
        return loginRats;
    }
}
