/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.springframework.boot.context.properties.ConfigurationProperties
 *  org.springframework.stereotype.Component
 */
package com.fulongtech.uams.properties;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@ConfigurationProperties(prefix="uams.user-information")
@Component
public class UserInformationProper {
    private String requiredIdCard;

    public String getRequiredIdCard() {
        return this.requiredIdCard;
    }

    public void setRequiredIdCard(String requiredIdCard) {
        this.requiredIdCard = requiredIdCard;
    }
}
