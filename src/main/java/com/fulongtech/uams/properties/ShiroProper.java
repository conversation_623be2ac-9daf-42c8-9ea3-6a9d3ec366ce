/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.springframework.stereotype.Component
 *  org.springframework.util.ResourceUtils
 *  org.yaml.snakeyaml.Yaml
 */
package com.fulongtech.uams.properties;

import java.io.File;
import java.io.FileNotFoundException;
import org.springframework.stereotype.Component;
import org.springframework.util.ResourceUtils;
import org.yaml.snakeyaml.Yaml;

@Component
public class ShiroProper {
    private String filter;
    private int sessionValidationInterval;
    private int globalSessionTimeout;
    private int sessionIdCookieMaxAge;
    private int rememberMeCookieMaxAge;
    private int kickoutSessionControlFilterMaxSession;
    private String kickoutUrl;
    private String loginUrl;
    private String successUrl;
    private String unauthorizedUrl;
    private boolean kickoutAfter;

    public static ShiroProper getInstance() {
        Yaml yaml = new Yaml();
        ShiroProper shiroProper = new ShiroProper();
        shiroProper = (ShiroProper)yaml.loadAs(ShiroProper.class.getResourceAsStream("/config/shiroProper.yml"), ShiroProper.class);
        return shiroProper;
    }

    public static File getApplicationConfig() {
        File file = null;
        try {
            file = ResourceUtils.getFile((String)"classpath:config/application.yml");
        }
        catch (FileNotFoundException e) {
            e.printStackTrace();
        }
        return file;
    }

    public String getFilter() {
        return this.filter;
    }

    public void setFilter(String filter) {
        this.filter = filter.replaceAll(";", System.getProperty("line.separator"));
    }

    public int getSessionValidationInterval() {
        return this.sessionValidationInterval;
    }

    public void setSessionValidationInterval(int sessionValidationInterval) {
        this.sessionValidationInterval = sessionValidationInterval;
    }

    public int getGlobalSessionTimeout() {
        return this.globalSessionTimeout;
    }

    public void setGlobalSessionTimeout(int globalSessionTimeout) {
        this.globalSessionTimeout = globalSessionTimeout;
    }

    public int getRememberMeCookieMaxAge() {
        return this.rememberMeCookieMaxAge;
    }

    public void setRememberMeCookieMaxAge(int rememberMeCookieMaxAge) {
        this.rememberMeCookieMaxAge = rememberMeCookieMaxAge;
    }

    public int getKickoutSessionControlFilterMaxSession() {
        return this.kickoutSessionControlFilterMaxSession;
    }

    public void setKickoutSessionControlFilterMaxSession(int kickoutSessionControlFilterMaxSession) {
        this.kickoutSessionControlFilterMaxSession = kickoutSessionControlFilterMaxSession;
    }

    public String getKickoutUrl() {
        return this.kickoutUrl;
    }

    public void setKickoutUrl(String kickoutUrl) {
        this.kickoutUrl = kickoutUrl;
    }

    public String getLoginUrl() {
        return this.loginUrl;
    }

    public void setLoginUrl(String loginUrl) {
        this.loginUrl = loginUrl;
    }

    public String getSuccessUrl() {
        return this.successUrl;
    }

    public void setSuccessUrl(String successUrl) {
        this.successUrl = successUrl;
    }

    public String getUnauthorizedUrl() {
        return this.unauthorizedUrl;
    }

    public void setUnauthorizedUrl(String unauthorizedUrl) {
        this.unauthorizedUrl = unauthorizedUrl;
    }

    public int getSessionIdCookieMaxAge() {
        return this.sessionIdCookieMaxAge;
    }

    public void setSessionIdCookieMaxAge(int sessionIdCookieMaxAge) {
        this.sessionIdCookieMaxAge = sessionIdCookieMaxAge;
    }

    public boolean isKickoutAfter() {
        return this.kickoutAfter;
    }

    public void setKickoutAfter(boolean kickoutAfter) {
        this.kickoutAfter = kickoutAfter;
    }
}
