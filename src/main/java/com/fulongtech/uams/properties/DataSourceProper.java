/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.springframework.boot.context.properties.ConfigurationProperties
 *  org.springframework.stereotype.Component
 */
package com.fulongtech.uams.properties;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@ConfigurationProperties(prefix="spring.datasource")
@Component
public class DataSourceProper {
    private String type;
    private String username;
    private String password;
    private String oracleUrl;
    private String oracleDriverClassName;
    private String sqlserverUrl;
    private String sqlserverDriverClassName;
    private String mysqlUrl;
    private String mysqlDriverClassName;
    private String key;
    private Long timeBetweenEvictionRunsMillis;
    private Long minEvictableIdleTimeMillis;
    private String oracleValidationQuery;
    private String sqlServerValidationQuery;
    private String mysqlValidationQuery;
    private Boolean testOnBorrow = Boolean.FALSE;
    private Boolean testWhileIdle = Boolean.TRUE;
    private Integer numTestsPerEvictionRun;

    public String getType() {
        return this.type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getUsername() {
        return this.username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return this.password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getOracleUrl() {
        return this.oracleUrl;
    }

    public void setOracleUrl(String oracleUrl) {
        this.oracleUrl = oracleUrl;
    }

    public String getOracleDriverClassName() {
        return this.oracleDriverClassName;
    }

    public void setOracleDriverClassName(String oracleDriverClassName) {
        this.oracleDriverClassName = oracleDriverClassName;
    }

    public String getSqlserverUrl() {
        return this.sqlserverUrl;
    }

    public void setSqlserverUrl(String sqlserverUrl) {
        this.sqlserverUrl = sqlserverUrl;
    }

    public String getSqlserverDriverClassName() {
        return this.sqlserverDriverClassName;
    }

    public void setSqlserverDriverClassName(String sqlserverDriverClassName) {
        this.sqlserverDriverClassName = sqlserverDriverClassName;
    }

    public String getKey() {
        return this.key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public Long getTimeBetweenEvictionRunsMillis() {
        return this.timeBetweenEvictionRunsMillis;
    }

    public void setTimeBetweenEvictionRunsMillis(Long timeBetweenEvictionRunsMillis) {
        this.timeBetweenEvictionRunsMillis = timeBetweenEvictionRunsMillis;
    }

    public Long getMinEvictableIdleTimeMillis() {
        return this.minEvictableIdleTimeMillis;
    }

    public void setMinEvictableIdleTimeMillis(Long minEvictableIdleTimeMillis) {
        this.minEvictableIdleTimeMillis = minEvictableIdleTimeMillis;
    }

    public String getOracleValidationQuery() {
        return this.oracleValidationQuery;
    }

    public void setOracleValidationQuery(String oracleValidationQuery) {
        this.oracleValidationQuery = oracleValidationQuery;
    }

    public String getSqlServerValidationQuery() {
        return this.sqlServerValidationQuery;
    }

    public void setSqlServerValidationQuery(String sqlServerValidationQuery) {
        this.sqlServerValidationQuery = sqlServerValidationQuery;
    }

    public Boolean getTestOnBorrow() {
        return this.testOnBorrow;
    }

    public void setTestOnBorrow(Boolean testOnBorrow) {
        this.testOnBorrow = testOnBorrow;
    }

    public Boolean getTestWhileIdle() {
        return this.testWhileIdle;
    }

    public void setTestWhileIdle(Boolean testWhileIdle) {
        this.testWhileIdle = testWhileIdle;
    }

    public Integer getNumTestsPerEvictionRun() {
        return this.numTestsPerEvictionRun;
    }

    public void setNumTestsPerEvictionRun(Integer numTestsPerEvictionRun) {
        this.numTestsPerEvictionRun = numTestsPerEvictionRun;
    }

    public String getMysqlUrl() {
        return this.mysqlUrl;
    }

    public void setMysqlUrl(String mysqlUrl) {
        this.mysqlUrl = mysqlUrl;
    }

    public String getMysqlDriverClassName() {
        return this.mysqlDriverClassName;
    }

    public void setMysqlDriverClassName(String mysqlDriverClassName) {
        this.mysqlDriverClassName = mysqlDriverClassName;
    }

    public String getMysqlValidationQuery() {
        return this.mysqlValidationQuery;
    }

    public void setMysqlValidationQuery(String mysqlValidationQuery) {
        this.mysqlValidationQuery = mysqlValidationQuery;
    }
}
