/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.springframework.boot.context.properties.ConfigurationProperties
 *  org.springframework.stereotype.Component
 */
package com.fulongtech.uams.properties;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@ConfigurationProperties(prefix="uams.server")
@Component
public class ServerProper {
    private String contextPath;
    private Connector basicConnector;
    private Connector apiConnector;

    public Connector getBasicConnector() {
        return this.basicConnector;
    }

    public void setBasicConnector(Connector basicConnector) {
        this.basicConnector = basicConnector;
    }

    public Connector getApiConnector() {
        return this.apiConnector;
    }

    public void setApiConnector(Connector apiConnector) {
        this.apiConnector = apiConnector;
    }

    public String getContextPath() {
        return this.contextPath;
    }

    public void setContextPath(String contextPath) {
        this.contextPath = contextPath;
    }

    public static class Connector {
        private String schema;
        private String protocol;
        private int port;

        public String getSchema() {
            return this.schema;
        }

        public void setSchema(String schema) {
            this.schema = schema;
        }

        public String getProtocol() {
            return this.protocol;
        }

        public void setProtocol(String protocol) {
            this.protocol = protocol;
        }

        public int getPort() {
            return this.port;
        }

        public void setPort(int port) {
            this.port = port;
        }
    }
}
