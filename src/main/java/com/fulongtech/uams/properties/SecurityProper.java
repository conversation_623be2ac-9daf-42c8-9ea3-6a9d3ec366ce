/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.springframework.boot.context.properties.ConfigurationProperties
 *  org.springframework.stereotype.Component
 */
package com.fulongtech.uams.properties;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@ConfigurationProperties(prefix="uams.security-policy")
@Component
public class SecurityProper {
    private int globalSessionTimeout;
    private int maxSession;
    private String kickOutPolicy;
    private String whiteHostName;
    private boolean enableCaptcha;
    private boolean enableRememberMe;
    private String privateKey;

    public int getGlobalSessionTimeout() {
        return this.globalSessionTimeout;
    }

    public void setGlobalSessionTimeout(int globalSessionTimeout) {
        this.globalSessionTimeout = globalSessionTimeout;
    }

    public int getMaxSession() {
        return this.maxSession;
    }

    public void setMaxSession(int maxSession) {
        this.maxSession = maxSession;
    }

    public String getKickOutPolicy() {
        return this.kickOutPolicy;
    }

    public void setKickOutPolicy(String kickOutPolicy) {
        this.kickOutPolicy = kickOutPolicy;
    }

    public String getWhiteHostName() {
        return this.whiteHostName;
    }

    public void setWhiteHostName(String whiteHostName) {
        this.whiteHostName = whiteHostName;
    }

    public boolean isEnableCaptcha() {
        return this.enableCaptcha;
    }

    public void setEnableCaptcha(boolean enableCaptcha) {
        this.enableCaptcha = enableCaptcha;
    }

    public String getPrivateKey() {
        return this.privateKey;
    }

    public void setPrivateKey(String privateKey) {
        this.privateKey = privateKey;
    }

    public boolean isEnableRememberMe() {
        return this.enableRememberMe;
    }

    public void setEnableRememberMe(boolean enableRememberMe) {
        this.enableRememberMe = enableRememberMe;
    }
}
