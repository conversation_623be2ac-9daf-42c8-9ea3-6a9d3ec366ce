/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.springframework.boot.context.properties.ConfigurationProperties
 *  org.springframework.stereotype.Component
 */
package com.fulongtech.uams.properties;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@ConfigurationProperties(prefix="uams.smsLogin")
@Component
public class SmsLoginProper {
    private boolean enableSmsLogin;
    private String smsJarPath;
    private boolean enableHumanVerification;
    private int timeInterval;
    private int validityPeriod;
    private int mobilePhoneSendNumber;
    private int ipSendNumber;

    public boolean isEnableSmsLogin() {
        return this.enableSmsLogin;
    }

    public void setEnableSmsLogin(boolean enableSmsLogin) {
        this.enableSmsLogin = enableSmsLogin;
    }

    public int getTimeInterval() {
        return this.timeInterval;
    }

    public void setTimeInterval(int timeInterval) {
        this.timeInterval = timeInterval;
    }

    public int getValidityPeriod() {
        return this.validityPeriod;
    }

    public void setValidityPeriod(int validityPeriod) {
        this.validityPeriod = validityPeriod;
    }

    public int getMobilePhoneSendNumber() {
        return this.mobilePhoneSendNumber;
    }

    public void setMobilePhoneSendNumber(int mobilePhoneSendNumber) {
        this.mobilePhoneSendNumber = mobilePhoneSendNumber;
    }

    public int getIpSendNumber() {
        return this.ipSendNumber;
    }

    public void setIpSendNumber(int ipSendNumber) {
        this.ipSendNumber = ipSendNumber;
    }

    public String getSmsJarPath() {
        return this.smsJarPath;
    }

    public void setSmsJarPath(String smsJarPath) {
        this.smsJarPath = smsJarPath;
    }

    public boolean isEnableHumanVerification() {
        return this.enableHumanVerification;
    }

    public void setEnableHumanVerification(boolean enableHumanVerification) {
        this.enableHumanVerification = enableHumanVerification;
    }
}
