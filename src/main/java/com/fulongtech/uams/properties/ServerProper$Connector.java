/*
 * Decompiled with CFR 0.152.
 */
package com.fulongtech.uams.properties;

public static class ServerProper.Connector {
    private String schema;
    private String protocol;
    private int port;

    public String getSchema() {
        return this.schema;
    }

    public void setSchema(String schema) {
        this.schema = schema;
    }

    public String getProtocol() {
        return this.protocol;
    }

    public void setProtocol(String protocol) {
        this.protocol = protocol;
    }

    public int getPort() {
        return this.port;
    }

    public void setPort(int port) {
        this.port = port;
    }
}
