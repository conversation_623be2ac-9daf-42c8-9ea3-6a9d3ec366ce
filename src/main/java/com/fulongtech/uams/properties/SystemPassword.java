/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.springframework.boot.context.properties.ConfigurationProperties
 *  org.springframework.stereotype.Component
 */
package com.fulongtech.uams.properties;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Component
@ConfigurationProperties(prefix="uams.security-policy.password")
public class SystemPassword {
    private String defaultpwd;
    private long expireTime;
    private boolean updateDefaultPassword;
    private int retryCount;
    private long lockTime;

    public boolean isUpdateDefaultPassword() {
        return this.updateDefaultPassword;
    }

    public void setUpdateDefaultPassword(boolean updateDefaultPassword) {
        this.updateDefaultPassword = updateDefaultPassword;
    }

    public String getDefaultpwd() {
        return this.defaultpwd;
    }

    public void setDefaultpwd(String defaultpwd) {
        this.defaultpwd = defaultpwd;
    }

    public long getExpireTime() {
        return this.expireTime;
    }

    public void setExpireTime(long expireTime) {
        this.expireTime = expireTime * 1000L * 60L * 60L * 24L;
    }

    public int getRetryCount() {
        return this.retryCount;
    }

    public void setRetryCount(int retryCount) {
        this.retryCount = retryCount;
    }

    public long getLockTime() {
        return this.lockTime;
    }

    public void setLockTime(long lockTime) {
        this.lockTime = lockTime;
    }
}
