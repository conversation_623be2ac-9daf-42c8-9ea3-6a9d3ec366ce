/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.springframework.boot.context.properties.ConfigurationProperties
 *  org.springframework.stereotype.Component
 */
package com.fulongtech.uams.properties;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@ConfigurationProperties(prefix="uams.third-authentication")
@Component(value="thirdAuthenticationProper")
public class ThirdAuthenticationProper {
    private List<String> authenticationType = new ArrayList<String>();
    private Map<String, Object> env = new HashMap<String, Object>();

    public List<String> getAuthenticationType() {
        return this.authenticationType;
    }

    public void setAuthenticationType(List<String> authenticationType) {
        this.authenticationType = authenticationType;
    }

    public Map<String, Object> getEnv() {
        return this.env;
    }

    public void setEnv(Map<String, Object> env) {
        this.env = env;
    }
}
