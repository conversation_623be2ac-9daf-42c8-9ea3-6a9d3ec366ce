/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.springframework.boot.context.properties.ConfigurationProperties
 *  org.springframework.stereotype.Component
 */
package com.fulongtech.uams.properties;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@ConfigurationProperties(prefix="uams.gateway")
@Component
public class GatewayProper {
    private boolean useSSL;
    private String host;
    private String port;
    private String gatewayUrl;

    public boolean isUseSSL() {
        return this.useSSL;
    }

    public void setUseSSL(boolean useSSL) {
        this.useSSL = useSSL;
    }

    public String getHost() {
        return this.host;
    }

    public void setHost(String host) {
        this.host = host;
    }

    public String getPort() {
        return this.port;
    }

    public void setPort(String port) {
        this.port = port;
    }

    public String getGatewayUrl() {
        if (this.gatewayUrl == null || this.gatewayUrl.equals("")) {
            this.gatewayUrl = (this.useSSL ? "https://" : "http://") + this.host + ":" + this.port;
        }
        return this.gatewayUrl;
    }

    public void setGatewayUrl(String gatewayUrl) {
        this.gatewayUrl = gatewayUrl;
    }
}
