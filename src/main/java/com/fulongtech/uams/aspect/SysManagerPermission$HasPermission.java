/*
 * Decompiled with CFR 0.152.
 */
package com.fulongtech.uams.aspect;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target(value={ElementType.METHOD})
@Retention(value=RetentionPolicy.RUNTIME)
@Documented
public static @interface SysManagerPermission.HasPermission {
    public String value() default "";

    public boolean isAdmin() default false;

    public boolean isSysGroup() default true;
}
