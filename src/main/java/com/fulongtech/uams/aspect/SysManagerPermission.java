/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.servlet.http.HttpServletRequest
 *  org.aspectj.lang.JoinPoint
 *  org.aspectj.lang.annotation.Aspect
 *  org.aspectj.lang.annotation.Before
 *  org.aspectj.lang.annotation.Pointcut
 *  org.slf4j.Logger
 *  org.slf4j.LoggerFactory
 *  org.springframework.stereotype.Component
 */
package com.fulongtech.uams.aspect;

import com.fulongtech.uams.controller.UserController;
import com.fulongtech.uams.exception.ErrorCode;
import com.fulongtech.uams.exception.UamsException;
import com.fulongtech.uams.model.Group;
import com.fulongtech.uams.model.SysUser;
import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import java.util.List;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
@Aspect
public class SysManagerPermission {
    @Resource
    private UserController userController;
    @Resource
    private HttpServletRequest request;
    private static final Logger log = LoggerFactory.getLogger(SysManagerPermission.class);

    @Pointcut(value="@annotation(com.fulongtech.uams.aspect.SysManagerPermission.HasPermission) ")
    public void entryPoint() {
    }

    @Before(value="entryPoint() && @annotation(sysManager)")
    public void before(JoinPoint joinPoint, HasPermission sysManager) {
        List<Group> groups;
        SysUser currentUser = this.userController.getCurrentUser(this.request);
        if (sysManager.isAdmin() && !"admin".equals(currentUser.getUsername()) && !sysManager.isAdmin() && sysManager.isSysGroup() && ((groups = currentUser.getGroups()) == null || groups.isEmpty() || groups.stream().noneMatch(Group::isSysGroup))) {
            log.warn("\u7528\u6237\uff1a" + currentUser.getUsername() + "\u8c03\u7528\u4e86\uff1a" + sysManager.value());
            throw new UamsException(ErrorCode.UNAUTHORIZED, "\u7528\u6237\uff1a" + currentUser.getUsername() + ",\u6ca1\u6709" + sysManager.value() + "\u7684\u6743\u9650");
        }
    }

    @Target(value={ElementType.METHOD})
    @Retention(value=RetentionPolicy.RUNTIME)
    @Documented
    public static @interface HasPermission {
        public String value() default "";

        public boolean isAdmin() default false;

        public boolean isSysGroup() default true;
    }
}
