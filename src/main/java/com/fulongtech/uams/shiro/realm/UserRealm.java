/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.apache.shiro.SecurityUtils
 *  org.apache.shiro.authc.AuthenticationException
 *  org.apache.shiro.authc.AuthenticationInfo
 *  org.apache.shiro.authc.AuthenticationToken
 *  org.apache.shiro.authc.SimpleAuthenticationInfo
 *  org.apache.shiro.authz.AuthorizationInfo
 *  org.apache.shiro.authz.SimpleAuthorizationInfo
 *  org.apache.shiro.realm.AuthorizingRealm
 *  org.apache.shiro.subject.PrincipalCollection
 */
package com.fulongtech.uams.shiro.realm;

import com.fulongtech.uams.model.SysUser;
import com.fulongtech.uams.service.UserService;
import javax.annotation.Resource;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authc.AuthenticationException;
import org.apache.shiro.authc.AuthenticationInfo;
import org.apache.shiro.authc.AuthenticationToken;
import org.apache.shiro.authc.SimpleAuthenticationInfo;
import org.apache.shiro.authz.AuthorizationInfo;
import org.apache.shiro.authz.SimpleAuthorizationInfo;
import org.apache.shiro.realm.AuthorizingRealm;
import org.apache.shiro.subject.PrincipalCollection;

public class UserRealm
extends AuthorizingRealm {
    @Resource
    private UserService userService;

    protected AuthorizationInfo doGetAuthorizationInfo(PrincipalCollection principals) {
        String username = (String)principals.getPrimaryPrincipal();
        SimpleAuthorizationInfo authorizationInfo = new SimpleAuthorizationInfo();
        authorizationInfo.setRoles(null);
        authorizationInfo.setStringPermissions(null);
        return authorizationInfo;
    }

    public boolean isAuthorizationCachingEnabled() {
        return true;
    }

    protected AuthenticationInfo doGetAuthenticationInfo(AuthenticationToken token) throws AuthenticationException {
        String userName = (String)token.getPrincipal();
        SysUser user = this.userService.findByUsername(userName);
        if (user == null) {
            user = new SysUser();
            user.setUsername(userName);
        }
        SimpleAuthenticationInfo authenticationInfo = new SimpleAuthenticationInfo((Object)user.getUsername(), (Object)user.getPassword(), this.getName());
        return authenticationInfo;
    }

    public void clearCachedAuthorizationInfo(PrincipalCollection principals) {
        super.clearCachedAuthorizationInfo(principals);
    }

    public void clearCachedAuthenticationInfo(PrincipalCollection principals) {
        super.clearCachedAuthenticationInfo(principals);
    }

    public void clearCache(PrincipalCollection principals) {
        super.clearCache(principals);
    }

    public void clearAllCachedAuthorizationInfo() {
        this.getAuthorizationCache().clear();
    }

    public void clearAllCachedAuthenticationInfo() {
        this.getAuthenticationCache().clear();
    }

    public void clearAllCache() {
        this.clearCachedAuthorizationInfo(SecurityUtils.getSubject().getPrincipals());
    }
}
