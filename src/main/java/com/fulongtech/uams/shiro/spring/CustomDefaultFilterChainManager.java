/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.servlet.Filter
 *  javax.servlet.FilterChain
 *  org.apache.shiro.config.Ini
 *  org.apache.shiro.config.Ini$Section
 *  org.apache.shiro.util.CollectionUtils
 *  org.apache.shiro.util.Nameable
 *  org.apache.shiro.util.StringUtils
 *  org.apache.shiro.web.filter.AccessControlFilter
 *  org.apache.shiro.web.filter.authc.AuthenticationFilter
 *  org.apache.shiro.web.filter.authz.AuthorizationFilter
 *  org.apache.shiro.web.filter.mgt.DefaultFilterChainManager
 *  org.apache.shiro.web.filter.mgt.SimpleNamedFilterList
 */
package com.fulongtech.uams.shiro.spring;

import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.PostConstruct;
import javax.servlet.Filter;
import javax.servlet.FilterChain;
import org.apache.shiro.config.Ini;
import org.apache.shiro.util.CollectionUtils;
import org.apache.shiro.util.Nameable;
import org.apache.shiro.util.StringUtils;
import org.apache.shiro.web.filter.AccessControlFilter;
import org.apache.shiro.web.filter.authc.AuthenticationFilter;
import org.apache.shiro.web.filter.authz.AuthorizationFilter;
import org.apache.shiro.web.filter.mgt.DefaultFilterChainManager;
import org.apache.shiro.web.filter.mgt.SimpleNamedFilterList;

public class CustomDefaultFilterChainManager
extends DefaultFilterChainManager {
    private Map<String, String> filterChainDefinitionMap = null;
    private String loginUrl;
    private String successUrl;
    private String unauthorizedUrl;

    public CustomDefaultFilterChainManager() {
        this.setFilters(new LinkedHashMap());
        this.setFilterChains(new LinkedHashMap());
        this.addDefaultFilters(true);
    }

    public Map<String, String> getFilterChainDefinitionMap() {
        return this.filterChainDefinitionMap;
    }

    public void setFilterChainDefinitionMap(Map<String, String> filterChainDefinitionMap) {
        this.filterChainDefinitionMap = filterChainDefinitionMap;
    }

    public void setCustomFilters(Map<String, Filter> customFilters) {
        for (Map.Entry<String, Filter> entry : customFilters.entrySet()) {
            this.addFilter(entry.getKey(), entry.getValue(), false);
        }
    }

    public void setDefaultFilterChainDefinitions(String definitions) {
        Ini ini = new Ini();
        ini.load(definitions);
        Ini.Section section = ini.getSection("urls");
        if (CollectionUtils.isEmpty((Map)section)) {
            section = ini.getSection("");
        }
        this.setFilterChainDefinitionMap((Map<String, String>)section);
    }

    public String getLoginUrl() {
        return this.loginUrl;
    }

    public void setLoginUrl(String loginUrl) {
        this.loginUrl = loginUrl;
    }

    public String getSuccessUrl() {
        return this.successUrl;
    }

    public void setSuccessUrl(String successUrl) {
        this.successUrl = successUrl;
    }

    public String getUnauthorizedUrl() {
        return this.unauthorizedUrl;
    }

    public void setUnauthorizedUrl(String unauthorizedUrl) {
        this.unauthorizedUrl = unauthorizedUrl;
    }

    @PostConstruct
    public void init() {
        Map<String, String> chains;
        Map filters = this.getFilters();
        if (!CollectionUtils.isEmpty((Map)filters)) {
            for (Map.Entry entry : filters.entrySet()) {
                String name = (String)entry.getKey();
                Filter filter = (Filter)entry.getValue();
                this.applyGlobalPropertiesIfNecessary(filter);
                if (!(filter instanceof Nameable)) continue;
                ((Nameable)filter).setName(name);
            }
        }
        if (!CollectionUtils.isEmpty(chains = this.getFilterChainDefinitionMap())) {
            for (Map.Entry<String, String> entry : chains.entrySet()) {
                String url = entry.getKey();
                String chainDefinition = entry.getValue();
                this.createChain(url, chainDefinition);
            }
        }
    }

    protected void initFilter(Filter filter) {
    }

    public FilterChain proxy(FilterChain original, List<String> chainNames) {
        SimpleNamedFilterList configured = new SimpleNamedFilterList(chainNames.toString());
        for (String chainName : chainNames) {
            configured.addAll((Collection)this.getChain(chainName));
        }
        return configured.proxy(original);
    }

    private void applyLoginUrlIfNecessary(Filter filter) {
        AccessControlFilter acFilter;
        String existingLoginUrl;
        String loginUrl = this.getLoginUrl();
        if (StringUtils.hasText((String)loginUrl) && filter instanceof AccessControlFilter && "/login.jsp".equals(existingLoginUrl = (acFilter = (AccessControlFilter)filter).getLoginUrl())) {
            acFilter.setLoginUrl(loginUrl);
        }
    }

    private void applySuccessUrlIfNecessary(Filter filter) {
        AuthenticationFilter authcFilter;
        String existingSuccessUrl;
        String successUrl = this.getSuccessUrl();
        if (StringUtils.hasText((String)successUrl) && filter instanceof AuthenticationFilter && "/".equals(existingSuccessUrl = (authcFilter = (AuthenticationFilter)filter).getSuccessUrl())) {
            authcFilter.setSuccessUrl(successUrl);
        }
    }

    private void applyUnauthorizedUrlIfNecessary(Filter filter) {
        AuthorizationFilter authzFilter;
        String existingUnauthorizedUrl;
        String unauthorizedUrl = this.getUnauthorizedUrl();
        if (StringUtils.hasText((String)unauthorizedUrl) && filter instanceof AuthorizationFilter && (existingUnauthorizedUrl = (authzFilter = (AuthorizationFilter)filter).getUnauthorizedUrl()) == null) {
            authzFilter.setUnauthorizedUrl(unauthorizedUrl);
        }
    }

    private void applyGlobalPropertiesIfNecessary(Filter filter) {
        this.applyLoginUrlIfNecessary(filter);
        this.applySuccessUrlIfNecessary(filter);
        this.applyUnauthorizedUrlIfNecessary(filter);
    }
}
