/*
 * Decompiled with CFR 0.152.
 */
package com.fulongtech.uams.shiro.cache;

import java.io.Serializable;
import java.util.concurrent.atomic.AtomicInteger;

public class RetryCache
implements Serializable {
    private static final long serialVersionUID = 619219165383346173L;
    private AtomicInteger retryCount;
    private Integer forbiddenStart;

    public AtomicInteger getRetryCount() {
        return this.retryCount;
    }

    public void setRetryCount(AtomicInteger retryCount) {
        this.retryCount = retryCount;
    }

    public Integer getForbiddenStart() {
        return this.forbiddenStart;
    }

    public void setForbiddenStart(Integer forbiddenStart) {
        this.forbiddenStart = forbiddenStart;
    }
}
