/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.apache.shiro.authc.AuthenticationInfo
 *  org.apache.shiro.authc.AuthenticationToken
 *  org.apache.shiro.authc.ExcessiveAttemptsException
 *  org.apache.shiro.authc.UsernamePasswordToken
 *  org.apache.shiro.authc.credential.HashedCredentialsMatcher
 *  org.apache.shiro.cache.Cache
 *  org.crazycake.shiro.RedisCache
 */
package com.fulongtech.uams.shiro.credentials;

import com.fulongtech.uams.properties.SystemPassword;
import com.fulongtech.uams.shiro.cache.RetryCache;
import com.fulongtech.uams.shiro.token.UamsAuthenticationToken;
import com.fulongtech.uams.util.Md5Util;
import java.util.concurrent.atomic.AtomicInteger;
import javax.annotation.Resource;
import org.apache.shiro.authc.AuthenticationInfo;
import org.apache.shiro.authc.AuthenticationToken;
import org.apache.shiro.authc.ExcessiveAttemptsException;
import org.apache.shiro.authc.UsernamePasswordToken;
import org.apache.shiro.authc.credential.HashedCredentialsMatcher;
import org.apache.shiro.cache.Cache;
import org.crazycake.shiro.RedisCache;

public class RetryLimitHashedCredentialsMatcher
extends HashedCredentialsMatcher {
    private Cache<String, RetryCache> passwordRetryCache;
    private Integer passwordRetryTimeToIdleSeconds;
    @Resource
    private SystemPassword systemPassword;

    public RetryLimitHashedCredentialsMatcher(RedisCache redisCache, int expire) {
        this.passwordRetryCache = redisCache;
        this.passwordRetryTimeToIdleSeconds = expire;
    }

    public boolean doCredentialsMatch(AuthenticationToken token, AuthenticationInfo info) {
        String username = (String)token.getPrincipal();
        RetryCache retryCache = this.passwordRetryCache.get((Object)username) == null ? new RetryCache() : (RetryCache)this.passwordRetryCache.get((Object)username);
        AtomicInteger retryCount = retryCache.getRetryCount();
        if (retryCount == null) {
            retryCount = new AtomicInteger(0);
            retryCache.setRetryCount(retryCount);
        }
        if (retryCount.incrementAndGet() >= this.systemPassword.getRetryCount()) {
            if (retryCache.getForbiddenStart() == null) {
                retryCache.setForbiddenStart(Math.toIntExact(System.currentTimeMillis() / 1000L));
                this.passwordRetryCache.put((Object)username, (Object)retryCache);
            }
            Integer now = Math.toIntExact(System.currentTimeMillis() / 1000L);
            Integer timeRemaining = now - retryCache.getForbiddenStart();
            throw new ExcessiveAttemptsException("" + (this.passwordRetryTimeToIdleSeconds - timeRemaining) / 60);
        }
        this.passwordRetryCache.put((Object)username, (Object)retryCache);
        char[] c = (char[])token.getCredentials();
        if (info.getCredentials() == null) {
            return false;
        }
        boolean matches = false;
        matches = token instanceof UamsAuthenticationToken || token instanceof UsernamePasswordToken ? info.getCredentials().equals(String.valueOf(c)) : info.getCredentials().equals(Md5Util.getMD5(String.valueOf(c)));
        if (matches) {
            this.passwordRetryCache.remove((Object)username);
        }
        return matches;
    }
}
