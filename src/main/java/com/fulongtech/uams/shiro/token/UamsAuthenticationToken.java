/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.apache.shiro.authc.UsernamePasswordToken
 */
package com.fulongtech.uams.shiro.token;

import org.apache.shiro.authc.UsernamePasswordToken;

public class UamsAuthenticationToken
extends UsernamePasswordToken {
    public UamsAuthenticationToken() {
    }

    public UamsAuthenticationToken(String username, char[] password) {
        this(username, password, false, (String)null);
    }

    public UamsAuthenticationToken(String username, String password) {
        this(username, password != null ? password.toCharArray() : null, false, (String)null);
    }

    public UamsAuthenticationToken(String username, char[] password, String host) {
        this(username, password, false, host);
    }

    public UamsAuthenticationToken(String username, String password, String host) {
        this(username, password != null ? password.toCharArray() : null, false, host);
    }

    public UamsAuthenticationToken(String username, char[] password, boolean rememberMe) {
        this(username, password, rememberMe, (String)null);
    }

    public UamsAuthenticationToken(String username, String password, boolean rememberMe) {
        this(username, password != null ? password.toCharArray() : null, rememberMe, (String)null);
    }

    public UamsAuthenticationToken(String username, char[] password, boolean rememberMe, String host) {
        super(username, password, rememberMe, host);
    }

    public UamsAuthenticationToken(String username, String password, boolean rememberMe, String host) {
        this(username, password != null ? password.toCharArray() : null, rememberMe, host);
    }
}
