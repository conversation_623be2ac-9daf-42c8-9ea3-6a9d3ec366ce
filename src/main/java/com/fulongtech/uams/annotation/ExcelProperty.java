/*
 * Decompiled with CFR 0.152.
 */
package com.fulongtech.uams.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Inherited;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target(value={ElementType.FIELD})
@Retention(value=RetentionPolicy.RUNTIME)
@Inherited
public @interface ExcelProperty {
    public String value() default "";

    public boolean require() default false;

    public boolean multi() default false;
}
