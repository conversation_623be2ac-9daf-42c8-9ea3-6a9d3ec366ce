/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.servlet.http.HttpServletRequest
 *  javax.servlet.http.HttpServletRequestWrapper
 */
package com.fulongtech.uams.filter;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;

class XssFilter.FulongXssHttpServletRequestWrapper
extends HttpServletRequestWrapper {
    private Map<String, String[]> params;

    public XssFilter.FulongXssHttpServletRequestWrapper(HttpServletRequest request) throws IOException {
        super(request);
        this.params = new HashMap<String, String[]>();
        this.params.putAll(request.getParameterMap());
    }

    public void setParameter(String name, Object value) {
        if (value != null) {
            if (value instanceof String[]) {
                this.params.put(name, (String[])value);
            } else if (value instanceof String) {
                this.params.put(name, new String[]{(String)value});
            } else {
                this.params.put(name, new String[]{String.valueOf(value)});
            }
        }
    }

    public String getParameter(String name) {
        String[] values = this.params.get(name);
        if (values == null || values.length == 0) {
            return null;
        }
        return values[0];
    }

    public String[] getParameterValues(String name) {
        return this.params.get(name);
    }
}
