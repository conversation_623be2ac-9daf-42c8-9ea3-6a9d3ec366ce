/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  com.fulongtech.uams.token.utils.UAMSTokenUtils
 *  com.google.gson.Gson
 *  javax.servlet.ServletRequest
 *  javax.servlet.ServletResponse
 *  javax.servlet.http.HttpServletRequest
 *  javax.servlet.http.HttpServletResponse
 *  javax.servlet.http.HttpSession
 *  org.apache.commons.lang3.StringUtils
 *  org.apache.shiro.subject.Subject
 *  org.apache.shiro.web.filter.AccessControlFilter
 *  org.springframework.session.ExpiringSession
 *  org.springframework.stereotype.Component
 */
package com.fulongtech.uams.filter;

import com.fulongtech.uams.model.Group;
import com.fulongtech.uams.model.SysUser;
import com.fulongtech.uams.service.GroupService;
import com.fulongtech.uams.service.UserService;
import com.fulongtech.uams.token.utils.UAMSTokenUtils;
import com.fulongtech.uams.util.SessionUtil;
import com.google.gson.Gson;
import java.net.URLDecoder;
import java.util.HashMap;
import java.util.List;
import javax.annotation.Resource;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.subject.Subject;
import org.apache.shiro.web.filter.AccessControlFilter;
import org.springframework.session.ExpiringSession;
import org.springframework.stereotype.Component;

@Component
public class UserFilter
extends AccessControlFilter {
    @Resource
    private UserService userService;
    @Resource
    private GroupService groupService;
    @Resource
    private SessionUtil sessionUtil;
    private static final String TOKEN_HEADER = "Authorization-UAMS";

    protected boolean isAccessAllowed(ServletRequest request, ServletResponse response, Object mappedValue) {
        if (this.isLoginRequest(request, response)) {
            return true;
        }
        Subject subject = this.getSubject(request, response);
        return subject.getPrincipal() != null;
    }

    protected boolean onAccessDenied(ServletRequest request, ServletResponse response) throws Exception {
        HttpServletRequest httpServletRequest = (HttpServletRequest)request;
        HttpServletResponse httpServletResponse = (HttpServletResponse)response;
        String userInfo = httpServletRequest.getHeader("userInfo");
        HttpSession session = httpServletRequest.getSession();
        if (!StringUtils.isEmpty((CharSequence)userInfo)) {
            Gson gson = new Gson();
            String decodeInfo = URLDecoder.decode(userInfo);
            HashMap map = (HashMap)gson.fromJson(decodeInfo, HashMap.class);
            String id = (String)map.get("id");
            SysUser currentUser = this.userService.getById(id);
            if (currentUser == null) {
                httpServletResponse.sendError(412, "\u7528\u6237\u4e0d\u5b58\u5728\uff0c\u8bf7\u68c0\u67e5");
                return false;
            }
            if (!currentUser.getUsername().equals(map.get("username"))) {
                httpServletResponse.sendError(412, "username\u6709\u8bef\uff0c\u8bf7\u68c0\u67e5");
                return false;
            }
            List<ExpiringSession> activeSessions = this.sessionUtil.getSessionByUsername(currentUser.getUsername());
            if (activeSessions.size() > 0 && currentUser != null) {
                List<Group> groups = this.groupService.getGroupByUserId(id);
                currentUser.setGroups(groups);
                session.setAttribute("currentUser", (Object)currentUser);
                session.setAttribute("isLogin", (Object)false);
                return true;
            }
            String token_header = httpServletRequest.getHeader(TOKEN_HEADER);
            if (!StringUtils.isEmpty((CharSequence)token_header) && UAMSTokenUtils.checkTokenValid((String)token_header)) {
                List<Group> groups = this.groupService.getGroupByUserId(id);
                currentUser.setGroups(groups);
                session.setAttribute("currentUser", (Object)currentUser);
                session.setAttribute("isLogin", (Object)false);
                return true;
            }
            httpServletResponse.sendError(403, "\u672a\u767b\u5f55\uff01");
            return false;
        }
        this.saveRequestAndRedirectToLogin(request, response);
        return false;
    }
}
