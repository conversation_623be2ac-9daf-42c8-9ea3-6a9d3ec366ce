/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.servlet.Filter
 *  javax.servlet.FilterChain
 *  javax.servlet.FilterConfig
 *  javax.servlet.ServletException
 *  javax.servlet.ServletRequest
 *  javax.servlet.ServletResponse
 *  javax.servlet.http.HttpServletRequest
 *  javax.servlet.http.HttpServletResponse
 *  org.springframework.beans.factory.annotation.Value
 *  org.springframework.stereotype.Component
 */
package com.fulongtech.uams.filter;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class ChangePasswordFilter
implements Filter {
    private static final String UPDATE_PASSWORD = "updatePassword";
    @Value(value="${uams.server.contextPath}")
    private String contextPath;
    private static final List<String> WHITE_URL = new ArrayList<String>();

    public void init(FilterConfig filterConfig) {
        WHITE_URL.add(this.contextPath + "/users/toChangePwd");
        WHITE_URL.add(this.contextPath + "/users/changePwd");
        WHITE_URL.add(this.contextPath + "/getKey");
        WHITE_URL.add(this.contextPath + "/login");
        WHITE_URL.add(this.contextPath + "/static/");
        WHITE_URL.add(this.contextPath + "/logout");
        WHITE_URL.add(this.contextPath + "/exit");
    }

    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        HttpServletRequest httpRequest = (HttpServletRequest)request;
        HttpServletResponse httpResponse = (HttpServletResponse)response;
        if (httpRequest.getSession().getAttribute(UPDATE_PASSWORD) != null && ((Boolean)httpRequest.getSession().getAttribute(UPDATE_PASSWORD)).booleanValue()) {
            String requestUrl = httpRequest.getRequestURI();
            boolean toChangePassword = true;
            for (String s : WHITE_URL) {
                if (!requestUrl.startsWith(s)) continue;
                toChangePassword = false;
                break;
            }
            if (toChangePassword) {
                httpResponse.sendRedirect(this.contextPath + "/users/toChangePwd");
                return;
            }
        }
        chain.doFilter((ServletRequest)httpRequest, (ServletResponse)httpResponse);
    }

    public void destroy() {
    }
}
