/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.servlet.Filter
 *  javax.servlet.FilterChain
 *  javax.servlet.FilterConfig
 *  javax.servlet.ServletException
 *  javax.servlet.ServletRequest
 *  javax.servlet.ServletResponse
 *  javax.servlet.annotation.WebFilter
 *  javax.servlet.http.HttpServletRequest
 *  javax.servlet.http.HttpServletRequestWrapper
 *  org.apache.commons.lang3.StringUtils
 *  org.springframework.core.annotation.Order
 *  org.springframework.stereotype.Component
 */
package com.fulongtech.uams.filter;

import com.fulongtech.uams.exception.ErrorCode;
import com.fulongtech.uams.exception.UamsException;
import java.io.IOException;
import java.text.StringCharacterIterator;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Pattern;
import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.annotation.WebFilter;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

@Component
@Order(value=1)
@WebFilter(urlPatterns={"/*"}, filterName="XssFilter")
public class XssFilter
implements Filter {
    public void init(FilterConfig filterConfig) {
    }

    public void destroy() {
    }

    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        FulongXssHttpServletRequestWrapper requestWrapper1 = new FulongXssHttpServletRequestWrapper((HttpServletRequest)request);
        String url = ((HttpServletRequest)request).getRequestURI();
        XssFilter.HTMLEncode(XssFilter.xssEncode(url));
        Enumeration enu = request.getParameterNames();
        while (enu.hasMoreElements()) {
            String paraName = (String)enu.nextElement();
            String value = request.getParameter(paraName);
            if (paraName.equals("returnURL")) {
                this.checkValue(value);
            }
            requestWrapper1.setParameter(paraName, XssFilter.HTMLEncode(XssFilter.xssEncode(value)));
        }
        chain.doFilter((ServletRequest)requestWrapper1, response);
    }

    private void checkValue(String value) {
        if (value != null && !StringUtils.isEmpty((CharSequence)value)) {
            String copyValue = value;
            Pattern p = Pattern.compile("^([^\\<\\>\\\"\\'\\%\\;\\)\\(\\&\\+]*)$");
            if (!p.matcher(value).find()) {
                throw new UamsException(ErrorCode.UNPROCESABLE_ENTITY, "\u68c0\u6d4b\u5230\u5f02\u5e38\u5b57\u7b26\u4e32: " + copyValue);
            }
        }
    }

    private static String xssEncode(String s) {
        if (s == null || s.isEmpty()) {
            return s;
        }
        String result = XssFilter.stripXSS(s);
        return result;
    }

    public static String escape(String s) {
        StringBuilder sb = new StringBuilder(s.length() + 16);
        block8: for (int i = 0; i < s.length(); ++i) {
            char c = s.charAt(i);
            switch (c) {
                case '>': {
                    sb.append('\uff1e');
                    continue block8;
                }
                case '<': {
                    sb.append('\uff1c');
                    continue block8;
                }
                case '\'': {
                    sb.append('\u2018');
                    continue block8;
                }
                case '\"': {
                    sb.append('\u201c');
                    continue block8;
                }
                case '\\': {
                    sb.append('\uff3c');
                    continue block8;
                }
                case '%': {
                    sb.append('\uff05');
                    continue block8;
                }
                default: {
                    sb.append(c);
                }
            }
        }
        return sb.toString();
    }

    private static String stripXSS(String value) {
        String copyValue = value;
        if (value != null) {
            value = value.replaceAll("", "");
            Pattern scriptPattern = Pattern.compile("<script>(.*?)</script>", 2);
            value = scriptPattern.matcher(value).replaceAll("xss_attack");
            scriptPattern = Pattern.compile("src[\r\n]*=[\r\n]*\\'(.*?)\\'", 42);
            value = scriptPattern.matcher(value).replaceAll("xss_attack");
            scriptPattern = Pattern.compile("</script>", 2);
            value = scriptPattern.matcher(value).replaceAll("xss_attack");
            scriptPattern = Pattern.compile("<script(.*?)>", 42);
            value = scriptPattern.matcher(value).replaceAll("xss_attack");
            scriptPattern = Pattern.compile("eval\\((.*?)\\)", 42);
            value = scriptPattern.matcher(value).replaceAll("xss_attack");
            scriptPattern = Pattern.compile("expression\\((.*?)\\)", 42);
            value = scriptPattern.matcher(value).replaceAll("xss_attack");
            scriptPattern = Pattern.compile("javascript:", 2);
            value = scriptPattern.matcher(value).replaceAll("xss_attack");
            scriptPattern = Pattern.compile("vbscript:", 2);
            value = scriptPattern.matcher(value).replaceAll("xss_attack");
            scriptPattern = Pattern.compile("onload(.*?)=", 42);
            value = scriptPattern.matcher(value).replaceAll("xss_attack");
            scriptPattern = Pattern.compile("<iframe>(.*?)</iframe>", 2);
            value = scriptPattern.matcher(value).replaceAll("xss_attack");
            scriptPattern = Pattern.compile("</iframe>", 2);
            value = scriptPattern.matcher(value).replaceAll("xss_attack");
            scriptPattern = Pattern.compile("<iframe(.*?)>", 42);
            value = scriptPattern.matcher(value).replaceAll("xss_attack");
        }
        if (value.contains("xss_attack")) {
            throw new UamsException(ErrorCode.UNPROCESABLE_ENTITY, "\u68c0\u6d4b\u5230\u5f02\u5e38\u5b57\u7b26\u4e32: " + copyValue);
        }
        return value;
    }

    public static String HTMLEncode(String aText) {
        StringBuilder result = new StringBuilder();
        StringCharacterIterator iterator = new StringCharacterIterator(aText);
        char character = iterator.current();
        while (character != '\uffff') {
            if (character == '<') {
                result.append("<");
            } else if (character == '>') {
                result.append(">");
            } else if (character == '&') {
                result.append("&");
            } else if (character == '\"') {
                result.append("\"");
            } else {
                result.append(character);
            }
            character = iterator.next();
        }
        return result.toString();
    }

    class FulongXssHttpServletRequestWrapper
    extends HttpServletRequestWrapper {
        private Map<String, String[]> params;

        public FulongXssHttpServletRequestWrapper(HttpServletRequest request) throws IOException {
            super(request);
            this.params = new HashMap<String, String[]>();
            this.params.putAll(request.getParameterMap());
        }

        public void setParameter(String name, Object value) {
            if (value != null) {
                if (value instanceof String[]) {
                    this.params.put(name, (String[])value);
                } else if (value instanceof String) {
                    this.params.put(name, new String[]{(String)value});
                } else {
                    this.params.put(name, new String[]{String.valueOf(value)});
                }
            }
        }

        public String getParameter(String name) {
            String[] values = this.params.get(name);
            if (values == null || values.length == 0) {
                return null;
            }
            return values[0];
        }

        public String[] getParameterValues(String name) {
            return this.params.get(name);
        }
    }
}
