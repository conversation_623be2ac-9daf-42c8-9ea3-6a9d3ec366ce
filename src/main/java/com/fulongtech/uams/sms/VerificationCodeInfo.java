/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  cn.hutool.core.date.DateField
 *  cn.hutool.core.date.DateUtil
 */
package com.fulongtech.uams.sms;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import java.util.Date;

public class VerificationCodeInfo {
    private String mobilePhone;
    private Date sendTime;
    private Date refreshTime;
    private Date expirationTime;
    private String verificationCode;

    public VerificationCodeInfo(String mobilePhone, Date sendTime, Date refreshTime, Date expirationTime, String verificationCode) {
        this.mobilePhone = mobilePhone;
        this.sendTime = sendTime;
        this.refreshTime = refreshTime;
        this.expirationTime = expirationTime;
        this.verificationCode = verificationCode;
    }

    public VerificationCodeInfo(String mobilePhone, String verificationCode, Date sendTime, Integer timeInterval, Integer validityPeriod) {
        this.mobilePhone = mobilePhone;
        this.sendTime = sendTime;
        this.expirationTime = DateUtil.offset((Date)sendTime, (DateField)DateField.HOUR_OF_DAY, (int)validityPeriod);
        this.refreshTime = DateUtil.offset((Date)sendTime, (DateField)DateField.SECOND, (int)timeInterval);
        this.verificationCode = verificationCode;
    }

    public String getMobilePhone() {
        return this.mobilePhone;
    }

    public void setMobilePhone(String mobilePhone) {
        this.mobilePhone = mobilePhone;
    }

    public Date getSendTime() {
        return this.sendTime;
    }

    public void setSendTime(Date sendTime) {
        this.sendTime = sendTime;
    }

    public Date getExpirationTime() {
        return this.expirationTime;
    }

    public void setExpirationTime(Date expirationTime) {
        this.expirationTime = expirationTime;
    }

    public String getVerificationCode() {
        return this.verificationCode;
    }

    public void setVerificationCode(String verificationCode) {
        this.verificationCode = verificationCode;
    }

    public Date getRefreshTime() {
        return this.refreshTime;
    }

    public void setRefreshTime(Date refreshTime) {
        this.refreshTime = refreshTime;
    }
}
