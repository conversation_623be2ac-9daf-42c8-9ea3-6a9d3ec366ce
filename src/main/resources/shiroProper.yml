sessionValidationInterval: 6000000
rememberMeCookieMaxAge: 2592000
sessionIdCookieMaxAge: -1
kickoutUrl: /login?kickout=1
loginUrl: /login
successUrl: /
filter:
  /favicon.ico = anon;
  /captcha/** = anon;
  /serviceValidate = anon;
  /oauth/** =anon;
  /samlValidate = anon;
  /ownAuthentication = anon;
  /index = anon;
  /login = anon;
  /config/** = anon;
  /exit = anon;
  /third/login = anon;
  /template/** = anon;
  /thirdLogin = anon;
  /users/login = anon;
  /static/css/login/login.css = anon;
  /static/libs/jquery/jquery.min.js = anon;
  /static/libs/crypto-js/crypto-js.js = anon;
  /static/libs/jquery-easyui/jquery.easyui.min.js = anon;
  /static/images/login/back.png = anon;
  /static/element/** = anon;
  /static/** = anon;
  /errorpage/** = anon;
  /publicLogin = anon;
  /sso/ticket = anon;
  /getKey = anon;
  /setLang = anon;
  /getLang = anon;
  /auth = anon;
  /authEncrypt = anon;
  /token/** = anon;
  /token = anon;
  /templates/manage.html = anon;
  /sms/** = anon;
  /loginOfSms = anon;

  /users/** = userInfo;
  /groups/** = userInfo;
  /events/** = userInfo;
  /** = user,log;


