#mybatis
mybatis:
  config-location: file:./config/mybatis-config.xml
  mapper-locations: classpath:/com/fulongtech/uams/mapper/*.xml
spring:
  messages:
    encoding: UTF-8
    basename: static/i18n/message
  redis:
    host: ${uams.redis.host}
    port: ${uams.redis.port}
    password: ${uams.redis.password}
    database: ${uams.redis.database}
  http:
    multipart:
      enabled: true
      max-file-size: -1
      max-request-size: -1
    encoding:
      charset: UTF-8
      force: true
      enabled: true
  rabbitmq:
    host: ${uams.rabbitmq.host}
    port: ${uams.rabbitmq.port}
    username: ${uams.rabbitmq.username}
    password: ${uams.rabbitmq.password}
  #数据库
  datasource:
    key: 12345678
    username: ${uams.datasource.username}
    password: ${uams.datasource.password}
    oracle-url: jdbc:oracle:thin:@//${uams.datasource.host}:${uams.datasource.port}/${uams.datasource.serviceName}
    oracle-driver-class-name: oracle.jdbc.driver.OracleDriver
    sqlserver-url: jdbc:sqlserver://${uams.datasource.host}:${uams.datasource.port};DatabaseName=${uams.datasource.serviceName}
    sqlserver-driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
    mysql-url: jdbc:mysql://${uams.datasource.host}:${uams.datasource.port}/${uams.datasource.serviceName}?useUnicode=true&characterEncoding=UTF8
    mysql-driver-class-name: com.mysql.jdbc.Driver
    timeBetweenEvictionRunsMillis: 600000
    minEvictableIdleTimeMillis:  1800000
    oracleValidationQuery: SELECT 1 FROM DUAL
    sqlServerValidationQuery: SELECT 1
    mysqlValidationQuery: select 1
    testOnBorrow: false
    testWhileIdle: true
    numTestsPerEvictionRun: 3


  #FREEMARKER (FreeMarkerAutoConfiguration)
  freemarker:
    allow-request-override: false
    allow-session-override: false
    cache: false
    charset: UTF-8
    request-context-attribute: request
    check-template-location: true
    content-type: text/html
    enabled: true
    expose-request-attributes: false
    expose-session-attributes: false
    expose-spring-macro-helpers: true
    prefer-file-system-access: true
    suffix: .ftl
    template-loader-path: file:./pages/,classpath:/templates/
    settings:
      template_update_delay: 0
      default_encoding: UTF-8
      classic_compatible: true
    order: 1
  mvc:
    #静态资源映射地址
    static-path-pattern: /static/**
  resources:
    #静态资源路径
    static-locations: file:./pages/static/,classpath:/static/
flyway:
  baseline-on-migrate: true
  locations: classpath:db/migration/${uams.datasource.type}
#mybatis-plus:
#  configuration:
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
mybatis-plus:
  configuration:
    default-fetch-size: 5000
server:
  tomcat:
    max-threads: 1000
    min-spare-threads: 50
    accept-count: 1000
  max-http-header-size: 8192
  connection-timeout: 8000
  session:
    cookie:
      secure: ${uams.security-policy.enabled-gateway-https}
jasypt:
  encryptor:
    password: rzon2022
