create table SYS_USER
(
    ID                  VARCHAR2(32) not null
        constraint SYS_USER_PK
            primary key,
    PASSWORD       VARCHAR2(100),
    USERNAME           VARCHAR2(50),
    NAME       VARCHAR2(50),
    EMAIL          VARCHAR2(100),
    PHONE          VARCHAR2(20),
    MOBILE_PHONE    VARCHAR2(30),
    CHANGE_PWD_TIME TIMESTAMP(6),
    ID_CARD             VARCHAR2(18),
    CREATE_BY           VARCHAR2(32)
);

INSERT INTO SYS_USER ("ID", "PASSWORD", "USERNAME", "NAME", "EMAIL", "PHONE", "MO<PERSON>LE_PHONE", "CHAN<PERSON>_PWD_TIME", "ID_CARD", "CREATE_BY")
VALUES ('BEFE2FF1A20F4C419C8A94B7213C5219', '21232f297a57a5a743894a0e4a801fc3', 'admin', '超级管理员', NULL, NULL, NULL, NULL, NULL, NULL);

create table SYS_GROUP
(
    ID VARCHAR2(32) not null
        constraint GROUP_PK
            primary key,
    PARENT_ID VARCHAR2(32),
    NAME VARCHAR2(50),
    IS_SYS_GROUP NUMBER
);

comment on table SYS_GROUP is '用户组';

insert into SYS_GROUP (ID, PARENT_ID, NAME, IS_SYS_GROUP) values ('BEFE2FF1A20F4C419C8A94B7213C5218', null, '系统管理组', 1);

create table RELATION
(
    ID VARCHAR2(32),
    USER_ID VARCHAR2(32),
    GROUP_ID VARCHAR2(32)
);

comment on table RELATION is '用户角色关系';

insert into RELATION (ID, USER_ID, GROUP_ID) values ('BEFE3FF1A20F4C419C8A94B7213C5217', 'BEFE2FF1A20F4C419C8A94B7213C5219', 'BEFE2FF1A20F4C419C8A94B7213C5218');