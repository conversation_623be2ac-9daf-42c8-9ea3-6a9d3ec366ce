create table SYS_USER
(
    ID                  VARCHAR(32) not null
        constraint SYS_USER_PK
            primary key,
    PASSWORD       VARCHAR(100),
    USERNAME           VARCHAR(50),
    NAME       NVARCHAR(100),
    EMAIL          VARCHAR(100),
    PHONE          VARCHAR(20),
    MO<PERSON>LE_PHONE    VARCHAR(30),
    CHANGE_PWD_TIME  datetime2(6),
    ID_CARD             VARCHAR(18),
    CREATE_BY           VARCHAR(32)
);

INSERT INTO SYS_USER ("ID", "PASSWORD", "USERNAME", "NAME", "EMAIL", "PHONE", "MO<PERSON>LE_PHONE", "CHANGE_PWD_TIME", "ID_CARD", "CREATE_BY")
VALUES ('BEFE2FF1A20F4C419C8A94B7213C5219', '21232f297a57a5a743894a0e4a801fc3', 'admin', '超级管理员', NULL, NULL, NULL, NULL, NULL, NULL);

create table SYS_GROUP
(
    ID VARCHAR(32) not null
        constraint G<PERSON><PERSON>_PK
            primary key,
    PARENT_ID VARCHAR(32),
    NAME NVARCHAR(100),
    IS_SYS_GROUP tinyint
);


insert into SYS_GROUP (ID, PARENT_ID, NAME, IS_SYS_GROUP) values ('BEFE2FF1A20F4C419C8A94B7213C5218', null, '系统管理组', 1);

create table RELATION
(
    ID VARCHAR(32),
    USER_ID VARCHAR(32),
    GROUP_ID VARCHAR(32)
);


insert into RELATION (ID, USER_ID, GROUP_ID) values ('BEFE3FF1A20F4C419C8A94B7213C5217', 'BEFE2FF1A20F4C419C8A94B7213C5219', 'BEFE2FF1A20F4C419C8A94B7213C5218');