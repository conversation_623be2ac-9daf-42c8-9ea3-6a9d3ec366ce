const path = require('path');
const VueLoaderPlugin = require('vue-loader/lib/plugin');
const BundleAnalyzerPlugin = require("webpack-bundle-analyzer").BundleAnalyzerPlugin;
const MiniCssExtractPlugin = require('mini-css-extract-plugin');


module.exports = {
    mode: 'development',
    entry: {
        "main": ["babel-polyfill", "./index.js"],
        "toChangePwd": ["babel-polyfill", "./toChangePwd.js"]
    },
    output: {
        filename: "[name].js",
        path: path.resolve(__dirname, 'dist')
    },
    devServer: {
        contentBase: path.resolve(__dirname, 'dist'),
        port: 8080,
        publicPath: "/UAMS/static/dist/",
        open: false ,
        disableHostCheck: true
    },
    watchOptions: {
        poll: 100,//监测修改的时间(ms)
        aggregateTimeout: 10, //防止重复按键，500毫米内算按键一次
        ignored: /node_modules/,//不监测
    },
    module: {
        rules: [
            {test: /\.css$/, use: [MiniCssExtractPlugin.loader, 'css-loader']},
            {test: /\.vue$/, loader: 'vue-loader'},
            {test: /\.(ttf|eot|svg|woff|woff2)$/, use: 'url-loader'},
            {test: /\.js$/, exclude: /node_modules/, loader: "babel-loader"}
        ]
    },
    plugins: [
        // 请确保引入这个插件！
        new VueLoaderPlugin(),
        new MiniCssExtractPlugin({
            filename:"[name].css"
        })
        //new BundleAnalyzerPlugin()
    ],
    optimization: {
        splitChunks: {
            chunks: "all",
            minSize: 20000,
            minChunks: 1,
            maxInitialRequests: 5,
            cacheGroups: {
                default: { // 模块缓存规则，设置为false，默认缓存组将禁用
                    minChunks: 2, // 模块被引用>=2次，拆分至vendors公共模块
                    priority: -20, // 优先级
                    reuseExistingChunk: true // 默认使用已有的模块
                },
                vendors: {
                    name: "vendor",
                    test: /[\\/]node_modules[\\/]/,
                    priority: -10
                },
                vue: {
                    name: "chunk-vue",
                    priority: 20,
                    test: /[\\/]node_modules[\\/]vue|vue-router|vue-i18n[\\/]/
                },
                elementUI: {
                    name: "chunk-elementUI",
                    priority: 15,
                    test: /[\\/]node_modules[\\/]element-ui[\\/]/
                },
                umyUI: {
                    name: "chunk-umyUI", // 单独将 elementUI 拆包
                    priority: 10, // 权重需大于其它缓存组
                    test: /[\\/]node_modules[\\/]umy-ui|umy-table[\\/]/
                }
            }
        }
    }
};