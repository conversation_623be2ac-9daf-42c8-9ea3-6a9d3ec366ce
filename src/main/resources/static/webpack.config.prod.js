let config = require("./webpack.config.js");
config.mode = "production";
config.output.filename="[name]-[chunkhash:32].js";

const VueLoaderPlugin = require('vue-loader/lib/plugin');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const HtmlWebpackPlugin = require("html-webpack-plugin");

const plugins = [
    new VueLoaderPlugin(),
    new MiniCssExtractPlugin({
        filename:"[name]-[chunkhash:32].css"
    }),
    new HtmlWebpackPlugin({
        filename:"../../templates/managerindex.ftl",
        template:"../templates/main_template.ftl",
        publicPath: "${request.contextPath}/static/dist/",
        minify:false
    }),
    new HtmlWebpackPlugin({
        filename:"../../templates/user/user_changepwd.ftl",
        template:"../templates/main_changePwd_template.ftl",
        publicPath: "${request.contextPath}/static/dist/",
        chunks : ["toChangePwd", "vendor", "chunk-vue", "chunk-elementUI"],
        minify:false
    })
];
config.plugins = plugins;

module.exports = config;
