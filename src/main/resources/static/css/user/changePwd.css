.changePwdPage{
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
}
.changePwd{
    width: 950px;
    height: 332px;
    background: #FFFFFF;
    border-radius: 2px;
    border: 1px solid #D9D9D9;
}
.jumpToPage{
    height: 44px;
    border-bottom: 1px solid #D9D9D9;
    display: flex;
    align-items: center;
    color: #182A4E;
}
.jumpToPage_back{
    margin: 0 8px 0px 16px;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
}
.jumpToPage_back_img{
    /* color: #0854A1; */
    color: #606266;
    font-weight: 1000;
}
.jumpToPage_back:hover{
    /* border: 1px solid #0854A1; */
    border: 1px solid #c6e2ff;
    background: #ebf5fe;
    box-sizing: border-box;
    border-radius: 2px;
}
.jumpToPage_back:active{
    border: 1px solid #409EFF;
    background: #409EFF;
    box-sizing: border-box;
    border-radius: 2px;
    color: #409EFF;
}
.jumpToPage_back:hover .jumpToPage_back_img{
    color: #409EFF;
}
.jumpToPage_back:active .jumpToPage_back_img{
    color: white;
}
.changePwdPageRuleForm{
    margin: 0 16px;
    height: 215px;
    margin-top: 25px;
    width: auto;
}
.changePwdPageFooter{
    border-top: 1px solid #D9D9D9;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
}
.changePwdPageFooter_confirm, .changePwdPageFooter_cancel{
    min-width: 64px;
    height: 32px;
    line-height: 32px;
    padding: 0 5px;
    /* background: #0854A1;
    border: 1px solid #0854A1;
    border-radius: 2px;*/
}
.changePwdPageFooter_cancel{
    /* width: 64px;
    height: 32px;
    background: white;
    color: #0854A1;
    border: 1px solid rgba(0,0,0,0);
    border-radius: 2px;
    line-height: 32px;
    padding: 0; */
    margin-right: 15px;
}
/* .changePwdPageFooter_confirm:hover{
    width: 64px;
    height: 32px;
    background: #114171;
    border: 1px solid #114171;
    border-radius: 2px;
    line-height: 32px;
    padding: 0;
}
.changePwdPageFooter_cancel:hover{
    background: #EBF5FE;
    color: #0854A1;
    border: 1px solid #114171;
} */
.changePwdPage .el-input__inner{
    height: 32px;
    line-height: 32px;
}
.changePwdPage .el-form-item{
    margin-bottom: 30px;
}
.changePwdPage .el-form-item__error{
    padding-top: 0px;
}
.changePwdPage .el-form-item__content{
    padding-left: 15px;
}