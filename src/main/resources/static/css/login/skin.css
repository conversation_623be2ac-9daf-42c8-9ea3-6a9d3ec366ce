/*------------------------------------login----------------------------------*/
html,body{
	height:100%;
	margin:0;
}
.body-img{
	width:100%;
}
.login-body {
    margin: 0 auto;
    width: 100%;
	min-height:100%;
    position: relative;
	background-size: cover;
}

.login-font {
    font-family: ΢���ź�;
    color: #f8f8ff;
    font-weight: bold;
    -ms-text-shadow: 5px 2px 6px #000000;
    text-shadow: 5px 2px 6px #000000;
    background-repeat: no-repeat;
    background-position: 0 0;
    padding-top: 10px;
    position: absolute;
}

.login-title {
    font-size: 44px;
    top: 95px;
    left: 190px;
    -ms-letter-spacing: 5px;
    letter-spacing: 5px;
}

.login-version {
    font-size: 20px;
    top: 140px;
    left: 190px;
}

.login-message {
    color: red;
    text-align: center;
    height: 20px;
}

.login .login-border {
    width: 415px;
    height: 212px;
    position: absolute;
    top: 300px;
    left: 515px;
    text-align: center;
}

    .login .login-border span {
        display: block;
        width: 275px;
        text-align: center;
    }

    .login .login-border div {
        text-align: left;
        margin-top: 15px;
    }

    .login .login-border label {
        font-size: 12px;
        font-weight: bold;
        color: #102c5c;
    }

#saveinfo {
    margin-left: 55px;
}

.login_input {
    width: 180px;
    height: 20px;
    line-height: 20px;
    font-family: "΢���ź�";
    font-size: 12px;
    color: #333;
    border: 1px solid #888888;
}


.login-btn {
    background: url(/static/images/login/login_btn.png);
    width: 89px;
    height: 48px;
    border: 0;
    cursor: pointer;
    margin-top: 15px;
	clear:both;
	float:left;
	margin-left:156px;
}

.bottom {
    font-size: 16px;
    position: absolute;
    bottom: 30px;
    left: 20px;
    font-family: ΢���ź�;
    color: #f8f8ff;
    font-weight: bold;
    -ms-text-shadow: 5px 2px 6px #000000;
    text-shadow: 5px 2px 6px #000000;
    background-repeat: no-repeat;
    background-position: 0 0;
}

/*------------------------------------home----------------------------------*/
.mes-top-bg {
    background-image: url(top-bg.png);
    height: 68px;
    position: relative;
}

.index-font {
    font-family: ΢���ź�;
    color: #f8f8ff;
    font-weight: bold;
    -ms-text-shadow: 1px 1px 1px #000000;
    text-shadow: 1px 1px 1px #000000;
    background-repeat: no-repeat;
    background-position: 0 0;
    position: absolute;
}

.mes-top-title {
    top: 15px;
    left: 90px;
    font-size: 24px;
     -ms-letter-spacing: 4px;
    letter-spacing: 4px;
}

.mes-top-version {
    top: 45px;
    left: 90px;
    font-size: 14px;
}

.menu-position {
    position: absolute;
    bottom: 0;
    right: 0;
}

.mes-userinfo {
    position: absolute;
    top: 10px;
    right: 10px;
    color: #ffffff;
}

    .mes-userinfo label {
        font-weight: bold;
        font-size: 14px;
    }

    .mes-userinfo a {
        color: #FFF;
        text-decoration: none;
    }

        .mes-userinfo a:hover {
            color: #b0c4de;
        }
                .login_box .btn2 {
            background: rgba(0, 0, 0, 0) url("/static/images/login/login_btn2.png") repeat scroll 0 0;
            color: #000;
            float: left;
            font-family: "Microsoft YaHei",sans-serif;
            font-size: 18px;
            height: 33px;
            line-height: 33px;
            text-align: center;
            text-decoration: none;
            width: 180px;
            margin-top: 30px;
            margin-left: 40px;
        }
p{margin:0px;padding:0px;}
.login-info{
    font-family:"΢���ź�"; 
    color:#FFF; 
    font-weight:bold;
    font-size:40px;
    position: absolute;
    top: 150px;
    left: 31%;
    margin-left: -300px;
}
.login-box{
	position:absolute;
	font-family:"΢���ź�";
    width: 30%;
    height: 30%;
    left: 40%;
    top: 53%;
}
.login-box p{float:left;margin-top:15px;clear:both; margin-left:100px;}
.login-box p input[type=text]{width:150px;height:22px;}
.login-box p input[type=password]{width:150px;height:22px;}
.login-box p label{font-size:12px; line-height:21px; font-weight:bold;}
.login-box p.SaveAccountCheckbox input{float:left;margin-top:3px; margin-left:49px;}
.login-box p.SaveAccountCheckbox label{float:left;font-weight:normal;}
.login-btn2{background: url("../../images/login/login_btn2.png") top no-repeat; width:180px; height:33px;border:none; margin-bottom:20px; font-size:14px; font-weight:bold;font-family: "΢���ź�";}
#errorTip{color:red;font-size: 18px;position: absolute;top: -50px;margin-left: 105px;}