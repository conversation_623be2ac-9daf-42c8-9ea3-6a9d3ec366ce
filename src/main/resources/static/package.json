{"name": "uams", "version": "2.0.0", "description": "uams", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "build": "webpack --config webpack.config.prod.js", "genJavaAPIDoc": "apidoc -i ../../../src/main/java/com/ -o ../../apidoc -c ../config/apidoc-conf/apidoc.json", "start": "webpack-dev-server"}, "author": "", "license": "ISC", "dependencies": {"axios": "^0.19.2", "babel-polyfill": "^6.26.0", "el-select-tree": "^1.0.23", "element-ui": "^2.13.2", "lodash": "^4.17.19", "umy-ui": "^1.0.9", "vue": "^2.6.11", "vue-router": "^3.3.4", "vue-i18n": "^8.5.0"}, "devDependencies": {"@babel/core": "^7.11.1", "@babel/preset-env": "^7.11.0", "apidoc": "^0.24.0", "babel-loader": "^8.1.0", "babel-plugin-component": "^1.1.1", "css-loader": "^3.6.0", "html-webpack-plugin": "^4.5.0", "mini-css-extract-plugin": "^1.3.3", "style-loader": "^1.2.1", "url-loader": "^4.1.0", "vue-loader": "^15.9.3", "vue-template-compiler": "^2.6.11", "webpack": "^4.43.0", "webpack-bundle-analyzer": "^4.3.0", "webpack-cli": "^3.3.12", "webpack-dev-server": "^3.11.0"}}