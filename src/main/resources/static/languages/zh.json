{"app": {"users": "用户", "groups": "用户组", "logout": "注销", "change-password": "修改密码", "message": {"login-expire": "您的登录已过期，3秒后将跳转到登录页面", "no-permission": "您的账户没有权限"}}, "changPassWord": {"oldPassword": "旧密码", "newPassword": "密码", "confirmPassword": "确认密码", "confirm": "确认", "cancel": "取消", "ruleForm-oldPassword": "请输入旧密码密码!", "ruleForm-newPassword": "请输入新密码!", "ruleForm-confirmPassword": "请再次输入新密码!", "ruleForm-passWordRule": "口令长度至少为8位，最长20位，且为数字、大写字母、小写字母和特殊符号中至少三类的组合，数字、字母和特殊符号(~!@#%^&*_.-)!", "ruleForm-confirmPassword-error": "两次输入密码不一致!", "confirm-error": "旧密码错误!", "confirm-success": "密码修改成功，请重新登录！"}, "users": {"emptyText": "没有检索结果", "add": "新增", "import": "导入", "export": "导出", "username": "用户名", "name": "姓名", "idCard": "身份证", "Administrator": "超级管理员", "email": "邮箱", "mobilePhone": "手机号", "phone": "座机号", "save": "保 存", "cancel": "取 消", "addUserSuccess": "添加用户成功", "updateUserSuccess": "修改用户成功", "deleteUserSuccess": "删除成功！", "searchText": "请输入用户名、姓名", "selectTree": "所有用户组", "loadingUser": "正在加载用户列表", "number": "序号", "operation": "操作", "edit": "编辑", "grouping": "分配用户组", "delete": "删除", "resetPassword": "重置密码", "resetPasswordSuccess": "重置密码成功", "resetPasswordTitle": "此操作将重置用户密码，请问是否继续！", "notAssignGroup": "未分配用户组用户", "modifyUser": "修改用户", "addUser": "添加用户", "delUserWarnStart": "此操作将永久删除该用户：", "delUserWarnEnd": "，是否继续？", "confirm": "确定", "prompt": "提示", "AssignGroupSuccess": "分配用户组成功!", "importUsers": {"previous": "用户管理", "present": "用户导入", "downloadTemplate": "下载模板", "uploading": "正在上传用户文件", "upload": "上传", "drag": "将文件拖到此处，或点击上传", "restrict": "只能上传xls/xlsx文件", "error": "错误提示", "warn": "警告信息", "warning": "警告", "exitMessage": "退出该页面之后日志信息不会被保存，确定要退出吗?", "log": "操作信息", "header": "表头", "permission": "权限", "indexStart": " 第 ", "indexEnd": " 行 ", "noImportPermission": "您的用户没有用户导入的权限，无法访问本页面。系统将在3秒后跳转到主页面。"}, "exportUsers": {"selectAll": "全选", "selectedUser": "已选择", "notSelectedUser": "未选择", "toTheLeft": "到左边", "toTheRight": "到右边", "exportUserInformation": "导出用户信息", "UserInfoTable": "用户信息表", "ExportUserInfoSuccess": "导出用户信息成功"}, "validate": {"name": {"content": "不能含有特殊字符", "length": "姓名长度需要在2-20个字符之间", "notNull": "姓名不能为空"}, "username": {"content": "用户名只能含有数字和字母", "length": "用户名长度需要在2-20个字符之间", "notNull": "用户名不能为空", "hasSpace": "用户名不能包含空格"}, "mobilePhone": {"empty": "手机号码不能为空", "correct": "请输入合法的手机号"}, "idCard": "请输入18位的身份证号"}}, "groups": {"fake": "占位", "head": {"deleteButton": {"buttonName": "删除", "warnMessage": {"context": "请选择需要删除的用户组！"}, "successMessage": {"context": "删除用户组成功！"}, "dialog": {"hint": "提示", "context": "您将会删除选中的用户组，和他的孩子用户组！ 确定要继续吗？", "confirmButton": "确定", "cancelButton": "取消"}}, "addButton": {"buttonName": "添加"}, "search": {"warnMessage": "请输入要搜索的内容"}, "searchPlaceholder": "组名"}, "context": {"tableName1": "组名", "tableName2": "操作", "operation": {"edit": {"title": "编辑", "dialog": {"title": "修改用户组名", "context": {"groupName": "用户组名", "submitButton": "确定"}}, "successMessage": {"context": "修改成功!"}}, "add": {"title": "添加用户组"}, "delete": {"title": "删除", "emptyDialog": {"context": "请选择一个需要删除的用户组！"}, "dialog": {"Hint": "提示", "context": "你将会删除该用户组，请问需要继续吗？", "confirm": "确认", "cancel": "取消"}}}}, "addGroupDialog": {"title": "添加用户组", "parentGroupName": "父组名", "groupName": "组名", "submit": "提交", "cancel": "取消", "noParent": "无父组", "message": {"success": {"context": "添加用户组成功"}, "error": {"pre-context": "用户组：", "order-context": "，已经存在", "characterValid": "用户组名不能含有特殊字符(~!@$%^&*()+={}':;',[].<>?~！@￥%……&*（）——+{}【】‘；：”“’。，、？)", "lengthValid": "长度不能超过20个字符"}, "systemError": {"context": "系统错误，请重试！"}}}, "deleteGroup": {"message": {"title": "提示", "context": "此操作将永久删除所选用户组及其子用户组，请确定是否继续？", "error": {"context": "请选择需要删除的用户组！"}, "success": {"context": "删除用户组成功"}}}, "validate": {"name": {"content": "请输入用户组名", "length": "长度不能超过50个字符"}}}, "personal": {"title": "个人信息中心", "context": {"userInfo": {"username": "用户名：", "name": "姓名：", "identity": "身份证：", "email": "邮箱：", "phone": "手机号：", "mobilePhone": "座机号："}}}}