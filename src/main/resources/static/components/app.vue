<template>
    <div style="height: 100%">
        <el-container style="height: 100%">
            <el-aside width="auto">
                <el-menu class="uams-nav"
                         :router="true"
                         :default-active="$router.history.current.name"
                         :collapse="true">
                    <el-menu-item index="/user" :class="{active:$router.history.current.name==='用户管理'}">
                        <i class="el-icon-user"></i>
                        <span slot="title">{{ $t('app.users') }}</span>
                    </el-menu-item>
                    <el-menu-item index="/group" :class="{active:$router.history.current.name==='用户组管理'}">
                        <i class="el-icon-uams-group"></i>
                        <span slot="title">{{ $t('app.groups') }}</span>
                    </el-menu-item>
                </el-menu>
                <userIcon class="uams-user-tip-position"></userIcon>
            </el-aside>
            <el-main>
                <router-view class="uams-router-view"></router-view>
            </el-main>
        </el-container>
    </div>
</template>

<script>
import userIcon from "./user_icon.vue";
import CONSTANT from "../js/constant";
window.CONSTANT = CONSTANT;

export default {
    components: {
        userIcon
    }
};
</script>

<style scoped>
.el-container {
    /*设置内部填充为0，几个布局元素之间没有间距*/
    padding: 0px;
    /*外部间距也是如此设置*/
    margin: 0px;
    /*统一设置高度为100%*/
    height: 100%;
    width: 100%;
}
.el-main{
    padding: 0px;
    height: 100%;
}

.uams-nav {
    height: 100%;
    background-color: #f5f5f5;
}

.uams-router-view{
    padding: 20px;
}

.uams-user-tip-position{
    position: fixed;
    left: 10px;
    bottom: 15px;
}

.active {
    color: #409EFF;
    background-color: #ecf5ff;
    border-right-color: #409EFF;
    border-right-width: 2px;
    border-right-style: solid;
}
</style>