<template>
    <el-form :model="group" status-icon size="medium" :inline="formInline" :rules="add_rules" ref="addGroupForm" label-width="auto">
        <el-row v-show="!isEditMode">
	        <el-col :span=24>
		        <el-form-item span="8" :label="$t('groups.addGroupDialog.parentGroupName')" prop="">
			        <label autocomplete="off">{{
					        group.parent === null ? $t('groups.addGroupDialog.noParent') : group.parent.name
				        }}</label>
		        </el-form-item>
	        </el-col>
        </el-row>
        <el-row>
	        <el-col :span=24>
		        <el-form-item span="8" :label="$t('groups.addGroupDialog.groupName')" prop="name">
			        <el-input wo type="primary" v-model="group.name" autocomplete="off"></el-input>
		        </el-form-item>
	        </el-col>
        </el-row>
        <el-row v-show="!isEditMode">
	        <el-col :span=24 align="right">
		        <el-form-item  prop="">
			        <el-button type="primary" @click="saveGroup()" :disabled="submitBtDisabled">{{
					        $t('groups.addGroupDialog.submit')
				        }}
			        </el-button>
			        <el-button @click="handleClose">{{ $t('groups.addGroupDialog.cancel') }}</el-button>
		        </el-form-item>
	        </el-col>
        </el-row>
        <el-row v-show="isEditMode">
	        <el-col  :span=24 align="right">
		        <el-form-item  prop="">
			        <el-button type="primary" @click="updateGroup" :disabled="submitBtDisabled">
				        {{ $t('groups.context.operation.edit.dialog.context.submitButton') }}
			        </el-button>
		        </el-form-item>
	        </el-col>
        </el-row>
    </el-form>
</template>

<script>

import axios from "axios";

const characterSymbol = new RegExp("[`~!@#$^&*()=|{}':;',\\[\\].<>《》/?~！@#￥……&*（）——|{}【】‘；：”“'。，、？ ]")
const validateGroupName = function (rule, value, callback) {
    if (value === '') {
        callback(new Error(i18n.t('groups.validate.name.content')));
    } else if (value.length > 50) {
        callback(new Error(i18n.t('groups.validate.name.length')));
    }
    // else if (characterSymbol.test(value)) {
    //     callback(new Error(i18n.t('users.validate.name.content')));
    // }
    callback();
}

export default {
    name: "group_editor",
    props: ["group"],
    watch: {
      "group": {
        immediate: true,
        handler: function (group, old) {
          console.log(old);
                  this.submitBtDisabled = false;
          if (group) {
            this.$nextTick(()=>{
              this.$refs['addGroupForm'].clearValidate();
            })
          }
        }
      }
    },
    data: function () {
        return {
            submitBtDisabled : false,
            add_rules: {
                name: [
                    {validator: validateGroupName, trigger: 'blur'},
                ],
            },
	        formInline:(navigator.userAgent.indexOf("compatible") > -1 && navigator.userAgent.indexOf("MSIE") > 1 || navigator.userAgent.indexOf("rv:11.0") > -1)
        }
    },
    computed: {
        isEditMode: function () {
            if (this.group.id && this.group.id !== "") {
                return true;
            }
            return false;
        }
    },
    methods: {
        handleClose() {
            this.$emit("close");
            this.submitBtDisabled = false;
        },
        saveGroup() {
            const $this = this;
            $this.submitBtDisabled = true;
            $this.$refs['addGroupForm'].validate(function (valid) {
                if (!valid) {
                    $this.submitBtDisabled = false;
                    return false;
                }
                if ($this.group.name !== "") {
                    axios.post("groups", $this.group).then(function (response) {
                        $this.$message.success({
                            message: $this.$t('groups.addGroupDialog.message.success.context'),
                            center: true
                        });
                        $this.$emit("success");
                        $this.$emit("close");
                    }).catch(function (error) {
                        $this.submitBtDisabled = false;
                        $this.$message.error({message: error.response.data.message, center: true});
                    })
                }
            });
        },
        updateGroup() {
            const $this = this;
            $this.submitBtDisabled = true;
            $this.$refs['addGroupForm'].validate(function (valid) {
                if (!valid) {
                    $this.submitBtDisabled = false;
                    return false;
                }
                var data = {
                    "id": $this.group.id,
                    "name": $this.group.name
                };
                axios.put("groups/info", $this.group).then(response => {
                    if (response.data.name === data.name) {
                        $this.$emit("success");
                        $this.$emit("close");
                        $this.$message.success({
                            message: $this.$t('groups.context.operation.edit.successMessage.context'),
                            center: true
                        });
                        axios.get('groups').then(function (res) {
                            $this.originalGroup = res.data;
                        });
                    }
                }).catch(function (error) {
                    $this.submitBtDisabled = false;
                    $this.$message.error({message: error.response.data.message, center: true});
                });
            });
        },

    }
}
</script>

<style scoped>

</style>