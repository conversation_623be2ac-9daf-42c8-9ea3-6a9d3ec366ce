<template>
    <div class="userInfoClass">
        <el-row :gutter="20">
            <el-col :span="12">
                {{ $t('personal.context.userInfo.username') }}<label>{{ sharedState.currentUser.username }}</label>
            </el-col>
            <el-col :span="12">
                {{ $t('personal.context.userInfo.name') }}<label>{{ sharedState.currentUser.name }}</label>
            </el-col>
        </el-row>
        <el-row :gutter="20">
            <el-col :span="12">
                {{ $t('personal.context.userInfo.identity') }}<label>{{ sharedState.currentUser.idCard }}</label>
            </el-col>
            <el-col :span="12">
                {{ $t('personal.context.userInfo.email') }}<label>{{ sharedState.currentUser.email }}</label>
            </el-col>
        </el-row>
        <el-row :gutter="20">
            <el-col :span="12">
                {{ $t('personal.context.userInfo.phone') }}<label>{{ sharedState.currentUser.mobilePhone }}</label>
            </el-col>
            <el-col :span="12">
                {{ $t('personal.context.userInfo.mobilePhone') }}<label>{{ sharedState.currentUser.phone }}</label>
            </el-col>
        </el-row>
    </div>
</template>

<script>
import store from "../js/store";

export default {
    name: "user_detail",
    data:function(){
        return {
            sharedState:store.state
        }
    }
}
</script>

<style scoped>
.userInfoClass .el-row {
    margin-bottom: 20px;
    font-size: medium;
}
</style>