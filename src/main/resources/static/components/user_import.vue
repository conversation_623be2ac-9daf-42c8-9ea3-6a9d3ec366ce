<template>
	<div class="container">
		<el-container>
			<el-header class="header">
				<el-row :gutter="24" type="flex">
					<el-col :span="12">
						<el-breadcrumb separator="/">
							<el-breadcrumb-item :to="{ path: '/user' }">{{ $t('users.importUsers.previous') }}
							</el-breadcrumb-item>
							<el-breadcrumb-item>{{ $t('users.importUsers.present') }}</el-breadcrumb-item>
						</el-breadcrumb>
					</el-col>
					<el-col :span="12">
						<el-link href="static/template/usersTemplate.xlsx" style="float: right;" type="primary">
							{{ $t('users.importUsers.downloadTemplate') }}
						</el-link>
					</el-col>
				</el-row>
			</el-header>
			<el-main class="main">
				<el-row :gutter="20" justify="center" type="flex" align="middle" v-if="this.fileList.length>0">
					<el-col :xl="{span:10,offset:0}" :lg="{span:12,offset:0}" :md="{span:14,offset:0}"
					        :sm="{span:16,offset:0}" :xs="{span:20,offset:0}">
						<ul class="el-upload-list el-upload-list--text">
							<li tabindex="0" class="el-upload-list__item is-ready">
								<a class="el-upload-list__item-name"><i class="el-icon-document"></i>
									{{ fileList[0].name }}
								</a>
								<el-button class="uploadBtn" size="mini" :disabled="isDisabled" type="success"
								           v-loading.fullscreen.lock="fullscreenLoading"
								           :element-loading-text="$t('users.importUsers.uploading')"
								           element-loading-spinner="el-icon-loading"
								           @click="submitUpload">{{ $t('users.importUsers.upload') }}
								</el-button>
								<label class="el-upload-list__item-status-label"><i
									class="el-icon-upload-success el-icon-circle-check"></i></label>
								<i class="el-icon-close" @click="onRemove"></i>
							</li>
						</ul>
					</el-col>
				</el-row>
				<el-row v-show="fileList.length==0" :class="uploader" :gutter="20" justify="center" type="flex"
				        align="middle">
					<el-col :xl="{span:10,offset:4}" :lg="{span:12,offset:4}" :md="{span:14,offset:3}"
					        :sm="{span:16,offset:3}" :xs="{span:20,offset:0}">
						<form ref="uploadForm">
							<el-upload
								style="margin: 10px;"
								class="upload-demo"
								ref="upload"
								action="users/upload"
								:show-file-list="false"
								:auto-upload="false"
								:file-list="fileList"
								:before-upload="beforeUpload"
								:on-change="onChange"
								:before-remove="onRemove"
								:on-success="onSuccess"
								:on-error="onError"
								:drag="true"
								:multiple="false">
								<i class="el-icon-upload"></i>
								<div class="el-upload__text">{{ $t('users.importUsers.drag') }}</div>
								<div class="el-upload__tip" slot="tip">{{ $t('users.importUsers.restrict') }}
								</div>
							</el-upload>
						</form>
					</el-col>
				</el-row>
				<el-row :gutter="20" justify="center" type="flex" align="middle">
					<el-col :xl="{span:10,offset:0}" :lg="{span:12,offset:0}" :md="{span:14,offset:0}"
					        :sm="{span:16,offset:0}" :xs="{span:20,offset:0}">
						<span>{{ result }}</span>
						<el-tabs :stretch="true" v-if="hasLogs" v-model="activeName">
							<el-tab-pane :label="$t('users.importUsers.error')" name="error" class="message">
								<div style="width: 100%; ">
									<el-container>
										<el-aside style="width:150px; height: 500px">
											<el-menu>
												<div v-if="data.error.HEADER">
													<el-menu-item index="1" @click="showError(data.error.HEADER,-1)"
													              :class="{'active':errorIndex==-1}">
														{{ $t('users.importUsers.header') }}
													</el-menu-item>
												</div>
												<div v-if="data.error.PERMISSION">
													<el-menu-item index="2" @click="showError(data.error.PERMISSION,-2)"
													              :class="{'active':errorIndex==-2}">
														{{ $t('users.importUsers.permission') }}
													</el-menu-item>
												</div>
												<div v-for="(error,index) in data.error">
													<el-menu-item v-if="index !== 'PERMISSION' && index !== 'HEADER'"
													              :index="index" @click="showError(error,index)"
													              :class="{'active':errorIndex==index}">
														{{ $t('users.importUsers.indexStart') }} {{ index }}
														{{ $t('users.importUsers.indexEnd') }}
													</el-menu-item>
												</div>
											</el-menu>
										</el-aside>
										<el-main>
											<div class="rightLog">
												<p style="text-align: left;" v-for="(error,index) in error">
													{{ index + 1 }}. {{ error }}
												</p>
											</div>
										</el-main>
									</el-container>
								</div>
							</el-tab-pane>
							<el-tab-pane :label="$t('users.importUsers.warn')" name="warning" class="message">
								<div style="width: 100%;">
									<div class="logs">
										<p style="text-align: left;" v-for="(warn,index) in data.warn.HEADER">
											{{ index + 1 }}. {{ warn }}
										</p>
									</div>
								</div>

							</el-tab-pane>
							<el-tab-pane :label="$t('users.importUsers.log')" name="info" class="message">
								<div class="logs">
									<p style="text-align: left;" v-for="(info,index) in data.info">{{ index + 1 }}. {{
											info
										}} </p>
								</div>
							</el-tab-pane>
						</el-tabs>
					</el-col>
				</el-row>
			</el-main>
		</el-container>
    <el-dialog
        :visible="!sharedState.currentUser || (!sharedState.currentUser.isAdmin && !sharedState.currentUser.isManager)"
        :fullscreen="true"
        :show-close="false"
        :close-on-press-escape="false"
        :close-on-click-modal="false"
        :center="true"
        :modal-append-to-body="false">
		<div style="text-align: center">{{ $t("users.importUsers.noImportPermission") }}</div>

    </el-dialog>
	</div>
</template>

<script>
import store from "../js/store";
import axios from "axios";

export default {
	name: "user_import",
	data: function () {
		return {
			sharedState: store.state,
			haveLog: false,
			fullscreenLoading: false,
			multiple: false,
			isDisabled: false,
			file: null,
			fileList: [],
			data: null,
			error: [],
			warn: [],
			infoLogs: "提示:",
			result: "",
			activeName: "error",
			hasLogs: false,
			uploader: "middle",
			errorIndex: 0,
			warnIndex: 0
		}
	},
	methods: {
		submitUpload: function () {
			let groups = this.sharedState.currentUser.groups;
			if (groups == null || groups.length == 0) {
				this.$message.error({message: i18n.t("app.message.no-permission"), center: true});
				return;
			}
			for (let i = 0; i < groups.length; i++) {
				if (groups[i].name == "系统管理组" || groups[i].id == "BEFE2FF1A20F4C419C8A94B7213C5218") {
					this.$refs.upload.submit();
					return;
				}
			}
			this.$message.error({message: i18n.t("app.message.no-permission"), center: true});
		},
		onChange: function (file, fileList) {
			var that = this;
			let suffix = file.name.substring(file.name.lastIndexOf('.') + 1).toLowerCase();
			if (suffix != 'xls' && suffix != 'xlsx') {
				that.$message.error(this.$t('users.importUsers.restrict'))
				that.isDisabled = false;
				return false;
			}
			this.fileList = [file];
			if (file.status === "ready") {
				this.result = "";
			}
			this.uploader = "top";
			document.getElementsByClassName("el-upload-dragger")[0].style.display = "none";
		},
		onRemove: function (file, fileList) {
			this.hasLogs = false;
			this.result = null;
			this.fileList = [];
			this.error = [];
			this.warn = [];
			this.uploader = "middle";
			this.haveLog = false;
			this.$refs.uploadForm.reset();
			document.getElementsByClassName("el-upload-dragger")[0].style.display = "block";
		},
		beforeUpload:async function (file) {
			var that = this;
			that.isDisabled = true;
			that.result = "";
			that.hasLogs = false;
			//在上传之前调一个查询接口，通过axios的interceptors拦截401，进行被踢出跳转。
			//todo this.$refs.upload.submit(),此组件的拦截方法暂时没找到，找到更好的解决办法再解决这个问题。
			var isKicked = await axios.get("users/currentUser").then(function (res){
				if (res.status === 200) {
					that.fullscreenLoading = true;
					return true;
				}else{
					return false;
				}
			});
			return isKicked;
		},
		onSuccess: function (response, file, fileList) {
			this.data = response;
			this.result = "本次上传，新增 " + response.addNum + "条 ， 更新 " + response.updateNum + "条 ， 失败 " + response.failNum + "条";
			this.isDisabled = false;
			this.hasLogs = true;
			this.fullscreenLoading = false;
			this.checkLogs();
			this.haveLog = true;
		},
		checkLogs: function () {
			let $this = this;
			if ($this.data.error.HEADER) {
				$this.error = $this.data.error.HEADER;
				this.errorIndex = -1;
			} else if ($this.data.error.PERMISSION) {
				$this.error = $this.data.error.PERMISSION;
				this.errorIndex = -2;
			} else {
				for (let errorKey in $this.data.error) {
					$this.error = $this.data.error[errorKey];
					this.errorIndex = errorKey;
					break;
				}
			}
			if ($this.data.warn.HEADER) {
				$this.warn = $this.data.warn.HEADER;
				this.warnIndex = -1;
			} else if ($this.data.warn.PERMISSION) {
				$this.warn = $this.data.warn.PERMISSION;
				this.warnIndex = -2;
			} else {
				for (let warnKey in $this.warn.error) {
					$this.warn = $this.data.warn[warnKey];
					this.warnIndex = warnKey;
					break;
				}
			}
			this.activeName = "error";
		},
		onError: function (err) {
			this.fullscreenLoading = false;
			this.isDisabled = false;
			let obj = JSON.parse(err.message);
			this.$message.error(obj.message);
			this.$refs.upload.clearFiles();
		},
		showError: function (error, index) {
			this.error = [];
			this.errorIndex = 0;
			let $this = this;
			setTimeout(function () {
				$this.error = error;
				$this.errorIndex = index;
			}, 100);

		}
	},
	beforeCreate: function ()  {
		const currentUser = store.state.currentUser;
		if(!currentUser || (!currentUser.isAdmin && !currentUser.isManager)){
      //this.$router.replace({path:"/user"});
      const that = this;
      window.setTimeout(function(){
        that.$router.replace({path:"/user"});
      },3000)
    }
		return;
	},
	beforeRouteLeave(to, form, next) {
		if (this.haveLog) {
			this.$confirm(i18n.t('users.importUsers.exitMessage'), i18n.t('users.importUsers.warning'), {
				confirmButtonText: i18n.t('groups.context.operation.delete.dialog.confirm'),
				cancelButtonText: i18n.t('groups.context.operation.delete.dialog.cancel'),
				type: 'warning'
			}).then(() => {
				next();
				this.haveLog = false;
			});
		} else {
			next();
			this.haveLog = false;
		}
	}
}
</script>

<style scoped>
.container {
	height: 94%;
}

.header {
	padding: 0;
}

.main {
	overflow: hidden;
	padding: 0px;
}

.logs {
	overflow: auto;
	width: 100%;
	max-height: 500px;
}

.middle {
	height: 100%;
}

.top {
	height: 80px;
}

.leftLog {
	width: 50%;
	height: 100%;
	display: block;
	float: left;
	max-height: 500px;
	overflow: auto
}

.rightLog {
	display: block;
	float: left;
	margin-left: 100px;
	max-height: 500px;
	overflow: auto
}

.message {
	height: 500px;
	overflow-y: hidden;
	overflow-x: hidden;
}

.item {
	margin-top: 10px;
	margin-bottom: 20px;
}

.el-upload-list__item-name .is-ready {
	text-align: left !important;
}


.el-upload-list__item-name {
	width: 80%;
	-webkit-box-direction: normal;
	list-style: none;
	font-size: 14px;
	height: 30px;
	line-height: 30px;
	color: #606266;
	display: block;
	margin-right: 40px;
	margin-left: 0px;
	overflow: hidden;
	padding-left: 4px;
	text-overflow: ellipsis;
	transition: color .3s;
	white-space: nowrap;
}

.el-upload-list__item .el-icon-close {
	margin-right: 90px;
}

.uploadBtn {
	list-style: none;
	font-size: 14px;
	font-style: normal;
	font-variant: normal;
	text-transform: none;
	line-height: 1;
	position: absolute;
	top: 0px;
	right: 0px;
	cursor: pointer;
	color: #606266;
}

.el-icon-close {
	line-height: 20px;
	margin-right: 60px;
}

.el-menu {
	border: none;
}

.active {
	background-color: #ecf5ff;
	font-weight: 500;
	font-size: 14px;
	-webkit-box-direction: normal;
	color: #66b1ff;
}

::-webkit-scrollbar {
	height: 10px;
	width: 10px;
	background: transparent;
}

::-webkit-scrollbar-thumb {
	border-radius: 5px;
	border-style: dashed;
	background-color: rgba(205, 205, 205, 0.7);
	border-color: transparent;
	border-width: 1.5px;
	background-clip: padding-box;
}

::-webkit-scrollbar-thumb:hover {
	background: rgba(177, 183, 188, 0.7);
}

::-webkit-scrollbar-corner {
	display: none;
}

#container .el-dialog--center .el-dialog__body{
	text-align: center !important;
}
</style>