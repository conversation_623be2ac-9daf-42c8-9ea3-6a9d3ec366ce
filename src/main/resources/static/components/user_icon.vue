<template>
    <div>
        <el-dropdown @command="handleCommand">
            <span class="el-dropdown-link">
                <el-avatar> {{ sharedState.currentUser.getUserTip() }} </el-avatar>
            </span>
            <el-dropdown-menu slot="dropdown">
                <el-dropdown-item :disabled="true">
                    <span>{{ sharedState.currentUser.username }}</span>
                </el-dropdown-item>
                <el-dropdown-item :divided="true" command="openUserInfoPage">
                    <span>{{ $t('personal.title') }}</span>
                </el-dropdown-item>
                <el-dropdown-item command="changePassword">
                    <span>{{ $t('app.change-password') }}</span>
                </el-dropdown-item>
                <el-dropdown-item command="logout">
                    <span>{{ $t('app.logout') }}</span>
                </el-dropdown-item>
            </el-dropdown-menu>
        </el-dropdown>
        <el-dialog :title="$t('personal.title')"
                   :close-on-click-modal="false"
                   :append-to-body="true"
                   :visible.sync="userDetailVisible" :before-close="handleClose" width="30%" top="15%">
            <userDetail></userDetail>
        </el-dialog>
    </div>
</template>

<script>
import userProvider from "../js/user_provider";
import store from "../js/store";
import userDetail from "./user_detail.vue";

export default {
    name: "user_icon",
    data: function () {
        return {
            userDetailVisible: false,
            sharedState: store.state
        }
    },
    methods: {
        handleCommand: function (command) {
            switch (command) {
                case "openUserInfoPage" :
                    this.openUserDetailDialog();
                    break;
                case "logout":
                    this.logout();
                    break;
                case "changePassword":
                    this.toChangePassword();
                    break;
                default:
                    ;
            }
        },
        handleClose: function () {
            this.userDetailVisible = false;
        },
        openUserDetailDialog: function () {
            this.userDetailVisible = true;
            userProvider.requestCurrentUser().then(user => {
                store.setCurrentUser(user);
            });
        },
        logout() {
            window.location.replace(this.getAbsoluteURL() + "exit");
        },
        toChangePassword() {
            window.location.replace(this.getAbsoluteURL() + "users/toChangePwd");
        },
        getAbsoluteURL: function () {
            return window.location.protocol + "//" + window.location.host + window.location.pathname;
        }
    },
    components: {
        userDetail
    }
}
</script>

<style scoped>

.el-dropdown-link {
    cursor: pointer;
    color: #409EFF;
}

.el-avatar--circle {
    border: 3px solid transparent;
}

.el-avatar--circle:hover {
    border-color: #409EFF;
    border-style: solid;
    border-right-width: 3px;
}
</style>