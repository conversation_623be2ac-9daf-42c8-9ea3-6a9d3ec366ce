"use strict";

const characterSymbol = new RegExp("[`~!@$^&*()=|{}':;',\\[\\]\\\\.<>《》?~！@￥……&*（）——|{}【】‘；：”“'。，、？ ]");
const reg = new RegExp("[а-яА-Я.0-9a-zA-Z-а]+$");

function validateUsername(rule, value, callback) {
    if (value && value.length > 0) {
        if (!reg.test(value)) {
            callback(new Error(i18n.t('users.validate.username.content')));
        } else if (value.length > 20 || value.length <2) {
            callback(new Error(i18n.t('users.validate.username.length')));
        }
        if (value.indexOf(' ') > -1) {
            callback(new Error(i18n.t('users.validate.username.hasSpace')));
        }
    } else {
        callback(new Error(i18n.t('users.validate.username.notNull')));
    }
    callback();

};


function validateName(rule, value, callback) {
    if (value && value.length > 0) {
        if (characterSymbol.test(value)) {
            callback(new Error(i18n.t('users.validate.name.content')));
        } else if (value.length > 20 || value.length <2) {
            callback(new Error(i18n.t('users.validate.name.length')));
        }
    } else {
        callback(new Error(i18n.t('users.validate.name.notNull')));
    }
    callback();
};

function validateIdCard(rule, value, callback) {
    if (value) {
        if (value.length > 0 && value.length !== 18) {
            callback(new Error(i18n.t('users.validate.idCard')));
        }
    }
    callback();

}

let rules = {
    username: [
        {validator: validateUsername, trigger: 'blur', required: true}
    ],
    name: [
        {validator: validateName, trigger: 'blur', required: true}
    ],
    idCard: [
        {validator: validateIdCard, trigger: 'blur'}
    ]
};

module.exports = rules;