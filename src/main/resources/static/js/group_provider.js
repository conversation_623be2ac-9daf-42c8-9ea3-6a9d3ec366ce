"use strict";
const Group = require("./group");
const axios = require("axios");
const _ = require("lodash");

class GroupProvider {
    constructor() {
    }

    createGroup(options, parentGroup) {
        let group = new Group();
        if (options !== undefined) {
            group = _.assign(group, options);
        }
        if (parentGroup instanceof Group) {
            group.setParent(parentGroup);
        }
        return group;
    }

    requestGroup() {

    }

    requestGroups() {
        const that = this;
        return axios.get("groups").then(function (response) {
            if (response.status === 200) {
                if (_.isArray(response.data)) {
                    let groups = [];
                    _.forIn(response.data,function(groupData){
                        let group = that.createGroup(groupData);
                        groups.push(group);
                    });
                    return groups;
                }
            }
            return [];
        }).catch(function () {
            return Error("获取用户失败");
        });
    }

    adaptPidToChildren(groups, sharedStore){
        const origin = _.cloneDeep(groups);
        const result = [];
        if(!_.isArray(groups)){
            return result;
        }
        let map = {};
        origin.forEach(item => {
            map[item.id] = item;
        });
        origin.forEach(item => {
            let parent = map[item.parentId];
            if (parent) {
                (parent.children || (parent.children = [])).push(item);
                if (parent.isHavePower === true)
                    item.isHavePower = true;
            } else {
                //为全选做准备
                item.isAllSelect = false;
                result.push(item);
            }
            if (sharedStore === undefined || sharedStore.currentUser === undefined){
                return ;
            }
            let currentUser =  sharedStore.currentUser;
            let managedGroups = sharedStore.currentUser.managedGroups;
            if (currentUser.roleType === CONSTANT.ADMIN || currentUser.roleType === CONSTANT.SYSTEM_ADMINISTRATOR){
                if (!item.sysGroup){
                    item.isHavePower = true;
                }
            } else if (currentUser.roleType === CONSTANT.USER_GROUP_ADMINISTRATOR){
                if (!item.sysGroup){
                    for (let i = 0; i < managedGroups.length; i++) {
                        if (managedGroups[i] === item.id || managedGroups[i] ===item.parentId){
                            item.isHavePower = true;
                        }
                    }
                }
            }
            let  selectedUserManagedGroups = sharedStore.selectedUser.managedGroups;
            if (_.includes(selectedUserManagedGroups, item.id)){
                item.isGroupManage = true;
            }
        });
        return result;
    }

    filter(groups,filterText){
        let result = [];
        result = _.filter(groups,function(group){
            return group.name.includes(filterText);
        })
        return result;

    }

    findAncestors(originGroups, group) {
        let result = [];
        let parentId = group.parentId;
        while(parentId && parentId!==""){
            const parent = _.find(originGroups,{id:parentId});
            if(parent){
                result.push(parent);
                parentId = parent.parentId;
            }else{
                break;
            }
        }
        return result;
    }

    requestDescendants(group, originalGroups) {
        if(originalGroups === undefined || !_.isArray(originalGroups)){
            console.error("暂不支持从数据库中获取子孙");
            return;
        }
        let results = [];
        let children = [];
        let groupId = group.id;
        children = _.filter(originalGroups,{"parentId":groupId});
        while(children && children.length>0){
            results = _.concat(results,children);
            let groupIds = _.map(children,"id");
            children = _.filter(originalGroups,function(group){
                if(groupIds.includes(group.parentId)){
                    return true;
                }
                return false;
            })
        }
        results = _.compact(results);
        return results;
    }
}

module.exports = new GroupProvider();