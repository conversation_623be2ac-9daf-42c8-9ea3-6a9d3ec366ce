"use strict";

import axios from "axios";
import {Message} from "element-ui";

// @note  添加X-Requested-With
axios.defaults.headers.common["X-Requested-With"] = "XMLHttpRequest";
axios.defaults.headers.common["Pragma"] = "no-cache";


let isLogout = false;
axios.interceptors.response.use(function (response) {
    return response;
}, function (error) {
    if (isLogout) {
        return;
    }
    if (error.response.status === 401) {
        isLogout = true;
        const loginURL = window.location.protocol + "//" + window.location.host + window.location.pathname;
        Message({
            message: window.i18n.t('app.message.login-expire'),
            duration: 3000,
            type: "warning",
            center: true,
            onClose: function () {
                window.location.replace(loginURL);
            }
        });
        return;
    }
    return Promise.reject(error);

});