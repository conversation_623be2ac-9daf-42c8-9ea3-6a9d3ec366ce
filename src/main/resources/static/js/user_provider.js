"use strict";
const _ = require("lodash");
const axios = require("axios");
const ADMINUSERID = "BEFE2FF1A20F4C419C8A94B7213C5219";
const ORDINARY_USERS = "ORDINARY_USERS";
const USER_GROUP_ADMINISTRATOR = "USER_GROUP_ADMINISTRATOR";
const SYSTEM_ADMINISTRATOR = "SYSTEM_ADMINISTRATOR";
const ADMIN = "ADMIN";



class User {
    // TODO 用户
    constructor() {
        this.id="";
        this.name="";
        this.username="";
        this.groups=[];
        this.email="";
        this.phone="";
        this.mobilePhone="";
        this.idCard="";
        this.isAdmin = false;
        this.isManager = false;
        this.isGroupManager = false;
        this.roleType = "";
        this.managedGroups = [];
    }
    grant(){
        let that = this;
        if ( window.CONSTANT === undefined){
            return ;
        }
        if(this.id === ADMINUSERID){
            this.isAdmin = true;
            this.roleType = ADMIN;
        }else if(_.find(this.groups, {sysGroup: true})){
            that.roleType = SYSTEM_ADMINISTRATOR;
            that.isManager = true;
            that.isGroupManager = true;
        }else if (that.managedGroups !== undefined && that.managedGroups !== null && that.managedGroups.length > 0){
            const newManagedGroups = _.cloneDeep(that.managedGroups);
            const removeManagedGroups =  _.filter(newManagedGroups, function (managedGroup) {
                return managedGroup !== ADMINUSERID;
            })
            if (removeManagedGroups.length > 0) {
                that.isGroupManager = true;
                that.roleType = USER_GROUP_ADMINISTRATOR;
            }
        }else{
            that.roleType = ORDINARY_USERS
        }
    }

    getUserTip(){
        if(_.isString(this.username)){
            return this.username.toLocaleString().slice(0, 1).toUpperCase()
        }
        return "";
    }
    isExist(){
        return (this.id !== undefined && this.id !== null && this.id !== "");
    }

}

class UserProvider{
    constructor() {
    }
    requestUser(id,callback){
        const url = "/UAMS/users/"+id;
        const that = this;
        axios.get(url).then(function (response){
            if (response.status === 200) {
                let user = that.createUser(response.data);
                if(_.isFunction(callback)){
                    callback(null,user);
                }
            }
        }).catch(function(error){
            if(_.isFunction(callback)){
                callback(error);
            }
        });
    }
    createUser(options){
        let user = new User();
        user = _.assign(user,options);
        user.grant();
        return user;
    }
    requestCurrentUser(){
        let url = 'users/currentUser?t=' + (new Date()).valueOf();
        const that = this;
        return axios.get(url).then(function(response){
            let currentUser;
            if(response.status === 200){
                currentUser = that.createUser(response.data);
            }
            return currentUser;
        }).catch(function(error){
            return error;
        });
    }
    requestUsers(){
        let url = "users?T=" + Math.random();
        const that =this;
        return axios.get(url).then(function(response){
            if(response.status === 200){
                const users = [];
                _.forIn(response.data,function(_user){
                    users.push(that.createUser(_user));
                });
                return users;
            }
            return [];
        });
    }
    saveUser(user,callback){
        let config = {
            url: "users",
            method: !user.isExist() ? "POST" : "PUT",
            data: user
        };
        if (user.idCard === "") {
            user.idCard = null;
        }
        let err;
        axios.request(config).then(function(response){
            if (response.status === 200) {
                err = null;
                if(_.isFunction(callback)){
                    callback(null);
                }
            }
        }).catch(function(error){
            callback(error);
        });
    }

}
module.exports = new UserProvider();