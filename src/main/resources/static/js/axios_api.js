import axios from "axios";

const axiosApi = {
    exportFileRequest(method, url, data, fileName) {
        console.log(method, url, data, fileName);
        return axios({
            method: method,
            url: url,
            data: data,
            responseType: 'blob'
        }).then(function (res) {
            if (res.status === 200) {
                if (window.navigator && window.navigator.msSaveBlob) {
                    // 兼容ie11
                    try {
                        window.navigator.msSaveBlob(new Blob([res.data]), fileName);
                    } catch (e) {
                        console.log(e);
                    }
                } else {
                    let blob = new Blob([res.data]);
                    let downloadElement = document.createElement("a");
                    //创建下载的链接
                    let href = window.URL.createObjectURL(blob);
                    downloadElement.href = href;
                    let date = new Date();
                    downloadElement.download = fileName;
                    document.body.appendChild(downloadElement);
                    //点击下载
                    downloadElement.click();
                    //下载完成移除元素
                    document.body.removeChild(downloadElement);
                    //释放掉blob对象
                    window.URL.revokeObjectURL(href);
                }
                return true;
            } else {
                return false;
            }
        }).catch(error => {
            console.log(error);
        });
    }
}
export default axiosApi
;