#!/bin/bash

echo "========================================"
echo "UAMS Java源代码反编译脚本"
echo "========================================"
echo

# 检查Java环境
if ! command -v java &> /dev/null; then
    echo "错误: 未找到Java环境，请确保Java已正确安装并配置PATH"
    exit 1
fi

echo "1. 检查反编译工具..."

# 检查是否存在CFR反编译工具
if [ ! -f "cfr.jar" ]; then
    echo "未找到CFR反编译工具，正在尝试下载..."
    
    if command -v curl &> /dev/null; then
        curl -L -o cfr.jar "https://github.com/leibnitz27/cfr/releases/latest/download/cfr-0.152.jar"
    elif command -v wget &> /dev/null; then
        wget -O cfr.jar "https://github.com/leibnitz27/cfr/releases/latest/download/cfr-0.152.jar"
    else
        echo "请手动下载CFR反编译工具到当前目录:"
        echo "https://github.com/leibnitz27/cfr/releases/latest/download/cfr-0.152.jar"
        echo "并重命名为 cfr.jar"
        echo
        echo "或者使用其他反编译工具:"
        echo "- JD-GUI: http://java-decompiler.github.io/"
        echo "- Fernflower: 使用IntelliJ IDEA打开.class文件"
        echo
        exit 1
    fi
fi

echo "2. 开始反编译Java源代码..."

# 创建输出目录
mkdir -p "src/main/java"

# 使用CFR反编译
echo "正在反编译com.fulongtech包..."
java -jar cfr.jar --outputdir "src/main/java" "BOOT-INF/classes/com/fulongtech"

echo "正在反编译io包..."
java -jar cfr.jar --outputdir "src/main/java" "BOOT-INF/classes/io"

echo "3. 复制MyBatis映射文件..."
# 查找并复制XML映射文件
if ls BOOT-INF/classes/com/fulongtech/uams/mapper/*.xml 1> /dev/null 2>&1; then
    mkdir -p "src/main/resources/com/fulongtech/uams/mapper"
    cp BOOT-INF/classes/com/fulongtech/uams/mapper/*.xml "src/main/resources/com/fulongtech/uams/mapper/"
fi

echo "4. 清理和整理..."
# 删除临时文件
rm -f "src/main/java/.gitkeep" 2>/dev/null
rm -f "src/main/resources/.gitkeep" 2>/dev/null
rm -f "src/test/java/.gitkeep" 2>/dev/null

echo
echo "========================================"
echo "反编译完成！"
echo "========================================"
echo
echo "下一步操作:"
echo "1. 检查反编译的Java代码是否正确"
echo "2. 修复可能的编译错误"
echo "3. 配置数据库连接参数"
echo "4. 运行 mvn clean compile 测试编译"
echo "5. 运行 mvn spring-boot:run 启动应用"
echo
