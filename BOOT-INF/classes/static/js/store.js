"use strict";

const _ = require("lodash");
const userProvider = require("./user_provider");

let store = {
    debug: true,
    state: {
        currentUser: userProvider.createUser(),
        selectedUser: {},
        clientHeight: 0
    },
    setCurrentUser: function (newValue) {
        if (this.state.currentUser.id !== newValue.id && this.state.currentUser.id !== "") {
            console.error("无法在当前会话中修改当前登录用户")
        }
        console.log('setCurrentUserAction triggered with', newValue);
        this.state.currentUser = _.assign(this.state.currentUser, newValue);
    },
    clearCurrentUser: function () {
        //TODO 此方法应该不会有时候被调用
        console.log('clearCurrentUserAction triggered')
        this.state.currentUser = {};
    },
    setSelectedUser: function (newValue) {
        console.log('setSelectedUserAction triggered with', newValue)
        this.state.selectedUser = newValue;
    },
    clearSelectedUser: function () {
        console.log('clearSelectedUserAction triggered')
        this.state.selectedUser = {};
    },
    setClientHeight:function(newValue){
        console.log('setClientHeight triggered with', newValue)
        this.state.clientHeight = newValue;
    }
}

module.exports = store;