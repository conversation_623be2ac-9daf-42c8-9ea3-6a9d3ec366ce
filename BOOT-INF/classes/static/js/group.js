"use strict";
const axios = require("axios");

class Group {
    constructor() {
        this.id = "";
        this.name = "";
        this.sysGroup = false;
        this.parentId = "";
        this.children = [];
        this.parent = null;
        this.isGroupManage = false;
        this.isHavePower = false;
    }

    isSystemManagerGroup() {
        if (this.id && this.id === 'BEFE2FF1A20F4C419C8A94B7213C5218') {
            return true;
        } else {
            return false
        }
    }

    isNew() {
        if (this.id && this.id !== "") {
            return false;
        }
        return true;
    }

    setParent(parentGroup) {
        if (! (parentGroup instanceof Group)) {
            console.error(parentGroup + "不是一个有效的用户组");
            return;
        }
        if (this.parentId !== "" && this.parentId !== parentGroup.id) {
            console.error("暂不支持修改父用户组");
            return;
        }
        this.parentId = parentGroup.id;
        this.parent = parentGroup;
    }
    requestUsers(){
        if(!this.id){
            console.error("当前的group不是一个有效的组");
            return;
        }
        const url =  "groups/" + this.id + "/users?T=" + Math.random();
        return axios.get(url).then(function(response){
            if (response.status === 200) {
                return response.data;
            }
            return [];
        });
    }
}

module.exports = Group;