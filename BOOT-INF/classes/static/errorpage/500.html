<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
<title>500</title>
<meta http-equiv="pragma" content="no-cache">
<meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
<meta http-equiv="cache-control" content="no-cache">
<meta http-equiv="expires" content="0">
<meta http-equiv="keywords" content="keyword1,keyword2,keyword3">
<meta http-equiv="description" content="This is my page">
<style type="text/css">
#AuthorityPrompt {
	width: 760px;
	text-align: center;
	margin: 0 auto;
	font-family: "黑体";
	color: #4a4f60;
	text-align: center;
	overflow: hidden;
	margin-top: 270px;
}

#AuthorityPrompt label {
	float: left;
	font-size: 120px;
}

#AuthorityContent {
	float: left;
	margin-left: 30px;
	margin-top: 17px;
}

#AuthorityContent p {
	float: left;
	font-size: 50px;
	clear: both;
}

#AuthorityContent p a {
	float: left;
	font-size: 16px;
	color: #22c0e8;
	margin-top: 11px;
	cursor: pointer;
}

#AuthorityContent p span {
	font-size: 16px;
	color: #22c0e8;
	margin-top: 11px;
}

#AuthorityContent p span.PromptReturnTime {
	float: left;
	background: url(../static/images/user/ReturnTimeIcon.png) no-repeat left;
	padding-left: 20px;
	color: #4a4f60;
	margin-right: 25px;
}
</style>
<script type="text/javascript">
	var c;
	var t;
	(function(){
		setTimeout("timedCount()", 1000);
	})();
	function timedCount() {
		c = document.getElementById("time").innerHTML;
		if (c > 0) {
			document.getElementById("time").innerHTML = c - 1;
		} else {
			window.history.back();
		}
		t = setTimeout("timedCount()", 1000);
	}
</script>

</head>

<body>
	<div id="AuthorityPrompt">
		<label>500</label>
		<div id="AuthorityContent">
			<p>服务器内部错误~~</p>
			<p>
				<span class="PromptReturnTime"><span id="time">5</span>秒后</span><a
					onclick="javascript:history.back()">返回上一页</a> 
			</p>
		</div>
	</div>
</body>
</html>
