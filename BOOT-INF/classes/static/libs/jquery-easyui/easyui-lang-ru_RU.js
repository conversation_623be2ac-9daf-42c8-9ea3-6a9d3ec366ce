if ($.fn.pagination){
    $.fn.pagination.defaults.beforePageText = '';
    $.fn.pagination.defaults.afterPageText = '{pages}страницы';
    $.fn.pagination.defaults.displayMsg = 'Show{from}To{to},{total}pages';
}
if ($.fn.datagrid){
    $.fn.datagrid.defaults.loadMsg = 'Обработка, пожалуйста, подождите。。。';
}
if ($.fn.treegrid && $.fn.datagrid){
    $.fn.treegrid.defaults.loadMsg = $.fn.datagrid.defaults.loadMsg;
}
if ($.messager){
    $.messager.defaults.ok = 'ДА';
    $.messager.defaults.cancel = 'НЕТ';
}
$.map(['validatebox','textbox','filebox','searchbox',
    'combo','combobox','combogrid','combotree',
    'datebox','datetimebox','numberbox',
    'spinner','numberspinner','timespinner','datetimespinner'], function(plugin){
    if ($.fn[plugin]){
        $.fn[plugin].defaults.missingMessage = 'Эта запись является обязательным элементом';
    }
});
if ($.fn.validatebox){
    $.fn.validatebox.defaults.rules.email.message = 'Пожалуйста, введите действительный адрес электронной почты';
    $.fn.validatebox.defaults.rules.url.message = 'Пожалуйста, введите правильный URL-адрес';
    $.fn.validatebox.defaults.rules.length.message = 'Длина введенного символа не может превышать {1}';
    $.fn.validatebox.defaults.rules.remote.message = 'Пожалуйста, исправьте это поле';
}
if ($.fn.calendar){
    $.fn.calendar.defaults.weeks = ['СОЛНЦЕ','ПН','Вт','МЫ БЫ','ЧГ','FRI','СУББОТА'];
    $.fn.calendar.defaults.months = ['январь','февраль','март','апрель','май','июнь','июль','август','сентябрь','октябрь','ноябрь','декабрь'];
}
if ($.fn.datebox){
    $.fn.datebox.defaults.currentText = 'CЕГОДНЯ';
    $.fn.datebox.defaults.closeText = 'близко';
    $.fn.datebox.defaults.okText = 'ДА';
    $.fn.datebox.defaults.formatter = function(date){
        var y = date.getFullYear();
        var m = date.getMonth()+1;
        var d = date.getDate();
        return y+'-'+(m<10?('0'+m):m)+'-'+(d<10?('0'+d):d);
    };
    $.fn.datebox.defaults.parser = function(s){
        if (!s) return new Date();
        var ss = s.split('-');
        var y = parseInt(ss[0],10);
        var m = parseInt(ss[1],10);
        var d = parseInt(ss[2],10);
        if (!isNaN(y) && !isNaN(m) && !isNaN(d)){
            return new Date(y,m-1,d);
        } else {
            return new Date();
        }
    };
}
if ($.fn.datetimebox && $.fn.datebox){
    $.extend($.fn.datetimebox.defaults,{
        currentText: $.fn.datebox.defaults.currentText,
        closeText: $.fn.datebox.defaults.closeText,
        okText: $.fn.datebox.defaults.okText
    });
}
if ($.fn.datetimespinner){
    $.fn.datetimespinner.defaults.selections = [[0,4],[5,7],[8,10],[11,13],[14,16],[17,19]]
}
