.pagination {
  zoom: 1;
}
.pagination table {
  float: left;
  height: 30px;
}
.pagination td {
  border: 0;
}
.pagination-btn-separator {
  float: left;
  height: 24px;
  border-left: 1px solid #ccc;
  border-right: 1px solid #fff;
  margin: 3px 1px;
}
.pagination .pagination-num {
  border-width: 1px;
  border-style: solid;
  margin: 0 2px;
  padding: 2px;
  width: 2em;
  height: auto;
}
.pagination-page-list {
  margin: 0px 6px;
  padding: 1px 2px;
  width: auto;
  height: auto;
  border-width: 1px;
  border-style: solid;
}
.pagination-info {
  float: right;
  margin: 0 6px 0 0;
  padding: 0;
  height: 30px;
  line-height: 30px;
  font-size: 12px;
}
.pagination span {
  font-size: 12px;
}
.pagination-link .l-btn-text {
  width: 24px;
  text-align: center;
  margin: 0;
}
.pagination-first {
  background: url('images/pagination_icons.png') no-repeat 0 center;
}
.pagination-prev {
  background: url('images/pagination_icons.png') no-repeat -16px center;
}
.pagination-next {
  background: url('images/pagination_icons.png') no-repeat -32px center;
}
.pagination-last {
  background: url('images/pagination_icons.png') no-repeat -48px center;
}
.pagination-load {
  background: url('images/pagination_icons.png') no-repeat -64px center;
}
.pagination-loading {
  background: url('images/loading.gif') no-repeat center center;
}
.pagination-page-list,
.pagination .pagination-num {
  border-color: #95B8E7;
}
