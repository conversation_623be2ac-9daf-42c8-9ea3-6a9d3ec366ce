if ($.fn.pagination){
    $.fn.pagination.defaults.beforePageText = '';
    $.fn.pagination.defaults.afterPageText = '{pages}pages';
    $.fn.pagination.defaults.displayMsg = 'show{from}To{to},{total}pages';
}
if ($.fn.datagrid){
    $.fn.datagrid.defaults.loadMsg = 'Processing, please wait。。。';
}
if ($.fn.treegrid && $.fn.datagrid){
    $.fn.treegrid.defaults.loadMsg = $.fn.datagrid.defaults.loadMsg;
}
if ($.messager){
    $.messager.defaults.ok = 'YES';
    $.messager.defaults.cancel = 'NO';
}
$.map(['validatebox','textbox','filebox','searchbox',
    'combo','combobox','combogrid','combotree',
    'datebox','datetimebox','numberbox',
    'spinner','numberspinner','timespinner','datetimespinner'], function(plugin){
    if ($.fn[plugin]){
        $.fn[plugin].defaults.missingMessage = 'This entry is a must-lose item';
    }
});
if ($.fn.validatebox){
    $.fn.validatebox.defaults.rules.email.message = 'Please enter a valid email address';
    $.fn.validatebox.defaults.rules.url.message = 'Please enter a valid URL address';
    $.fn.validatebox.defaults.rules.length.message = 'The input character length cannot exceed {1}';
    $.fn.validatebox.defaults.rules.remote.message = 'Please fix this field';
}
if ($.fn.calendar){
    $.fn.calendar.defaults.weeks = ['SUN','MON','TUE','WED','THU','FRI','SAT'];
    $.fn.calendar.defaults.months = ['Jan','Feb','Mar','Apr','May','June','July','Aug','Sep','Oct','Nov','Dec'];
}
if ($.fn.datebox){
    $.fn.datebox.defaults.currentText = 'TODAY';
    $.fn.datebox.defaults.closeText = 'Close';
    $.fn.datebox.defaults.okText = 'YES';
    $.fn.datebox.defaults.formatter = function(date){
        var y = date.getFullYear();
        var m = date.getMonth()+1;
        var d = date.getDate();
        return y+'-'+(m<10?('0'+m):m)+'-'+(d<10?('0'+d):d);
    };
    $.fn.datebox.defaults.parser = function(s){
        if (!s) return new Date();
        var ss = s.split('-');
        var y = parseInt(ss[0],10);
        var m = parseInt(ss[1],10);
        var d = parseInt(ss[2],10);
        if (!isNaN(y) && !isNaN(m) && !isNaN(d)){
            return new Date(y,m-1,d);
        } else {
            return new Date();
        }
    };
}
if ($.fn.datetimebox && $.fn.datebox){
    $.extend($.fn.datetimebox.defaults,{
        currentText: $.fn.datebox.defaults.currentText,
        closeText: $.fn.datebox.defaults.closeText,
        okText: $.fn.datebox.defaults.okText
    });
}
if ($.fn.datetimespinner){
    $.fn.datetimespinner.defaults.selections = [[0,4],[5,7],[8,10],[11,13],[14,16],[17,19]]
}
