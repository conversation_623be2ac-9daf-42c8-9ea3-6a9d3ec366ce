{"app": {"users": "пользовател", "groups": "Группа пользователей", "logout": "выход из системы", "change-password": "изменить пароль", "message": {"login-expire": "Срок вашей посадки истек и выскочит на посадочную страницу через 3 секунды", "no-permission": "У ваш счет нет полномоч"}}, "changPassWord": {"oldPassword": "старый пароль ", "newPassword": "новый пароль  ", "confirmPassword": "подтвердить пароль ", "confirm": "подтверждение ", "cancel": "отмен ", "ruleForm-oldPassword": "Введите старый пароль ", "ruleForm-newPassword": "Введите новый пароль ", "ruleForm-confirmPassword": "Пожалуйста, введите новый пароль", "ruleForm-passWordRule": "длина пароля не менее 8 битов, максимум 20 бит, и комбинация цифр, прописных букв, маленьких букв и специальных символов по меньшей мере трех категорий, цифр, букв и специальных символов (~!@#%^&*_.-)!", "ruleForm-confirmPassword-error": "двоичная ошибка ввода пароля", "confirm-error": " ошибка старого пароля ", "confirm-success": " пароль изменён успешно, повторите, пожалуйста, вход! "}, "users": {"emptyText": "Нет результатов", "add": "добавля", "import": "Импорт", "export": "производный", "username": "Имя пользователя", "name": "имя", "Administrator": "Администратора", "idCard": "идентификационная карта", "email": "электронное письмо", "mobilePhone": "мобильный телефон", "phone": "Телефон", "save": "Сохранить", "cancel": "отмена", "addUserSuccess": "Добавление пользователя завершено", "updateUserSuccess": "Изменение пользователя успешно", "deleteUserSuccess": "Удалено успешно", "searchText": "Введите имя или имя пользователя", "selectTree": "Все группы пользователей", "loadingUser": "Загружаю список пользователей", "number": "порядковый номер", "operation": "операция", "edit": "редактор", "grouping": "Распределять группу пользователей", "delete": "Удалить", "resetPassword": "Сброс пароля", "resetPasswordSuccess": "сбросить Пароль успешно", "resetPasswordTitle": "Эта операция восстановит пароль пользователя, будет ли продолжена!", "notAssignGroup": "безгрупповой пользователь", "modifyUser": "Изменение информации пользователя", "addUser": "Добавить пользователя", "delUserWarnStart": "Эта операция навсегда удалит пользователя：", "delUserWarnEnd": "，Продолжать или нет?", "confirm": "подтверждение", "prompt": "предупреждать", "AssignGroupSuccess": "Распределение пользовательской группы успешно!", "importUsers": {"previous": "Управление пользователями", "present": "Пользователи добавляют их оптом", "downloadTemplate": "Загрузка шаблона", "uploading": "Передача", "upload": "Загрузить", "drag": "Тащите или выбирайте документы сюда", "restrict": "Файл может быть только форматом XLS/XLSX", "error": "сообщение об ошибке", "warn": "предупреждающая информация", "warning": "предупред", "exitMessage": "После выхода из страницы информация в журнале не сохранится.", "log": "Операция информация", "header": "заголовок", "permission": "разрешение", "indexStart": " Строк  ", "indexEnd": "", "noImportPermission": "у вашего пользователя нет прав на импорт, он не может получить доступ к этой странице.  система будет прыгать через 3 секунды на главную страницу."}, "exportUsers": {"selectAll": "выбрать все", "selectedUser": "выбранный", "notSelectedUser": "Не выбран", "toTheLeft": "Слева", "toTheRight": "Направо", "exportUserInformation": "Экспорт информации о пользователе", "UserInfoTable": "Форма информации о пользователе", "ExportUserInfoSuccess": "экспортировать сведения о пользователе успешно"}, "validate": {"name": {"content": "Не может содержать особые символыы", "length": "Дл,ина должна быть от 2 до 20", "notNull": "Имя не должно быть пустым"}, "username": {"content": "Только введи буквы и цифры", "length": "Длина должна быть между 2-20 символами", "notNull": "Имя пользователя не может быть пустым", "hasSpace": "Имя пользователя не может содержать пробелы"}, "mobilePhone": {"empty": "номер телефона не может быть пустым", "correct": "Пожалуйста, введите действующий номер телефона"}, "idCard": "Введите 18 - битный идентификационный номер"}}, "groups": {"fake": "занимать место", "head": {"deleteButton": {"buttonName": "Удалить", "warnMessage": {"context": "Пожалуйста, выберите группу, которую вы хотите удалить!"}, "successMessage": {"context": "Удалить группу пользователей!"}, "dialog": {"hint": "предупреждать", "context": "Вы удалите выбранную группу пользователей, включая его дочернюю группу пользователей! Желаете ли вы продолжить?", "confirmButton": "подтвердить", "cancelButton": "Отмена"}}, "addButton": {"buttonName": "ДОБАВИТЬ"}, "search": {"warnMessage": "Пожалуйста, введите значение поиска"}, "searchPlaceholder": "Имя группы пользователей"}, "context": {"tableName1": "Имя группы пользователей", "tableName2": "Операция", "operation": {"edit": {"title": "редактировать", "dialog": {"title": "Изменить имя группы", "context": {"groupName": "Имя группы пользователей", "submitButton": "подтвердить"}}, "successMessage": {"context": "Изменить успех!"}}, "add": {"title": "Добавить"}, "delete": {"title": "Удалить.", "emptyDialog": {"context": "Вы удалите выбранную группу пользователей, включая его дочернюю группу пользователей! Желаете ли вы продолжить?"}, "dialog": {"Hint": "предупреждать", "context": "Вы удалите выбранную группу пользователей, включая его дочернюю группу пользователей! Желаете ли вы продолжить?", "confirm": "подтвердить", "cancel": "Отмена"}}}}, "addGroupDialog": {"title": "Добавить группу", "parentGroupName": "Родительская группа", "groupName": "Имя группы пользователей", "submit": "подтвердить", "cancel": "Отмена", "noParent": "без", "message": {"success": {"context": "Добавьте успех!"}, "error": {"pre-context": "Имя группы пользователей:", "order-context": ",уже существует", "characterValid": "Название группы не может содержать эти символы(~!@$%^&*()+={}':;',[].<>?~！@￥%……&*（）——+{}【】‘；：”“’。，、？)", "lengthValid": "Максимальная длина имени не может превышать 20 символов!"}, "systemError": {"context": "Системная ошибка, попробуйте еще раз!"}}}, "deleteGroup": {"message": {"title": "предупреждать", "context": "Вы удалите выбранную группу пользователей, включая его дочернюю группу пользователей! Желаете ли вы продолжить?", "error": {"context": "Отметьте хотя бы одну группу！"}, "success": {"context": "Удалить успех!"}}}, "validate": {"name": {"content": "Пожалуйста, введите имя группы", "length": "Длина не может превышать 50 символов"}}}, "personal": {"title": "Личное пространство", "context": {"userInfo": {"username": "имя пользователя：", "name": "название：", "identity": "личность：", "email": "электронное письмо：", "phone": "Телефон：", "mobilePhone": "мобильный телефон："}}}}