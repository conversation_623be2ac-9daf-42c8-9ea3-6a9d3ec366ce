{"app": {"users": "users", "groups": "groups", "logout": "Logout", "change-password": "Change Password", "message": {"login-expire": "Your landing has expired and will jump to the landing page in 3 seconds", "no-permission": "Your account has no access"}}, "changPassWord": {"oldPassword": "old password", "newPassword": "new password", "confirmPassword": "confirm password", "confirm": "confirm", "cancel": "cancel", "ruleForm-oldPassword": "Please enter your old password", "ruleForm-newPassword": "Please enter a new password", "ruleForm-confirmPassword": "Please enter the new password again", "ruleForm-passWordRule": "The password shall be at least 8 digits in length and up to 20 digits in length, and shall be a combination of at least three types of numbers, uppercase letters, lowercase letters and special symbols, including numbers, letters and special symbols(~!@#%^&*_.-)", "ruleForm-confirmPassword-error": "The two passwords are inconsistent", "confirm-error": "Old password error", "confirm-success": "Password changed successfully, please login again"}, "users": {"emptyText": "No results", "add": "ADD", "import": "IMPORT", "export": "export", "username": "username", "Administrator": "Administrator", "name": "name", "idCard": "idCard", "email": "email", "mobilePhone": "mobilePhone", "phone": "phone", "save": "save", "cancel": "cancel", "addUserSuccess": "Add Successfully!", "updateUserSuccess": "Modified user successful", "deleteUserSuccess": "deleted successfully", "searchText": "Please enter username or name", "selectTree": "All user groups", "loadingUser": "Loading user list", "number": "number", "operation": "operation", "edit": "edit", "grouping": "Assign user groups", "delete": "delete", "resetPassword": "resetPassword", "resetPasswordSuccess": "reset password success", "resetPasswordTitle": "This operation will reset the user password. Would you like to continue!", "notAssignGroup": "Users without group", "modifyUser": "Modify the user", "addUser": "Add User", "delUserWarnStart": "This action will permanently delete the user:", "delUserWarnEnd": ",Shall we continue?", "confirm": "confirm", "prompt": "prompt", "AssignGroupSuccess": "User group assigned successfully!", "importUsers": {"previous": "user management", "present": "import users", "downloadTemplate": "Download the template", "uploading": "Uploading user files", "upload": "upload", "drag": "Drag the file here, or click Upload", "restrict": "Only XLS/XLSX files can be uploaded", "error": "error", "warn": "warn", "warning": "warning", "exitMessage": "Log information will not be saved after exiting this page. Are you sure you want to exit?", "log": "log", "header": "header", "permission": "permission", "indexStart": "Line ", "indexEnd": "", "noImportPermission": "Your account has no permission, can't import users.System will route to homepage in 3 seconds."}, "exportUsers": {"selectAll": "SelectAll", "selectedUser": "SelectedUser", "notSelectedUser": "NotSelectedUser", "toTheLeft": "ToTheLeft", "toTheRight": "ToTheRight", "exportUserInformation": "ExportUserInfo", "UserInfoTable": "UserInfoTable", "ExportUserInfoSuccess": "User information exported successfully"}, "validate": {"name": {"content": "Cannot contain special characters", "length": "length between 2 and 20", "notNull": "The name cannot be empty"}, "username": {"content": "only num and alpabet", "length": "length between 2 and 20", "notNull": "The username cannot be empty", "hasSpace": "The user name cannot contain spaces"}, "mobilePhone": {"empty": "mobilePhone's can not empty", "correct": "Please enter a valid mobile phone number"}, "idCard": "Please enter your 18-digit ID number"}}, "groups": {"fake": "fake", "head": {"deleteButton": {"buttonName": "DELETE", "warnMessage": {"context": "Please select a group that you want to delete!"}, "successMessage": {"context": "Delete checked groups success!"}, "dialog": {"hint": "Hint", "context": "You will delete the selected user group, including his child user group! Would you like to continue?", "confirmButton": "confirm", "cancelButton": "cancel"}}, "addButton": {"buttonName": "ADD"}, "search": {"warnMessage": "Please input value about search"}, "searchPlaceholder": "GroupName"}, "context": {"tableName1": "groupName", "tableName2": "operation", "operation": {"edit": {"title": "edit", "dialog": {"title": "Modify Group Name", "context": {"groupName": "GroupName", "submitButton": "submit"}}, "successMessage": {"context": "Modify Success!"}}, "add": {"title": "add"}, "delete": {"title": "delete", "emptyDialog": {"context": "you do not select anyone group,Please select again!"}, "dialog": {"Hint": "Hint", "context": "You will delete this group! Would you like to continue?", "confirm": "confirm", "cancel": "cancel"}}}}, "addGroupDialog": {"title": "Add Group", "parentGroupName": "Parent GroupName", "groupName": "GroupName", "submit": "submit", "cancel": "cancel", "noParent": "None", "message": {"success": {"context": "Add Successfully!"}, "error": {"pre-context": "group name：", "order-context": "，already exists", "characterValid": "The group name can not contain this characters(~!@$%^&*()+={}':;',[].<>?~！@￥%……&*（）——+{}【】‘；：”“’。，、？)", "lengthValid": "The max name length can not over 20 chars!"}, "systemError": {"context": "System Error,Please try again!"}}}, "deleteGroup": {"message": {"title": "Hint", "context": "You will delete checked groups! Would you like to continue?", "error": {"context": "Pleas check at least one group！"}, "success": {"context": "Delete Success!"}}}, "validate": {"name": {"content": "Please enter the groupName", "length": "The length cannot exceed 50 characters"}}}, "personal": {"title": "Personal Information", "context": {"userInfo": {"username": "username：", "name": "name：", "identity": "idCard：", "email": "email：", "phone": "phone：", "mobilePhone": "mobilePhone："}}}}