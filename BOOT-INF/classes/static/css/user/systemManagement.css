@charset "utf-8";
/* CSS Document */

body{overflow: hidden}
ul,a{ list-style-type:none;padding:0px; margin:0px;text-decoration: none}
/*左侧内容*/
#managementList{float:left;width:175px;height:100%;border:solid 1px #aac8ec; overflow:hidden;margin:10px;}
#managementList a{text-transform:none;text-decoration:none;width:100%;border-bottom:solid 1px #aac8ec;height:33px; line-height:33px; text-align:left;font-size:12px; color:#191b1f; padding-left:30px;display:block;}
#managementList a li span{background:no-repeat top left; padding:2px 2px 2px 30px;}
@-moz-document url-prefix(){#managementList a li span{background:no-repeat top left; padding:1px 2px 0px 30px;}}/* 仅firefox 识别 */
#managementList a.managementListSelect{ background:#e9f1ff;}
#managementList a.managementListSelect span{ background-position-y:-18px; font-weight:bold;}
/*右侧内容*/
.operationBar{ float:left; width:100%;border-bottom:solid 1px #d4d4d4; height:30px;font-size: 12px}
.systemManagementReturn{ float:left;background:url(../../images/systemManagement/systemManagementReturn.png) no-repeat left; width:20px; height:12px; margin-right:28px; margin-top:5px;}
.systemManagementAdd{ float:left; background:url(../../images/systemManagement/systemManagementAdd.png) no-repeat left; width:18px; height:18px; margin-right:28px; margin-top:1px;}
.systemManagementDelete{ float:left; background:url(../../images/systemManagement/systemManagementDelete.png) no-repeat left; width:16px; height:18px;margin-top:1px;}
.systemManagementClose{ float:right; background:url(../../images/systemManagement/systemManagementClose.png) no-repeat left; width:12px; height:12px;margin-top:4px;}
.systemManagementButton{float:left;background-color: #f0f0f0;border-radius: 5px; width:auto;min-width:72px; height:32px; border:none; color:#111; font-family:"微软雅黑";margin-top:-5px;margin-left:5px; cursor:pointer;}
.list{ font-weight:normal; clear:both; font-size:14px;font-weight:bold;padding-top:20px;}

/*列表上方搜索框*/
.searchForm{ font-size:14px; margin-top:10px;}
.searchFormText{ border:solid 1px #cbccd0; width:100px; height:20px; border-radius:2px;}
.submitSearchForm{ border:solid 1px #cbccd0; width: 60px; height:25px; border-radius:2px;margin-left: 25px;}
.userRealName{ margin-left: 35px;}

/*列表*/
#listTable{ font-size:14px;	border-collapse: collapse; margin-top:7px;table-layout:fixed;}
#listTable th{ font-weight:normal; border:solid 1px #d4d4d4; height:30px; line-height:30px;text-align: center;}
#listTable td{ text-align:center; border:solid 1px #d4d4d4;white-space: nowrap;overflow: hidden;text-overflow: ellipsis; line-height:30px;}
#listTable td a{ padding-left:15px;color:#6399eb;}

/*添加角色*/
.operationBar h3{font-size:12px;font-family:"微软雅黑";font-size:12px; font-weight:normal; color:#191a1f; margin-top:5px;}
.addRoleText{font-size:12px;font-family:"微软雅黑"; clear:both;}
.addRoleText label{ font-size:12px;}
.addRoleText input{border:solid 1px #d4d4d4;border-radius:2px; width:150px; height:22px; margin-top:10px;}
.addRoleText textarea{border:solid 1px #d4d4d4;border-radius:2px; width:150px; height:100px; margin-top:10px;}
.addRoleText select{border:solid 1px #d4d4d4;border-radius:2px; width:150px; height:22px; margin-top:10px;text-algin:center;}
#addRoleSubmit{ background:url(../../images/systemManagement/addRoleSubmit.png) no-repeat left; width:60px; height:30px; border:none; color:#191a1f; font-size:14px; font-family:"微软雅黑"; margin-top:20px;}

/*部门右键菜单*/
div#rMenu {position: absolute;visibility: hidden;top: 0;background-color: #555;text-align: left;padding: 2px;}
div#rMenu ul li {margin: 0;padding: 5px;cursor: pointer;list-style: none outside none;background-color: #DFDFDF;font-size:12px;}

/*添加账号*/
h3,p{ padding:0px; margin:0px;}
#AddAccount{font-size:12px;font-family:"微软雅黑";width:720px;height:320px;padding:10px}
/*#AddAccount h3#AddAccountTitle{color:#1d2121;font-weight:normal; margin-top:15px;margin-left:12px;}*/
#AddAccountTitle{font-size:12px;font-family:"微软雅黑";color:#1d2121;font-weight:normal; margin-left:12px;}
.UserInfo{ border:solid 1px #cacbcf; height:90px; border-radius:8px; margin-top:48px;}
.UserInfoContent{ clear:both;}
.UserInfoContent span{ float:left; width:150px; margin-top:10px; line-height:24px; min-height:20px; height:auto;overflow: hidden;text-overflow: ellipsis;}
.UserInfo label{ float:left; width:50px;height:20px;margin-left:25px; text-align:right; line-height:20px; margin-top:10px;}
.UserInfo input{ float:left; border:solid 1px #cbccd0; width:146px; height:20px; margin-top:10px; border-radius:2px;}
.UserInfo span{ float:left; width:146px; height:20px; margin-top:10px; border-radius:2px;line-height:20px;}
.UserSet{float:left;width:718px; border:solid 1px #cacbcf; border-radius:8px; margin-top:28px;padding-bottom:20px;}
.UserSet h3{ float:left; width:70px; height:20px; background-color:#f5f6fa; font-size:12px; text-align:center; line-height:20px; margin-left:12px; font-weight:normal;margin-top:-12px;}
.UserSet input{ float:left; border:solid 1px #cbccd0; width:146px; height:20px; border-radius:2px;}
.UserSet span{ float:left; width:146px; border-radius:2px;overflow: hidden;}
.UserSet p{clear:both;}
.AddCancel{float:right; background:url(../../images/systemManagement/AddCancel.png) no-repeat left; width:54px; height:28px; border:none; color:#282c2f; font-family:"微软雅黑"; margin-top:10px; margin-right:30px; cursor:pointer;}
.AddSave{float:right; background:url(../../images/systemManagement/AddSave.png) no-repeat left; width:54px; height:28px; border:none; color:#FFF; font-family:"微软雅黑";margin-top:10px; cursor:pointer;}
.editbtn{float:left; background:url(../../images/systemManagement/AddSave.png) no-repeat left; width:54px; height:28px; border:none; color:#FFF; font-family:"微软雅黑";margin-top:-5px; cursor:pointer;}
.UserSetContent{ clear:both; margin-top:18px;}
.UserSetContent label{float:left; line-height:24px; margin-left:38px;}
.AccountSelect{ float:left;border:solid 1px #cbccd0;width:148px;height:22px;position:relative;border-radius:4px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;
}
.AccountSelect a.selet_open{display:inline-block;position:absolute;right:0;top:0;background:url(../../images/systemManagement/AccountSelectIcon.png) no-repeat;width:14px;height:8px;margin-top:8px;margin-right:5px;}
.AccountSelect span.option{width:148px;max-height:140px;height:auto;border:solid 1px #cbccd0;position:absolute;top:23px;left:-1px;z-index:2;overflow-y:auto;overflow-x:hidden;display:none; background-color:#FFF;line-height:24px;}
.AccountSelect span.option a{display:block;height:20px;text-align:left;margin-left:5px;width:100%; line-height:19px;}
.UserInfo h3{ float:left; width:70px; height:20px; background-color:#f5f6fa; font-size:12px; text-align:center; line-height:20px; margin-top:-12px; margin-left:12px; font-weight:normal;}
.UserSetContent span{ float:left; width:150px;line-height:24px;}
div#groupTree {position: absolute;visibility: hidden;top: 0;background-color: #FFFAF0;text-align: left;padding: 2px; width: 200px;height: 200px;border: 1px solid;overflow: auto;}
#roleList {position: absolute;visibility: hidden;top: 0;background-color: #FFFAF0;text-align: left;padding: 2px;height: 200px;border: 1px solid;overflow: hidden;margin-left: 75px;margin-top: 230px;}
#roleList p{clear:both;}
#roleList input{float:left;border-radius:2px;}
#roleList span{float: left;line-height: 24px;}
/*分页*/
#AccountPagination {margin-left: 3%;margin-bottom: 3%}
#AccountPagination a{ font-size: 12px;text-decoration: none;display: block;float: left;color: #565656;height: 12px;line-height: 12px;padding: 0 5px;margin:2px; margin-top:5px;}
#AccountPagination a.homePage{ background:url(../../images/systemManagement/HomePage.png) no-repeat; width:10px; height:12px;}
#AccountPagination a.PagePrev{ background:url(../../images/systemManagement/PrevPage.png) no-repeat; width:8px; height:12px;}
#AccountPagination a.PageNext{ background:url(../../images/systemManagement/NextPage.png) no-repeat; width:8px; height:12px;}
#AccountPagination a.lastPage{ background:url(../../images/systemManagement/LastPage.png) no-repeat; width:10px; height:12px;}
.PageSelect{border:solid 1px #aac8ec;width:50px; float:left; margin-top:2px;}
#AccountPagination span{ float:left; padding: 3px 0px 0px 5px; font-size:12px;}
/*权限管理*/
.table {width:80%;font:14px;color:#333333;text-align:center;border-collapse:collapse;}
.table td,.table th{border:1px solid #333333;padding: 10px;}
.message {background: #eee;border: 1px solid #eee;margin: 10px 0;padding: 10px;}
.form-group {margin: 10px 0;}
.form-group label {width: 130px;float: left;}

/*导入用户页面*/
#userInfo{width: 200px;}
#submitImport{width: 48px;margin-left: 12px;}
.downloadTemplate{ border:solid 1px #000000; width: 80px; height:25px; border-radius:2px;text-decoration: none;font-size: 14px;background-color: #D3D3D3;display:inline-block;line-height:25px;text-align:center;margin-left: 15px;;}

/*自定义zTree图标*/
/*.ztree li span.button.level1_ico_docu{background: url(../../images/user/level1.png) no-repeat scroll 0 0 transparent; vertical-align:top; *vertical-align:middle}
.ztree li span.button.level2_ico_docu{background: url(../../images/user/level2.png) no-repeat scroll 0 0 transparent; vertical-align:top; *vertical-align:middle}
.ztree li span.button.level3_ico_docu{background: url(../../images/user/level3.png) no-repeat scroll 0 0 transparent; vertical-align:top; *vertical-align:middle}*/
#userListDiv{
    height: calc(100vh - 200px);
    overflow-y: auto;
}
