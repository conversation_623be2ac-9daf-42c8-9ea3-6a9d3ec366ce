@charset "utf-8";
/* CSS Document */
body{padding:0px; margin:0px; overflow:hidden;}
ul,li{ list-style-type:none;padding:0px; margin:0px;}
p,h3{padding:0px; margin:0px;}

/*权限界面*/
#AuthorityBack{ background:url(../images/user/AuthorityBack.png) no-repeat center center; width:auto;height:900px;margin:0 auto;}
.InfoInterface{ background:url(../images/user/InfoInterface.png) no-repeat; width:360px; height:580px; position:absolute; bottom:0px; right:152px;}
.InfoInterface p{clear:both; font-family:"黑体"; font-size:30px; text-align:center; margin-top:150px; color:#1a1a1a;}
.InfoInterface p.ReturnTime{float:left; text-align:left; font-size:16px; background:url(../images/user/ReturnTimeIcon.png) no-repeat left; padding-left:20px; clear:none; margin-top:125px; margin-left:110px;}
.InfoInterface input{ float:left; background:url(../images/user/ReturnTimeBack.png) no-repeat; width:70px; height:43px; color:#b0811e; border:none; margin-top:100px; margin-left:45px; font-size:18px; padding-top:15px; font-weight:bold; cursor:pointer;}

#AuthorityPrompt{width:760px; text-align:center; margin: 0 auto;font-family:"黑体"; color:#4a4f60; text-align:center; overflow:hidden; margin-top:270px;}
#AuthorityPrompt label{ float:left; font-size:120px;}
#AuthorityContent{ float:left;margin-left:30px; margin-top:17px;}
#AuthorityContent p{float:left; font-size:50px;clear:both;}
#AuthorityContent p a{float:left; font-size:16px; color:#22c0e8; margin-top:11px;cursor: pointer;}
#AuthorityContent p span{font-size:16px; color:#22c0e8; margin-top:11px;}
#AuthorityContent p span.PromptReturnTime{float:left;background:url(../../images/user/ReturnTimeIcon.png) no-repeat left; padding-left:20px;color:#4a4f60; margin-right:25px;}