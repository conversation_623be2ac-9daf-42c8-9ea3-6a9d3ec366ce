import Vue from 'vue';
import <PERSON>ement<PERSON> from 'element-ui';
import VueRouter from 'vue-router'
import VueI18n from 'vue-i18n'

import "./js/axios_config";
import "./js/window_resize";
import userProvider from "./js/user_provider";
import axios from "axios";

/**
 * 密码修改页面
 */
import changePassword from "./components/changePassword.vue";
import "./css/user/changePwd.css";
Vue.use(ElementUI);
Vue.use(VueRouter);
Vue.use(VueI18n);

// 挂载axios原型上
Vue.prototype.$axios = axios;

import 'element-ui/lib/theme-chalk/index.css';
import "./fonts/iconfont.css";
import "./css/user/main.css";


const i18n = new VueI18n({
    locale: localStorage.lang,
    messages: {
        'en_US': require('./languages/en.json'),
        'zh_CN': require('./languages/zh.json'),
        'ru_RU': require('./languages/ru.json')
    }
});
window.i18n = i18n;
Vue.config.productionTip = false;
//group的添加编辑界面，回车键会导致页面刷新，这里暂时全局禁用回车
document.onkeydown = function() {
    if(window.event.keyCode===13) {
        window.event.returnValue=false;
    }
}

async function getSystemLang(){
    let lang = 'zh_CN';
    const baseUrl = window.location.protocol + "//" + window.location.host + "/UAMS"
    let url = baseUrl + '/getLang?t=' + (new Date()).valueOf();
    if(localStorage.lang === undefined || localStorage.lang===null){
        await axios.get(url).then(function(response){
            if(response.status === 200 ){
                // 判断是否为语言包数据，解决此请求被302之后，lang本地缓存不为语言包的bug
                if (response.data.length === 5){
                    lang = response.data;
                }
            }
        });
        localStorage.setItem("lang",lang);
    }else{
        lang = localStorage.lang;
    }
    return lang;
}

axios.all([getSystemLang(), userProvider.requestCurrentUser()])
    .then(axios.spread(function (lang, currentUser) {
        i18n.locale = lang;
        // if (currentUser !== undefined) {
        //     store.setCurrentUser(currentUser);
        // }
        new Vue({
            i18n,
            render: h => h(changePassword)
    }).$mount('#toChangePwd');
    }));
