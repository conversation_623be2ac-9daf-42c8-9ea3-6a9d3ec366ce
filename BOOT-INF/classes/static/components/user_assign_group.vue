<template>
    <div>
	    <el-row>
		    <el-col :span =24>
			    <GroupList :showOperation="false"
			               :height="500"
			               :canSelectAll="sharedState.currentUser.isAdmin"
			               :selection="checkedGroups"
			               @change="handleSelect"
			    ></GroupList>
		    </el-col>
	    </el-row>
        <el-row style="margin-top: 10px;height:40px;">
	        <el-col :span=24 align="right">
		        <el-button type="primary" @click="submitGrouping">{{ $t('groups.addGroupDialog.submit') }}</el-button>
		        <el-button @click="cancelAssign">{{ $t('groups.addGroupDialog.cancel') }}</el-button>
	        </el-col>
        </el-row>
    </div>
</template>

<script>
import axios from "axios";
import store from "../js/store";
import _ from "lodash";
import GroupList from "./group_list.vue";
import {forEach} from "lodash/collection";

export default {
    name: "user_assign_group",
    props: ["allGroups", "visible"],
    watch: {
        "visible": {
            immediate: true,
            handler: function (visible) {
                if (visible) {
                    this.selectUserOwnedGroups();
                } else {
                    this.checkedGroups = [];
                }
            }
        }
    },
    data: function () {
        return {
            groups: this.allGroups,
            checkedGroups: [],
            userOwnedGroups: [],
            sharedState: store.state
        }
    },
    methods: {
        handleSelect: function (selection) {
          this.userOwnedGroups = selection;
        },
        submitGrouping: function () {
            let $this = this;
            let userId = this.sharedState.selectedUser.id;
            let groupData = [];
            _.forEach($this.userOwnedGroups,function(group) {
              let data = {
                groupId : group.id,
                isGroupManage : group.isGroupManage
              }
              groupData.push(data)
            });
            let postData = {
              userId: userId,
              groupModelList: groupData
            };
            axios.post('users/grouping', postData
            ).then(function (response) {
                if (response.status === 200) {
                    $this.$message({
                        type: 'success',
                        message: i18n.t('users.AssignGroupSuccess'),
                        center: true
                    });
                    $this.$emit('close', null);
                    $this.$emit('refresh', null);
                }
            }).catch(function (error) {
                $this.$message({
                    type: 'error',
                    message: error.response.data.message,
                    center: true
                });
            });
        },
        cancelAssign: function () {
            this.$emit('close', null);
        },
        selectUserOwnedGroups: function () {
            let $this = this;
            const userId = this.sharedState.selectedUser.id;
            if (!userId || userId === "") {
                return;
            }
            axios.get('users/groups/' + userId + '?T=' + Math.random()).then(function (response) {
                if (response.status === 200) {
                    $this.checkedGroups = response.data;
                }
            }).catch(function (error) {
                console.log(error);
            });
        }
    },
    components: {
        GroupList
    }
}
</script>

<style scoped>

</style>