<template>
	<div>
		<el-row :gutter="20" ref="search" style="margin-bottom:20px;">
			<el-col :span="8" style="height: 40px;">
				<el-button type="primary" plain @click="openEditorPanel()"
				           v-if="sharedState.currentUser.isAdmin || sharedState.currentUser.isManager">{{ $t('users.add') }}
				</el-button>
				<el-button type="primary" plain @click="openImportDialog"
				           v-if="sharedState.currentUser.isAdmin || sharedState.currentUser.isManager">{{ $t('users.import') }}
				</el-button>
        <el-button type="primary" plain @click="openExportDialog()"
                   v-if="sharedState.currentUser.isAdmin || sharedState.currentUser.isManager">{{ $t('users.export') }}
        </el-button>
			</el-col>
			<el-col :xs="{span:16,offset:0}" :sm="{span:12,offset:4}" :md="{span:10,offset:6}">
				<el-input :placeholder="$t('users.searchText')" v-model="queryParams.searchText" @input="startFilter">
					<el-tooltip :content="queryParams.tooltip" slot="prepend" effect="light" placement="bottom-start"
					            :visible-arrow="false">
						<el-select-tree
							style="width:130px"
							:placeholder="$t('users.selectTree')"
							:clearable="true"
							:defaultExpandAll="true"
							:checkStrictly="true"
							:data="groupsInFilter"
							size="small"
							v-model="queryParams.group"
							:props="{value:'id',label:'name',children:'children'}"
							@change="getGroupUsers"
						></el-select-tree>
					</el-tooltip>
					<el-button slot="append" icon="el-icon-search" :clearable="true" @click="startFilter"></el-button>
				</el-input>
			</el-col>
		</el-row>
		<u-table v-loading="loading"
		         ref="UserList"
		         :element-loading-text="$t('users.loadingUser')"
		         use-virtual
		         style='width: 100%' border
		         :emptyText="$t('users.emptyText')"
		         :cell-style="{height:'40px',padding:'2px 0px'}"
                 :header-cell-style="{background:'#f5f5f5'}"
		         :row-height="40"
		         :height="tableHeight">
			<el-table-column type="index" label="NO." width="80" align='center'></el-table-column>
			<el-table-column :render-header="labelHead" prop='username' :label="$t('users.username')"
			                 show-overflow-tooltip
			                 align='center'></el-table-column>
			<el-table-column :render-header="labelHead" prop='name' :label="$t('users.name')" show-overflow-tooltip
			                 :formatter="nameFormatter"
                             align='center'></el-table-column>
			<el-table-column :render-header="labelHead" prop='idCard' :label="$t('users.idCard')" show-overflow-tooltip
			                 align='center'></el-table-column>
			<el-table-column :render-header="labelHead" prop='mobilePhone' :label="$t('users.mobilePhone')"
			                 show-overflow-tooltip align='center'></el-table-column>
			<el-table-column prop='operate' minWidth="50px" :label="$t('users.operation')" align='center'>
				<template slot-scope="scope">
					<el-tooltip class="item" effect="dark" :content="$t('users.edit')" placement="top">
						<el-button type="text" icon="el-icon-edit" size="medium"
						           v-if='scope.row.id=== sharedState.currentUser.id || canOperate(scope.row)'
						           @click="openEditorPanel(scope.row)"></el-button>
					</el-tooltip>
					<el-tooltip class="item" effect="dark" :content="$t('users.grouping')" placement="top">
						<el-button type="text" icon="el-icon-uams-group" size="medium"
                       v-if='canOperateGoupsManage(scope.row)'
						           @click="openGroupingDialog( scope.row)"></el-button>
					</el-tooltip>
					<el-tooltip class="item" effect="dark" :content="$t('users.delete')" placement="top">
						<el-button type="text" icon="el-icon-delete" size="medium" v-if='canOperate(scope.row)'
						           @click="delUser(scope.row)"></el-button>
					</el-tooltip>
          <el-tooltip class="item" effect="dark" :content="$t('users.resetPassword')" placement="top">
            <el-button type="text" icon="el-icon-refresh-left" size="medium" v-if='canOperate(scope.row)'
                       @click="resetPassword(scope.row)"></el-button>
          </el-tooltip>
				</template>
			</el-table-column>
		</u-table>
		<el-dialog :width="userDialogWidth"  :title=userEditorDialogTitle :visible.sync="visible.userEditorVisible"
		           :before-close="closeEditorPanel"
		           :close-on-click-modal="false">
			<userEditor ref="userEditor" :visible="visible.userEditorVisible" v-on:close="closeEditorPanel"
			            v-on:refresh="getGroupUsers"></userEditor>
		</el-dialog>
		<el-dialog :title="$t('users.grouping')" :visible.sync="visible.groupingVisible" :before-close="closeGrouping"
		           :close-on-click-modal="false">
			<groups :visible="visible.groupingVisible" :allGroups="groups" v-on:close="closeGrouping"
			        v-on:refresh="getGroupUsers"></groups>
		</el-dialog>
	</div>

</template>

<script>
import Vue from 'vue';
import { UTable } from 'umy-ui';
Vue.use(UTable);
import axios from "axios";
import _ from "lodash";
import userEditor from "./user_editor.vue";
import userImport from "./user_import.vue";
import groups from "./user_assign_group.vue"
import ElSelectTree from 'el-select-tree';
import GroupProvider from "../js/group_provider";
import UserProvider from "../js/user_provider";


import store from "../js/store.js";

let visible = {
	userEditorVisible: false,
	groupingVisible: false,
	userImportVisible: false
}

export default {
	name: "user_management",
	components: {
		userEditor,
		userImport,
		groups,
		ElSelectTree
	},
	data: function () {
		return {
			users: [],
			queryParams: {
				searchText: "",
				group: "",
				tooltip: this.$t('users.selectTree')
			},
			filterText: "",
			visible: visible,
			groups: [],
            emptyGroup:GroupProvider.createGroup({
                "id": "990F7CEB6F22435BAB2D67BADF18D470",
                "name": this.$t('users.notAssignGroup')
            }),
			userEditorDialogTitle: "",
			loading: true,
			sharedState: store.state,
			userDialogWidth:""
		}
	},
	computed: {
		filteredUsers: function () {
			const keyword = this.filterText;
			if (!keyword) {
				return this.users;
			} else {
				return _.filter(this.users, function (user) {
					return (!_.isEmpty(user.username) && user.username.toLowerCase().includes(keyword.toLowerCase())) 
					|| (!_.isEmpty(user.name) && user.name.toLowerCase().includes(keyword.toLowerCase()));
				});
			}
		},
        tableHeight:function(){
		    return this.sharedState.clientHeight - 100;
        },
		groupsInFilter: function () {
			let groups = GroupProvider.adaptPidToChildren(this.groups);
            groups = _.concat(this.emptyGroup, groups)
			return groups;
		}
	},
	methods: {
		startFilter: function () {
			this.filterText = this.queryParams.searchText;
			//TODO u-table的接口，直接双向绑定会影响性能
			this.$refs.UserList.reloadData(this.filteredUsers);
		},
        nameFormatter:function(row,column){
            if (row.isAdmin) {
                return this.$t('users.Administrator');
            }
            return row.name;
        },
		getGroupUsers(groupId) {
			let $this = this;
			let group = null;
            this.loading = true;
            if(groupId ==null) groupId = $this.queryParams.group;
            group = _.find(_.concat(this.emptyGroup,this.groups),{id:groupId});
            let promise;
            if(group !== undefined){
                promise = group.requestUsers();
	            this.queryParams.tooltip = group.name;
            }else{
                promise = UserProvider.requestUsers();
                this.queryParams.tooltip = this.$t('users.selectTree');
            }
            promise.then(function(users){
                $this.users = $this.sortUsers(users);
                $this.startFilter();
            }).catch(function (error) {
                $this.$message({
                    type: 'error',
                    message: error.response.data.message,
                    center: true
                });
            }).finally(function () {
                $this.loading = false;
            });
		},
        sortUsers:function(users){
            let results = [];
            const that = this;
            if (!this.sharedState.currentUser.isAdmin) {
                results = _.filter(users, function (user) {
	                return user.id !== "BEFE2FF1A20F4C419C8A94B7213C5219";
                });
            }else{
                results = _.cloneDeep(users);
            }
            //排序
	        results = _.sortBy(results, "username");
	        results = _.sortBy(results, function (item) {
		        return item.id===that.sharedState.currentUser.id ? -1:1;
	        });
            return results;
        },
		openEditorPanel(selectedUser) {
			//先检查用户是否存在
			let $this = this;
			if (selectedUser) {
				//是有选择用户的
				axios.get('users/' + selectedUser.id).then(function (response) {
          store.setSelectedUser(response.data);
					$this.visible.userEditorVisible = true;
					$this.userEditorDialogTitle = i18n.t('users.modifyUser');
				}).catch(function (error) {
					$this.$message({
						type: 'error',
						message: error.response.data.message,
						center: true
					});
				});
			} else {
				$this.visible.userEditorVisible = true;
				$this.userEditorDialogTitle = $this.$t('users.addUser');
				store.clearSelectedUser();
			}
		},
		closeEditorPanel() {
		    this.$refs['userEditor'].$refs['userForm'].resetFields();
			this.visible.userEditorVisible = false;
			store.clearSelectedUser();
		},

		delUser: function (row) {
			let $this = this;
			$this.$confirm(i18n.t('users.delUserWarnStart') + row.username + i18n.t('users.delUserWarnEnd'), i18n.t('users.prompt'), {
				confirmButtonText: i18n.t('users.confirm'),
				cancelButtonText: i18n.t('users.cancel'),
				type: 'warning'
			}).then(function () {
				axios.delete('users/' + row.id
				).then(function (response) {
					if (response.status === 200) {
						$this.$message({
							type: 'success',
							message: $this.$t('users.deleteUserSuccess'),
							center: true
						});
						$this.getGroupUsers();
					}
				}).catch(function (error) {
					$this.$message({
						type: 'error',
						message: error.response.data.message,
						center: true
					});
				});
			});
		},
    resetPassword: function (row){
      let $this = this;
      $this.$confirm(i18n.t('users.resetPasswordTitle') , i18n.t('users.prompt'), {
        confirmButtonText: i18n.t('users.confirm'),
        cancelButtonText: i18n.t('users.cancel'),
        type: 'warning'
      }).then(function () {
        axios.get('users/resetPassword?id=' + row.id
        ).then(function (response) {
          if (response.status === 200) {
            $this.$message({
              type: 'success',
              message: $this.$t('users.resetPasswordSuccess'),
              center: true
            });
          }
        }).catch(function (error) {
          $this.$message({
            type: 'error',
            message: error.response.data.message,
            center: true
          });
        });
      });
    },
		groupFormatter: function (row, column, groups, index) {
			return _.join(_.map(groups, "name"), ",");
		},

		openImportDialog: function () {
			this.$router.push("/user/import");
		},

    openExportDialog: function () {
      this.$router.push({
        name: "userExport",
        params: {users : this.users}
      });
    },

		openGroupingDialog: function (row) {
			//先检查用户是否存在
			let $this = this;
			axios.get('users/' + row.id + '?T=' + Math.random()).then(function (response) {
				if (response.status === 200) {
					$this.visible.groupingVisible = true;
					store.setSelectedUser(row);
				}
			}).catch(function (error) {
				$this.$message({
					type: 'error',
					message: error.response.data.message,
					center: true
				});
			});
		},
		closeGrouping: function () {
			this.visible.groupingVisible = false;
			store.clearSelectedUser();
		},
		getAllGroup: function () {
			let $this = this;
            GroupProvider.requestGroups().then(function(groups){
                $this.groups = groups;
            }).catch(function (error) {
                $this.$message({
                    type: 'error',
                    message: error.response.data.message,
                    center: true
                });
            });
		},
		/**
		 * 操作按键显示控制逻辑
		 * @param row
		 * @returns {boolean|boolean}
		 * @constructor
		 */
		canOperate: function (row) {
			const currentUser = this.sharedState.currentUser;
			if (currentUser.isManager || currentUser.isAdmin) {
				//只有当当前用户是管理员的时候才可以操作
				return !(row.username === "admin" || row.username === currentUser.username)
			}
			return false;
		},
    /**
     * 组管理员用户操作按键显示控制逻辑
     * @param row
     * @returns {boolean|boolean}
     * @constructor
     */
    canOperateGoupsManage: function (row) {
      const currentUser = this.sharedState.currentUser;
      if (currentUser.isManager || currentUser.isAdmin) {
        //只有当当前用户是管理员的时候才可以操作
        return !(row.username === "admin" || row.username === currentUser.username)
      }
      if (currentUser.roleType === CONSTANT.USER_GROUP_ADMINISTRATOR) {
        // 当前用户时组管理员的时候可以操作
        return !(row.username === "admin" || row.username === currentUser.username)
      }
      return false;
    },
		labelHead: function (h, {column, $index}) {
			let l = column.label.length
			let f = 8 //每个字大小，其实是每个字的比例值，大概会比字体大小差不多大一点，
			if (i18n.locale == 'ru_RU') {
				f = 8;
			} else {
				f = 16;
			}
			column.minWidth = f * l < 60 ? f * l : 60; //字大小乘个数即长度 ,注意不要加px像素，这里minWidth只是一个比例值，不是真正的长度
			return h('div', {class: 'table-head', style: {width: '100%'}}, [column.label])
		},
		dialogWidth:function (){
			let $this = this;
			if(i18n.locale === "zh_CN"){
				$this.userDialogWidth= "600px";
			}else if(i18n.locale === "en_US"){
				$this.userDialogWidth= "680px";
			}else{
				$this.userDialogWidth= "880px";
			}
		}
	},
	mounted: function () {
		this.getGroupUsers();
		this.getAllGroup();
		this.dialogWidth();
	}
};
</script>

<style>
.el-select-tree div input {
	background-color: #f5f7fa;
	border: 0px;
}

:focus {
	outline: #FFFFFF auto 0px;
}
</style>