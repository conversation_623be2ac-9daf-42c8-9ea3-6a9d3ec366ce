<template>
    <div>
        <el-form :rules="rules" :model="user" :inline="formInline" ref="userForm" label-width="auto">
            <el-row>
	            <el-col :span=12>
		            <el-form-item span="8" :label="$t('users.username')+`:`" prop='username' style=" margin-top: 20px">
				            <el-input v-model="user.username" :disabled="user.isExist()" :placeholder="$t('users.username')" maxlength=50></el-input>
		            </el-form-item>
	            </el-col>
	            <el-col :span=12>
		            <el-form-item span="8" :label="$t('users.name')+`:`" prop='name' style="margin-top: 20px">
				            <el-input v-model="user.name" :placeholder="$t('users.name')" maxlength=50></el-input>
		            </el-form-item>
	            </el-col>
            </el-row>
            <el-row>
	            <el-col :span=12>
		            <el-form-item span="8" :label="$t('users.idCard')+`:`" prop='idCard' style="margin-top: 20px">
			            <el-input v-model="user.idCard" :placeholder="$t('users.idCard')"></el-input>
		            </el-form-item>
	            </el-col>
	            <el-col :span=12>
		            <el-form-item span="8" :label="$t('users.email')+`:`" prop='email' style="margin-top: 20px">
			            <el-input v-model="user.email" :placeholder="$t('users.email')"></el-input>
		            </el-form-item>
	            </el-col>
            </el-row>
            <el-row>
	            <el-col :span=12>
		            <el-form-item span="8" :label="$t('users.mobilePhone')+`:`" prop='mobilePhone'
		                :rules="{validator: validatePhone, trigger: 'blur', required: enableSmsLogin}"
		                style="margin-top: 20px">
			            <el-input v-model="user.mobilePhone" :placeholder="$t('users.mobilePhone')"></el-input>
		            </el-form-item>
	            </el-col>
	            <el-col :span=12>
		            <el-form-item span="8" :label="$t('users.phone')+`:`" prop='phone' style="margin-top: 20px">
			            <el-input v-model="user.phone" :placeholder="$t('users.phone')"></el-input>
		            </el-form-item>
	            </el-col>
            </el-row>
            <el-row>
	            <el-col :span=24 align="right">
			            <el-button class="uams-user-detail-save" type="primary" :disabled="submitBtnDisable" @click="submitUser">{{ $t('users.save') }}</el-button>
			            <el-button @click="cancel">{{ $t('users.cancel') }}</el-button>
	            </el-col>
            </el-row>
        </el-form>
    </div>
</template>
<script>
import axios from "axios";
import store from "../js/store";
import validationRules from "../js/user_validation_rules";
import userProvider from "../js/user_provider";

export default {
    name: "user_detail",
    props: ['visible'],
    watch: {
        "visible": {
            immediate: true,
            handler: function (visible) {
                if (visible) {
                    this.getUserInfo();
                    this.submitBtnDisable = false;
                }
            }
        }
    },
    data: function () {
        return {
            enableSmsLogin: false,
            user: userProvider.createUser(),
            sharedState: store.state,
            rules: validationRules,
            submitBtnDisable: false,
	        formInline:(navigator.userAgent.indexOf("compatible") > -1 && navigator.userAgent.indexOf("MSIE") > 1 || navigator.userAgent.indexOf("rv:11.0") > -1)
        }
    },
    mounted () {
        this.getSmsConfig();
    },
    methods: {
        validatePhone (rule, value, callback) {
            if (this.enableSmsLogin && !value) {
                callback(new Error(i18n.t('users.validate.mobilePhone.empty')));
            }
            let regs = /^1[3456789]\d{9}$/;
            if (value && !regs.test(value)) {
                callback(new Error(i18n.t('users.validate.mobilePhone.correct')));
            }
            callback();
        },
        getSmsConfig () {
            axios.get("/UAMS/sms/enableSmsLogin").then(({ data }) => {
                this.enableSmsLogin = data;
            })
        },
        submitUser: function () {
            let $this = this;
            this.submitBtnDisable = true;
            $this.$refs['userForm'].validate((valid) => {
                if (!valid) {
                    $this.submitBtnDisable = false;
                    return;
                }
                userProvider.saveUser($this.user, function (err) {
                    if (err === null) {
                        $this.$message({
                            type: 'success',
                            message: !$this.user.isExist() ? $this.$t('users.addUserSuccess') : $this.$t('users.updateUserSuccess'),
                            center: true
                        });
                        $this.cancel();
                        $this.$emit('refresh', null);
                    } else {
                        $this.$message({
                            type: 'error',
                            message: err.response.data.message,
                            center: true
                        });
                        $this.submitBtnDisable = false;
                    }
                });
            })
        },
        cancel: function () {
            this.$refs['userForm'].resetFields();
            this.$emit('close', null);
        },
        getUserInfo: function () {
            let $this = this;
            const userId = this.sharedState.selectedUser.id;
            if (userId && userId !== "") {
                userProvider.requestUser(userId, function (err, user) {
                    if (err === null) {
                        $this.user = user;
                    }else{
                        $this.$message({
                            type: 'error',
                            message: err.message,
                            center: true
                        });
                        $this.cancel();
                    }
                })
            }else{
                this.user = userProvider.createUser();
            }
        }
    }
}
</script>
<style scoped>

.uams-user-detail-save {
    margin-left: 80px;
}

</style>