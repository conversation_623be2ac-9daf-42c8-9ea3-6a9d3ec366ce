<template>
    <div class="changePwdPage">
        <div>
            <div style="text-align: center;line-height: 40px;">{{newUserChangePwd}}</div>
            <div class="changePwd">
                <div class="jumpToPage"><div class="jumpToPage_back" @click="jumpToPage" ><i class="el-icon-arrow-left jumpToPage_back_img"></i></div>{{$t('app.change-password')}}</div>
                <el-form label-position="right" :model="ruleForm" status-icon :rules="rules" ref="ruleForm" label-width="auto" class="changePwdPageRuleForm">
                    <el-form-item :label="$t('changPassWord.oldPassword') + ':'" prop="oldPassword">
                        <el-input type="password" v-model="ruleForm.oldPassword" ref="oldPassword"></el-input>
                    </el-form-item>
                    <el-form-item :label="$t('changPassWord.newPassword') + ':'" prop="pass">
                        <el-input type="password" v-model="ruleForm.pass" autocomplete="off"></el-input>
                    </el-form-item>
                    <el-form-item :label="$t('changPassWord.confirmPassword') + ':'" prop="checkPass">
                        <el-input type="password" v-model="ruleForm.checkPass" autocomplete="off"></el-input>
                    </el-form-item>
                </el-form>
                <div class="changePwdPageFooter">
                    <div>
                        <el-button class="changePwdPageFooter_confirm" type="primary" @click="submitForm('ruleForm')">{{$t('changPassWord.confirm')}}</el-button>
                        <el-button class="changePwdPageFooter_cancel" @click="resetForm('ruleForm')">{{$t('changPassWord.cancel')}}</el-button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
    export default {
        data() {
            var validatePass = (rule, value, callback) => {
                if (value === '') {
                    callback(new Error(this.$t('changPassWord.ruleForm-newPassword')));
                } else {
                    var reg = /^(?![a-zA-Z]+$)(?![A-Z0-9]+$)(?![A-Z~!@#%^&*_.-]+$)(?![a-z0-9]+$)(?![a-z~!@#%^&*_.-]+$)(?![0-9~!@#%^&*_.-]+$)[a-zA-Z0-9~!@#%^&*_.-]{8,20}$/;
                    if (reg.test(value)) {
                        if (this.ruleForm.checkPass !== '') {
                            this.$refs.ruleForm.validateField('checkPass');
                        }
                        callback();
                    } else {
                        callback(new Error(this.$t('changPassWord.ruleForm-passWordRule')));
                    }
                    callback();
                }
            };
            var validatePass2 = (rule, value, callback) => {
                if (value === '') {
                    callback(new Error(this.$t('changPassWord.ruleForm-confirmPassword')));
                } else if (value !== this.ruleForm.pass) {
                    callback(new Error(this.$t('changPassWord.ruleForm-confirmPassword-error')));
                } else {
                    callback();
                }
            };
            return {
                ruleForm: {
                    pass: '',
                    checkPass: '',
                    oldPassword: ''
                },
                rules: {
                    oldPassword: [
                        { required: true, message: this.$t('changPassWord.ruleForm-oldPassword'), trigger: 'blur' }
                    ],
                    pass: [
                        { required: true, message: this.$t('changPassWord.ruleForm-newPassword'), trigger: 'blur' },
                        { validator: validatePass, trigger: 'blur' }
                    ],
                    checkPass: [
                        { required: true, message: this.$t('changPassWord.ruleForm-confirmPassword'), trigger: 'blur' },
                        { validator: validatePass2, trigger: 'blur' }
                    ]
                },
                newUserChangePwd: ""
            };
        },
        mounted(){
            var dom = document.getElementById("toChangePwd_newUser");
            // 是否是初始化重置密码
            if(dom){
                var newUserChangePwd = dom.innerText;
                if(newUserChangePwd){
                    this.newUserChangePwd = newUserChangePwd;
                }
            }
            var returnUrl = document.getElementById("toChangePwd_returnUrl");
            this.returnUrl = returnUrl.innerText;
        },
        methods: {
            /**
             * 返回
             */
            jumpToPage(){
                var returnUrl = "";
                // 初始化进入系统，修改密码
                if(this.newUserChangePwd){
                    returnUrl = window.location.protocol + "//" + window.location.host + "/UAMS/exit";
                }else{
                    if(this.returnUrl === ""){
                        // 退出登陆
                        returnUrl = window.location.protocol + "//" + window.location.host + "/UAMS";
                    }else{
                        returnUrl = window.location.protocol + "//" + window.location.host + "/UAMS/logout?returnUrl=" + this.returnUrl;
                    }
                }
                window.location.href = decodeURIComponent(returnUrl);
            },

            submitForm(formName) {
                var that = this;
                this.$refs[formName].validate((valid) => {
                    if (valid) {
                        console.log(`旧密码：${that.ruleForm.oldPassword}；新密码：${that.ruleForm.checkPass}`)
                        that.postPassWord(that.ruleForm.oldPassword, that.ruleForm.checkPass);
                    } else {
                        console.log('error submit!!');
                        return false;
                    }
                });
            },

            /**
             * 加密请求
             */
            async postPassWord(userOldPwd, userNewPwd){
                var that = this;
                // 获取Key
                var key = await this.encryption();
                console.log(userOldPwd, userNewPwd);
                // Wzw1234567
                // 加密密码
                userOldPwd= this.encrypt(userOldPwd, key);
                userNewPwd= this.encrypt(userNewPwd, key);
                this.$axios.post(`/UAMS/users/changePwd?userOldPwd=${encodeURIComponent(userOldPwd)}&userNewPwd=${encodeURIComponent(userNewPwd)}`).then((data)=>{
                    console.log(data);
                    /**
                     *  密码错误
                     */
                    if(data.data === 0){
                        that.$message({
                            message: that.$t('changPassWord.confirm-success'),
                            type: 'success'
                        });
                        var returnUrl = this.returnUrl;
                        if(returnUrl === "" || this.newUserChangePwdzw123456){
                            returnUrl = window.location.protocol + "//" + window.location.host + "/UAMS/exit";
                        }else{
                            if(decodeURIComponent(returnUrl).endsWith("/")){
                                returnUrl = returnUrl + "logout";
                            }else{
                                returnUrl = returnUrl + "/logout";
                            }
                        };
                        setTimeout(function () {
                            window.location.href = decodeURIComponent(returnUrl);
                        }, 2000);
                    }else{
                        that.$message({
                            message: that.$t('changPassWord.confirm-error'),
                            type: 'error'
                        });
                        that.resetForm();
                    };
                });
            },

            /**
             * 获取加密key
             */
            encryption(){
                var that = this;
                return new Promise((resolve, reject)=>{
                    that.$axios.get("/UAMS/getKey").then((data)=>{
                        resolve(data.data);
                    });
                });
            },

            /**
             * 重置form表单
             */
            resetForm() {
                this.$refs.ruleForm.resetFields();
            },

            /**
             * 通过key加密密码
             */
            encrypt(content, key){
                var sKey = CryptoJS.enc.Utf8.parse(key);
                var sContent = CryptoJS.enc.Utf8.parse(content);
                var encrypted = CryptoJS.AES.encrypt(sContent, sKey, {mode:CryptoJS.mode.ECB,padding: CryptoJS.pad.Pkcs7});
                return encrypted.toString();
            }
        }
    }
</script>
