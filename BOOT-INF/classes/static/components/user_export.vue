<template>
  <div>
    <p style="text-align: center; margin: 50px 0 20px">{{$t('users.exportUsers.exportUserInformation')}}</p>
    <div style="text-align: center;">
      <el-transfer
          :style="{textAlign: 'left', display: 'inline-block', width: '100%', height: transferHeight + 'px'}"
          v-model="value"
          filterable
          :titles="[this.$t('users.exportUsers.notSelectedUser'), this.$t('users.exportUsers.selectedUser')]"
          :button-texts="[this.$t('users.exportUsers.toTheLeft'), this.$t('users.exportUsers.toTheRight')]"
          :format="{
              noChecked: this.$t('users.exportUsers.selectAll'),
              hasChecked: this.$t('users.exportUsers.selectAll')
          }"
          :data="data">
        <span slot-scope="{ option }">{{ option.label }}</span>
        <el-button class="transfer-footer" slot="right-footer" type="primary" size="medium" @click="exportUserInfo()" :disabled="value.length <= 0">{{$t('users.export')}}</el-button>
      </el-transfer>
    </div>
  </div>

</template>

<style scoped>

.transfer-footer {
    margin-left: 20px;
    padding: 8px 14px;
}
::v-deep .el-transfer-panel {
    width: calc((100% - 300px) / 2);
    height: calc(100% - 50px);
}
::v-deep .el-transfer-panel > .el-transfer-panel__body {
    height: calc(100% - 40px);
}
::v-deep .el-transfer-panel > .el-transfer-panel__body.is-with-footer {
    height: calc(100% - 85px);
}
::v-deep .el-transfer-panel__body > .el-transfer-panel__list {
    height: calc(100% - 65px);
}
::v-deep .el-transfer-panel__body > .el-transfer-panel__filter.el-input {
    width: auto;
    display: block;
}
</style>

<script>
import axiosApi from "../js/axios_api";
import store from "../js/store.js";
import UserProvider from "../js/user_provider";

// todo transfer穿梭框样式调整 + 国际化样式调整
export default {
  name: "user_export",
  data() {
    return {
      users:[],
      data: [],
      value: [],
      sharedState: store.state
    };
  },
  computed: {
    transferHeight:function(){
      return this.sharedState.clientHeight - 200;
    },
  },

  async mounted () {
    await this.generateData();
  },

  methods: {

    sortUsers:function(users){
      let results = [];
      const that = this;
      if (!this.sharedState.currentUser.isAdmin) {
          results = _.filter(users, function (user) {
            return user.id !== "BEFE2FF1A20F4C419C8A94B7213C5219";
          });
      }else{
          results = _.cloneDeep(users);
      }
      //排序
      results = _.sortBy(results, "username");
      results = _.sortBy(results, function (item) {
        return item.id===that.sharedState.currentUser.id ? -1:1;
      });
      return results;
    },

    generateData (){
      UserProvider.requestUsers().then(res => {
        this.users = this.sortUsers(res);
        if (this.users){
          const data = [];
          for (let i = 0; i < this.users.length; i++) {
            let user = this.users[i];
            data.push({
              key: user.id,
              label: user.name,
              disabled: user.isAdmin
            });
          }
          this.data = data;
        }else{
          this.data = [];
        }
      })
    },
    async exportUserInfo () {
      const exportUserIdList  = this.value;
      axiosApi.exportFileRequest("post", "export/usersInfo", exportUserIdList, this.$t('users.exportUsers.UserInfoTable') + ".xls"
      ).then( res => {
        this.$message({
						type: 'success',
						message: this.$t('users.exportUsers.ExportUserInfoSuccess'),
						center: true
					});
      }).catch (error => {
        this.$message({
          type: 'error',
          message: error.response.data.message,
          center: true
        });
      });
    }
  }
};
</script>