<template>
    <el-table
        :data="groups"
        style="width: 100%"
        row-key="id"
        ref="multipleTable"
        @select="handleSelect"
        @selection-change="handleSelectChange"
        default-expand-all
        border
        lazy
        v-loading="loading"
        :emptyText="$t('users.emptyText')"
        :height="height"
        :cell-style="{height:'40px',padding:'2px 0px'}"
        :header-cell-style="{background:'#f5f5f5'}"
        @select-all="handleSelectAll"
        :tree-props="{children: 'children', hasChildren: 'hasChildren'}">
        <el-table-column
            type="selection"
            :selectable='canSelectSystemManageGroup'
            width="55">
        </el-table-column>
          <el-table-column prop="name" :label="$t('groups.context.tableName1')">
        </el-table-column>
        <el-table-column :label="$t('groups.context.tableName2')"  align='center' >
            <template slot-scope="slot">
                <div v-if="showOperation">
                  <el-tooltip class="item" effect="dark" :content="$t('groups.context.operation.edit.title')"
                              placement="top">
                    <el-button type="text" size="medium" icon="el-icon-edit" @click="openEditGroupDialog(slot.row)"
                               v-if='canOperate(slot.row)'></el-button>
                  </el-tooltip>
                  <el-tooltip class="item" effect="dark" :content="$t('groups.context.operation.add.title')"
                              placement="top">
                    <el-button type="text" size="medium" icon="el-icon-plus" @click="openAddGroupDialog(slot.row)"
                               v-if='canOperate(slot.row)'></el-button>
                  </el-tooltip>
                  <el-tooltip class="item" effect="dark" :content="$t('groups.context.operation.delete.title')"
                              placement="top">
                    <el-button type="text" size="medium" icon="el-icon-delete"
                               @click="openDeleteGroupDialog(slot.row)"
                               v-if='canOperate(slot.row)'></el-button>
                  </el-tooltip>
                </div>
                <div v-else>
                  <el-checkbox prop="isHavePower"  v-if ="slot.row.isHavePower" v-model="slot.row.isGroupManage"
                  @change = "edit(slot.row)" >设置为管理员</el-checkbox>
                </div>
            </template>
        </el-table-column>


    </el-table>
</template>

<script>
import store from "../js/store";
import GroupProvider from "../js/group_provider";
import CONSTANT from "../js/constant";
import _ from "lodash";

export default {
    props: ["showOperation", "height", "filterText", "canSelectAll", "selection"],
    name: "group_list",
    data: function () {
        return {
            sharedStore: store.state,
            originalGroups: [],
            loading: false
        }
    },
    watch: {
        "selection": {
            immediate: true,
            handler: function (newSelection) {
                this.resumeSelection();
            }
        }
    },
    computed: {
        groups: function (){

            if (!this.filterText || this.filterText === "") {
                let resultGroups = GroupProvider.adaptPidToChildren(this.originalGroups, this.sharedStore);
                window.setTimeout(this.resumeSelection, 0);
                return resultGroups;
            } else {
                let parentGroups = [];
                let searchGroups = [];
                searchGroups = GroupProvider.filter(this.originalGroups, this.filterText);
                const that = this;
                _.forIn(searchGroups, function (group) {
                    parentGroups = _.concat(parentGroups, GroupProvider.findAncestors(that.originalGroups, group));
                });
                searchGroups = _.concat(searchGroups, parentGroups);
                searchGroups = _.uniq(searchGroups);
                let resultGroups = GroupProvider.adaptPidToChildren(searchGroups, this.sharedStore);
                return resultGroups;
            }
        }
    },
    methods: {
        canSelectSystemManageGroup: function (row) {
            let  roleType= this.sharedStore.currentUser.roleType;
            if (roleType=== CONSTANT.ADMIN ){
              return true;
            } else if (roleType === window.CONSTANT.SYSTEM_ADMINISTRATOR ){
              return this.canSelectAll || !row.sysGroup;
            }else if(roleType === window.CONSTANT.USER_GROUP_ADMINISTRATOR ){
              if (row.isHavePower){
                return true;
              }else{
                return false;
              }
            }
            return this.canSelectAll || !row.sysGroup;
        },
        canOperate: function (row) {
            let  roleType= this.sharedStore.currentUser.roleType;
            // if(roleType === window.CONSTANT.USER_GROUP_ADMINISTRATOR ){
            //   if (row.isHavePower){
            //     return true;
            //   }else{
            //     return false;
            //   }
            // }
            if (row.sysGroup) {
                return false;
            } else {
                return this.sharedStore.currentUser.isAdmin || this.sharedStore.currentUser.isManager;
            }
            return false;
        },
        refresh: function () {
            const that = this;
            //获取所有的组
            this.loading = true;
            GroupProvider.requestGroups().then(function (groups) {
                that.originalGroups = groups;
            }).catch(function (error) {
                console.log(error);
            }).finally(function () {
                that.loading = false;
            });
        },
        resumeSelection() {
            if (!this.$refs.multipleTable) {
                // 还未渲染出来
                return;
            }
            this.$refs.multipleTable.clearSelection();
            if (_.isArray(this.selection) && this.selection.length > 0 && _.isArray(this.groups) && this.groups.length > 0) {
                // 才去选中
                const selectedGroupIds = _.map(this.selection, "id");
                let selectedGroups = [];
                const that = this;
                _.forIn(this.groups, function (group) {
                    if (selectedGroupIds.includes(group.id)) {
                        selectedGroups.push(group);
                    }
                    selectedGroups = _.concat(selectedGroups, that.collectDescendants(group, selectedGroupIds));
                });
                _.forIn(selectedGroups, function (group) {
	                if (selectedGroupIds.includes(group.id)) {
		                that.$refs.multipleTable.toggleRowSelection(group, true);
	                }
                });
            }
        },
        collectDescendants: function (group, filterGroupIds) {
            let results = [];
            if (filterGroupIds === undefined) {
                results = group.children;
            }
            const that = this;
            _.forIn(group.children, function (childGroup) {
                if (_.isArray(filterGroupIds) && filterGroupIds.includes(childGroup.id)) {
                    results.push(childGroup);
                }
                results = _.concat(results, that.collectDescendants(childGroup));
            });
            return results;
        },
        handleSelectAll: function (selection) {
            // TODO 先关闭，开始做全选后再开启
            return;
            const that = this;
            let isClear = true;
            _.forIn(selection, function (group) {
                if (that.groups.includes(group)) {
                    // 选中的group中包含根部的group，所以认为是全选
                    isClear = false;
                    return false;
                }
            });
            if (isClear) {
                this.$refs.multipleTable.clearSelection();
            } else {
                //全选
                let descendants = []
                _.forIn(this.groups, function (group) {
                    descendants = _.concat(descendants, that.collectDescendants(group));
                });
                descendants = _.uniq(_.compact(descendants));
                const toggleGroups = _.differenceWith(descendants, selection, _.isEqual)
                window.setTimeout(function () {
                    _.forIn(toggleGroups, function (group) {
                        that.$refs.multipleTable.toggleRowSelection(group, true);
                    });
                }, 10)

            }
        },
        handleSelect: function (selection, row) {
            if (selection.findIndex(group => group.id === row.id) === -1) {
                row.isGroupManage = false;
            }
            // TODO 先关闭，
            this.$emit("select", selection);
            return;
            let display = false;
            if (selection.includes(row)) {
                display = true;
            }
            const that = this;
            const descendants = this.collectDescendants(row);
            window.setTimeout(function () {
                // 滞后选中，以防止选中状态不正确
                _.forIn(descendants, function (group) {
                    that.$refs.multipleTable.toggleRowSelection(group, display);
                });
            }, 10)
        },
        handleSelectChange: function (selection) {
            this.$emit("change", selection);
        },
        openDeleteGroupDialog: function (row) {
            this.$emit("delete", row);
        },
        openAddGroupDialog: function (row) {
            this.$emit("add", row);
        },
        openEditGroupDialog: function (row) {
            this.$emit("edit", row);
        },
        edit: function (row) {
            if(row.isGroupManage){
              this.$refs.multipleTable.toggleRowSelection(row,true);
            }
        }
    },
    created: function () {
        this.refresh();
    },
}
</script>

<style scoped>

</style>