<template>
    <div>
        <el-row :gutter="20" style="margin-bottom:20px">
            <el-col :xs="12" :sm="9" style="height: 40px;">
                <el-button type="primary" plain @click="openAddGroupDialog"
                           v-if="sharedStore.currentUser.isManager || sharedStore.currentUser.isAdmin">
                    {{ $t("groups.head.addButton.buttonName") }}
                </el-button>
                <el-button type="primary" plain @click="openDeleteGroupsDialog"
                           v-if="sharedStore.currentUser.isManager || sharedStore.currentUser.isAdmin">
                    {{ $t("groups.head.deleteButton.buttonName") }}
                </el-button>
            </el-col>
            <el-col :xs="12" :sm="{span:10,offset:5}" :md="{span:8,offset: 7}" :lg="{span:6,offset:9}">
                <el-input :placeholder="$t('groups.head.searchPlaceholder')" v-model="searchVal" style="width: 100%;"
                >
                    <el-button slot="append" icon="el-icon-search" @click=""></el-button>
                </el-input>
            </el-col>
        </el-row>
        <GroupList ref="groupList" :showOperation="true" :height="tableHeight"
                   :filterText="searchVal"
                   @change="handleSelect"
                   @add="openAddGroupDialog" @delete="openDeleteGroupDialog" @edit="openEditGroupDialog"
        ></GroupList>
        <el-dialog
            :title="editorDialogTitle"
            :close-on-click-modal="false"
            :visible.sync="dialogVisible"
            :width="dialogWidth"
            :before-close="handleClose">
            <GroupEditor :group="group" @close="handleClose" @success="handleSuccess"></GroupEditor>
        </el-dialog>
    </div>
</template>

<script>
import GroupEditor from "./group_editor.vue";
import GroupList from "./group_list.vue";
import axios from "axios";
import store from "../js/store";
import GroupProvider from "../js/group_provider";

export default {
    name: "group_management",
    computed: {
        editorDialogTitle: function () {
            if (!this.group.isNew()) {
                // 编辑模式
                return this.$t('groups.context.operation.edit.dialog.title');
            }
            return this.$t('groups.addGroupDialog.title');
        },
        tableHeight:function(){
            return this.sharedStore.clientHeight - 100;
        }
    },
	mounted() {
        this.setDialogWidth();
	},
	data() {
        return {
            sharedStore: store.state,
            searchVal: "",
            //用户保存原始用户组数据
            dialogVisible: false,
            group: GroupProvider.createGroup(),
            selectedGroups:[],
	        dialogWidth:""
        }
    },
    methods: {
    	setDialogWidth:function(){
    		let $this = this;
		    if(i18n.locale === "zh_CN"){
			    $this.dialogWidth= "400px";
		    }else if(i18n.locale === "en_US"){
			    $this.dialogWidth= "430px";
		    }else{
			    $this.dialogWidth= "480px";
		    }
	    },
        openEditGroupDialog(row) {
            this.group = GroupProvider.createGroup(row);
            this.dialogVisible = true;
        },
        openAddGroupDialog(row) {
            this.dialogVisible = true;
            this.group = GroupProvider.createGroup();
            const parent = GroupProvider.createGroup(row);
            if(!parent.isNew()){
                this.group.setParent(parent);
            }
        },
        handleClose() {
            this.dialogVisible = false;
        },
        handleSuccess() {
            const parent = this.group.parent;
            this.group = GroupProvider.createGroup();
            if(parent!==null){
                this.group.setParent(parent);
            }
            this.$refs["groupList"].refresh();
        },
        handleSelect(selection){
            this.selectedGroups = selection;
        },
        openDeleteGroupDialog(group) {
            let $this = this;
            let groupIds = [];
            if (!group.id || group.id === "") {
                this.$message.error({
                    message: $this.$t('groups.context.operation.delete.emptyDialog.context'),
                    center: true
                });
                return;
            } else {
                groupIds.push(group.id);
            }
            this.deleteGroups(groupIds);
        },
        openDeleteGroupsDialog(){
            let groupIds = [];
            this.selectedGroups.forEach(item => {
                groupIds.push(item.id);
            });
            if (groupIds.length === 0) {
                this.$message.warning({
                    message: this.$t('groups.head.deleteButton.warnMessage.context'),
                    center: true
                });
                return;
            }
            this.deleteGroups(groupIds);
        },
        /**删除用户组*/
        deleteGroups(ids = []) {
            const $this = this;
            this.$confirm($this.$t('groups.context.operation.delete.dialog.context'), $this.$t('groups.context.operation.delete.dialog.Hint'), {
                confirmButtonText: $this.$t('groups.context.operation.delete.dialog.confirm'),
                cancelButtonText: $this.$t('groups.context.operation.delete.dialog.cancel'),
                type: 'warning'
            }).then(() => {
                if (ids.length === 0) {
                    $this.$message.warning($this.$t('groups.head.deleteButton.warnMessage.context'));
                } else {
                    axios.delete("groups", {data: ids}).then(function (res) {
                        if (res.status === 200) {
                            $this.$message.success({
                                message: $this.$t('groups.head.deleteButton.successMessage.context'),
                                center: true
                            });
                            $this.$refs["groupList"].refresh();
                        }
                    }).catch(function (error) {
                        $this.$message.error({message: error.response.data.message, center: true});
                    })
                }
            });
        },

    },
    components: {
        GroupEditor,
        GroupList
    }
}
</script>

<style scoped>
.el-table__header-wrapper .cell {
    display: none
}

.el-table__header .cell {
    display: none !important
}
</style>