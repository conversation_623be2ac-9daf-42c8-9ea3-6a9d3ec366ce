import Vue from 'vue';
import Element<PERSON> from 'element-ui';
import VueRouter from 'vue-router'
import VueI18n from 'vue-i18n'

import "./js/axios_config";
import "./js/window_resize";
import store from "./js/store";
import userProvider from "./js/user_provider";
import axios from "axios";

Vue.use(ElementUI);
Vue.use(VueRouter);
Vue.use(VueI18n);

import App from './components/App.vue'
import userListComponent from "./components/user_management.vue";
import groupListComponent from "./components/group_management.vue";
import userImport from "./components/user_import.vue";
import userExport from "./components/user_export.vue";

import 'element-ui/lib/theme-chalk/index.css';
import "./fonts/iconfont.css";
import "./css/user/main.css";


const i18n = new VueI18n({
    locale: localStorage.lang,
    messages: {
        'en_US': require('./languages/en.json'),
        'zh_CN': require('./languages/zh.json'),
        'ru_RU': require('./languages/ru.json')
    }
});
window.i18n = i18n;
Vue.config.productionTip = false;
//group的添加编辑界面，回车键会导致页面刷新，这里暂时全局禁用回车
document.onkeydown = function() {
    if(window.event.keyCode===13) {
        window.event.returnValue=false;
    }
}

async function getSystemLang(){
    let lang = 'zh_CN';
    let url = 'getLang?t=' + (new Date()).valueOf();
    if(localStorage.lang === undefined || localStorage.lang===null){
        await axios.get(url).then(function(response){
            if(response.status === 200 ){
                lang = response.data;
            }
        });
        localStorage.setItem("lang",lang);
    }else{
        lang = localStorage.lang;
    }
    return lang;
}

let routes = [
    {path: "/", redirect: "/user"},
    {path: "/user", name: "用户管理", component: userListComponent},
    {path: "/group", name: "用户组管理", component: groupListComponent},
    {path: "/user/import", name: "用户导入",component: userImport},
    {path: "/user/export", name: "userExport",component: userExport}
];

let router = new VueRouter({
    routes: routes
});

axios.all([getSystemLang(), userProvider.requestCurrentUser()])
    .then(axios.spread(function (lang, currentUser) {
        i18n.locale = lang;
        if (currentUser !== undefined) {
            store.setCurrentUser(currentUser);
        }
        new Vue({
            i18n,
            router,
            render: h => h(App)
        }).$mount('#app');
    }));
