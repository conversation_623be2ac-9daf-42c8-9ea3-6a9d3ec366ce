<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>

<title>无权访问</title>
<meta http-equiv="pragma" content="no-cache">
<meta http-equiv="cache-control" content="no-cache">
<meta http-equiv="expires" content="0">
<meta http-equiv="keywords" content="keyword1,keyword2,keyword3">
<meta http-equiv="description" content="This is my page">

<link rel="stylesheet" type="text/css"
	href="${request.contextPath}/static/css/user/authority.css" />
<script type="text/javascript"
	src="${request.contextPath}/static/libs/jquery/jquery.min.js"></script>
<script type="text/javascript">
	var c;
	var t;
	$(function(){
		setTimeout("timedCount()", 1000);
	});	
	function timedCount() {
		c = $("#time").html();
		if(c > 0){
			$("#time").html(c-1);
		}else{
			history.back();
		}
		t = setTimeout("timedCount()", 1000);
	}	
</script>

</head>

<body>
	<div id="AuthorityPrompt">
		<label>401</label>
		<div id="AuthorityContent">
			<p>您没有访问权限~~</p>
			<p>
				<span class="PromptReturnTime"><span id="time">5</span>秒后</span><a
					onclick="javascript:history.back()">返回上一页</a>
			</p>
		</div>
	</div>
</body>
</html>
