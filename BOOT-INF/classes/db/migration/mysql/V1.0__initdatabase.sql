CREATE TABLE `SYS_USER` (
`ID` varchar(32) NOT NULL,
`PASSWORD` varchar(100) DEFAULT NULL,
`USER<PERSON><PERSON>` varchar(50) DEFAULT NULL,
`NAME` varchar(50) DEFAULT NULL,
`EMAIL` varchar(100) DEFAULT NULL,
`PHONE` varchar(20) DEFAULT NULL,
`MOBILE_PHONE` varchar(30) DEFAULT NULL,
`CHANGE_PWD_TIME` datetime DEFAULT NULL,
`ID_CARD` varchar(18) DEFAULT NULL,
`CREATE_BY` varchar(32) DEFAULT NULL,
PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

INSERT INTO SYS_USER
(ID, PASSWORD, USERNAME, NAME, EMAIL, PHONE, MOBILE_PHONE, CHANGE_PWD_TIME, ID_CARD, CREATE_BY)
VALUES('BEFE2FF1A20F4C419C8A94B7213C5219', '21232f297a57a5a743894a0e4a801fc3', 'admin', '超级管理员', NULL, NULL, NULL, NULL, NULL, NULL);

CREATE TABLE `SYS_GROUP` (
 `ID` varchar(32) NOT NULL,
 `PARENT_ID` varchar(32) DEFAULT NULL,
 `NAME` varchar(200) DEFAULT NULL,
 `IS_SYS_GROUP` decimal(65,30) DEFAULT NULL,
 PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户组';

INSERT INTO SYS_GROUP
(ID, PARENT_ID, NAME, IS_SYS_GROUP)
VALUES('BEFE2FF1A20F4C419C8A94B7213C5218', NULL, '系统管理组', 1);

CREATE TABLE `RELATION` (
`ID` varchar(32) DEFAULT NULL,
`USER_ID` varchar(32) DEFAULT NULL,
`GROUP_ID` varchar(32) DEFAULT NULL,
`IS_GROUP_MANAGE` varchar(32) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

INSERT INTO RELATION
(ID, USER_ID, GROUP_ID, IS_GROUP_MANAGE)
VALUES('BEFE3FF1A20F4C419C8A94B7213C5217', 'BEFE2FF1A20F4C419C8A94B7213C5219', 'BEFE2FF1A20F4C419C8A94B7213C5218', 'false');

CREATE TABLE `EVENT` (
`ID` varchar(32) NOT NULL,
`USER_ID` varchar(32) DEFAULT NULL,
`GROUP_ID` text,
`IP` varchar(255) DEFAULT NULL,
`EVENT_TIME` datetime DEFAULT NULL,
`EVENT_TYPE` varchar(255) DEFAULT NULL,
PRIMARY KEY (`ID`),
KEY `USER_ID` (`USER_ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
