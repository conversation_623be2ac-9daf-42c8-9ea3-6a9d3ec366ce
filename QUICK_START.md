# UAMS 快速开始指南

## 🎉 恭喜！反编译已完成

您的UAMS项目已经成功从JAR包还原为完整的Maven项目！

## 📁 当前项目结构

```
UAMS/
├── src/main/java/                    # ✅ Java源代码 (已反编译)
│   ├── com/fulongtech/uams/         # 主要业务代码
│   └── io/                          # 启动器相关代码
├── src/main/resources/              # ✅ 配置和资源文件
│   ├── static/                      # 前端静态资源
│   ├── templates/                   # FreeMarker模板
│   ├── db/migration/                # 数据库迁移脚本
│   └── application.yml              # 主配置文件
├── pom.xml                          # ✅ Maven项目配置
├── README.md                        # 项目说明文档
├── RESTORATION_STATUS.md            # 还原状态报告
└── application-example.yml          # 配置示例文件
```

## 🚀 下一步操作

### 1. 安装必要工具

#### 安装Maven
```bash
# Windows (使用Chocolatey)
choco install maven

# 或者手动下载安装
# https://maven.apache.org/download.cgi
```

#### 验证安装
```bash
java -version    # 应该显示 Java 1.8+
mvn -version     # 应该显示 Maven 3.6+
```

### 2. 配置数据库和中间件

#### 复制配置文件
```bash
copy application-example.yml application-local.yml
```

#### 编辑 application-local.yml
```yaml
uams:
  datasource:
    type: mysql  # 或 oracle, sqlserver
    host: localhost
    port: 3306
    serviceName: uams
    username: your_username
    password: your_password
  
  redis:
    host: localhost
    port: 6379
    password: 
    database: 0
  
  rabbitmq:
    host: localhost
    port: 5672
    username: guest
    password: guest
```

### 3. 编译和运行

#### 编译项目
```bash
mvn clean compile
```

#### 如果遇到编译错误
反编译的代码可能需要小幅调整，常见问题：
- 导入语句可能需要调整
- 某些变量名可能需要修复
- 泛型声明可能需要完善

#### 运行项目
```bash
mvn spring-boot:run
```

#### 或者打包后运行
```bash
mvn clean package
java -jar target/UAMS-2.5.1.jar
```

### 4. 访问应用

- **应用地址**: http://localhost:8080
- **管理界面**: http://localhost:8080/uams

## 🔧 可能遇到的问题

### 编译错误
1. **导入错误**: 检查import语句，可能需要手动调整
2. **依赖缺失**: 检查pom.xml中的依赖版本
3. **语法错误**: 反编译可能产生的语法问题

### 运行时错误
1. **数据库连接**: 确保数据库服务正在运行且配置正确
2. **Redis连接**: 确保Redis服务正在运行
3. **RabbitMQ连接**: 确保RabbitMQ服务正在运行

### 解决方案
1. 查看日志文件: `logs/uams.log`
2. 检查配置文件: `application-local.yml`
3. 验证服务状态: 数据库、Redis、RabbitMQ

## 📚 项目特性

- **用户管理**: 用户增删改查、权限管理
- **组织管理**: 用户组管理、权限分配
- **安全认证**: Shiro安全框架、JWT令牌
- **数据导入导出**: Excel文件处理
- **多数据库支持**: MySQL、Oracle、SQL Server
- **缓存支持**: Redis缓存
- **消息队列**: RabbitMQ支持
- **任务调度**: Quartz定时任务

## 🎯 开发建议

1. **使用IDE**: 推荐IntelliJ IDEA或Eclipse
2. **代码格式化**: 反编译的代码可能需要重新格式化
3. **添加注释**: 为关键业务逻辑添加注释
4. **单元测试**: 为重要功能编写测试用例
5. **日志配置**: 根据需要调整日志级别

## 📞 技术支持

如果遇到问题：
1. 查看 `README.md` 获取详细信息
2. 查看 `RESTORATION_STATUS.md` 了解还原状态
3. 检查反编译的代码质量
4. 参考Spring Boot和相关框架文档

---

**🎉 恭喜您成功还原了UAMS项目！现在可以开始开发和部署了！**
