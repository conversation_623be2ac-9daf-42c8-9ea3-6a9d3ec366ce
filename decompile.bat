@echo off
echo ========================================
echo UAMS Java源代码反编译脚本
echo ========================================
echo.

REM 检查Java环境
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到Java环境，请确保Java已正确安装并配置PATH
    pause
    exit /b 1
)

echo 1. 检查反编译工具...

REM 检查是否存在CFR反编译工具
if not exist "cfr.jar" (
    echo 未找到CFR反编译工具，正在下载...
    echo 请手动下载CFR反编译工具到当前目录:
    echo https://github.com/leibnitz27/cfr/releases/latest/download/cfr-0.152.jar
    echo 并重命名为 cfr.jar
    echo.
    echo 或者使用其他反编译工具:
    echo - JD-GUI: http://java-decompiler.github.io/
    echo - Fernflower: 使用IntelliJ IDEA打开.class文件
    echo.
    pause
    exit /b 1
)

echo 2. 开始反编译Java源代码...

REM 创建输出目录
if not exist "src\main\java" mkdir "src\main\java"

REM 使用CFR反编译
echo 正在反编译com.fulongtech包...
java -jar cfr.jar --outputdir "src\main\java" "BOOT-INF\classes\com\fulongtech"

echo 正在反编译io包...
java -jar cfr.jar --outputdir "src\main\java" "BOOT-INF\classes\io"

echo 3. 复制MyBatis映射文件...
REM 查找并复制XML映射文件
if exist "BOOT-INF\classes\com\fulongtech\uams\mapper\*.xml" (
    if not exist "src\main\resources\com\fulongtech\uams\mapper" mkdir "src\main\resources\com\fulongtech\uams\mapper"
    copy "BOOT-INF\classes\com\fulongtech\uams\mapper\*.xml" "src\main\resources\com\fulongtech\uams\mapper\"
)

echo 4. 清理和整理...
REM 删除临时文件
del /q "src\main\java\.gitkeep" 2>nul
del /q "src\main\resources\.gitkeep" 2>nul
del /q "src\test\java\.gitkeep" 2>nul

echo.
echo ========================================
echo 反编译完成！
echo ========================================
echo.
echo 下一步操作:
echo 1. 检查反编译的Java代码是否正确
echo 2. 修复可能的编译错误
echo 3. 配置数据库连接参数
echo 4. 运行 mvn clean compile 测试编译
echo 5. 运行 mvn spring-boot:run 启动应用
echo.
pause
