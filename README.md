# UAMS - User Account Management System

## 项目概述

UAMS (User Account Management System) 是一个基于Spring Boot 1.5.6的用户账户管理系统。

### 版本信息
- **版本**: 2.5.1
- **Spring Boot**: 1.5.6.RELEASE
- **Java**: 1.8
- **构建工具**: Maven

## 技术栈

### 后端框架
- Spring Boot 1.5.6
- Spring MVC
- Spring AOP
- MyBatis Plus 3.3.2
- Apache Shiro 1.7.1 (安全框架)

### 数据库支持
- MySQL 8.0.28
- Oracle 11g
- SQL Server 2017

### 缓存和消息队列
- Redis (缓存和Session存储)
- RabbitMQ (消息队列)

### 前端技术
- FreeMarker (模板引擎)
- JavaScript/jQuery
- CSS/HTML

### 工具库
- Hutool 5.8.3 (Java工具库)
- FastJSON 1.2.83 (JSON处理)
- Apache POI 3.9 (Excel处理)
- Quartz 2.3.2 (任务调度)
- JWT (JSON Web Token)
- Jasypt (配置加密)

## 项目结构

```
UAMS/
├── src/
│   ├── main/
│   │   ├── java/
│   │   │   └── com/fulongtech/uams/
│   │   │       ├── application/     # 应用启动类
│   │   │       ├── config/          # 配置类
│   │   │       ├── controller/      # 控制器
│   │   │       ├── service/         # 服务层
│   │   │       ├── mapper/          # 数据访问层
│   │   │       ├── model/           # 实体类
│   │   │       ├── util/            # 工具类
│   │   │       ├── shiro/           # Shiro安全配置
│   │   │       ├── aspect/          # AOP切面
│   │   │       ├── filter/          # 过滤器
│   │   │       ├── exception/       # 异常处理
│   │   │       └── vo/              # 视图对象
│   │   └── resources/
│   │       ├── static/              # 静态资源
│   │       ├── templates/           # FreeMarker模板
│   │       ├── db/migration/        # 数据库迁移脚本
│   │       └── application.yml      # 应用配置
│   └── test/
└── pom.xml
```

## 还原说明

本项目是从JAR包反编译还原的，包含以下内容：

### 已还原的内容
1. ✅ Maven项目结构 (pom.xml)
2. ✅ 应用配置文件 (application.yml等)
3. ✅ 静态资源文件 (CSS, JS, 图片等)
4. ✅ FreeMarker模板文件
5. ✅ 数据库迁移脚本
6. ✅ 基础包结构

### 需要手动反编译的内容
由于技术限制，以下内容需要使用专业的Java反编译工具进行还原：

1. 🔄 Java源代码文件 (.class → .java)
2. 🔄 MyBatis XML映射文件
3. 🔄 具体的业务逻辑实现

### 推荐的反编译工具

1. **JD-GUI** - 图形界面反编译工具
   - 下载地址: http://java-decompiler.github.io/
   - 使用方法: 直接打开.class文件或JAR包

2. **CFR** - 命令行反编译工具
   - 下载地址: https://github.com/leibnitz27/cfr
   - 使用方法: `java -jar cfr.jar --outputdir src/main/java BOOT-INF/classes/`

3. **Fernflower** - IntelliJ IDEA内置反编译器
   - 在IDEA中直接查看.class文件

## 配置说明

### 数据库配置
项目支持多种数据库，在`application.yml`中配置：
- MySQL: `mysql-url`, `mysql-driver-class-name`
- Oracle: `oracle-url`, `oracle-driver-class-name`  
- SQL Server: `sqlserver-url`, `sqlserver-driver-class-name`

### Redis配置
```yaml
spring:
  redis:
    host: ${uams.redis.host}
    port: ${uams.redis.port}
    password: ${uams.redis.password}
    database: ${uams.redis.database}
```

### RabbitMQ配置
```yaml
spring:
  rabbitmq:
    host: ${uams.rabbitmq.host}
    port: ${uams.rabbitmq.port}
    username: ${uams.rabbitmq.username}
    password: ${uams.rabbitmq.password}
```

## 构建和运行

### 前置条件
- JDK 1.8+
- Maven 3.6+
- MySQL/Oracle/SQL Server数据库
- Redis服务器
- RabbitMQ服务器

### 构建项目
```bash
mvn clean compile
```

### 运行项目
```bash
mvn spring-boot:run
```

### 打包项目
```bash
mvn clean package
```

## 下一步工作

1. 使用反编译工具还原所有Java源代码
2. 检查和修复反编译后的代码
3. 配置数据库连接
4. 配置Redis和RabbitMQ连接
5. 运行和测试应用

## 注意事项

- 反编译的代码可能需要手动调整和优化
- 某些复杂的逻辑可能在反编译过程中丢失注释和变量名
- 建议在还原后进行充分的测试
- 确保所有外部依赖服务正常运行

## 联系信息

- 开发商: Fulongtech (福隆科技)
- 版本: 2.5.1
- 构建时间: 基于JAR包信息推断为2022年10月
